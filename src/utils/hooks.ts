import React, { useRef, useEffect } from "react";

export const useIntersectionObserver = (
  callback: IntersectionObserverCallback,
  componentRef?: any,
  options?: IntersectionObserverInit | undefined
) => {
  const observer = useRef<IntersectionObserver>();

  useEffect(() => {
    observer.current = new IntersectionObserver(callback, options);
    observer.current.observe(componentRef.current);
    return () => {
      observer.current?.disconnect();
    };
  }, []);

  return observer;
};
