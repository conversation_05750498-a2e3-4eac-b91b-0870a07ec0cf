.button {
  @apply font-medium rounded-lg text-sm px-5 py-2.5 mx-1 mb-2  focus:outline-none;
}
.active {
  @apply bg-GTI-BLUE-default text-white hover:bg-blue-800;
}
.not-active {
  @apply bg-white text-GTI-BLUE-default border-2 border-slate-200;
}

.product-card-main {
  @apply flex flex-col justify-start sm:m-10 duration-150 border md:pb-2 shadow-lg hover:shadow-lg hover:shadow-GTI-BLUE-default space-y-1 rounded-xl cursor-pointer;
}

.product-list-main {
  @apply grid w-full md:px-10 mx-auto lg:gap-x-5 gap-x-10 gap-y-10 lg:grid-cols-3 sm:grid-cols-2 grid-cols-1;
}

.product-card-img {
  @apply flex w-full duration-150;
}

.product-card-title {
  @apply flex text-left px-4 space-y-1;
}

.product-card-button {
  @apply w-full flex flex-row justify-center items-center;
}

.product-single-main {
  @apply flex flex-col lg:px-20 px-10 w-full py-5 h-full space-y-5 items-center;
}
.product-single-banner {
  @apply flex w-full shadow shadow-slate-500 justify-items-start  lg:h-96  md:h-48 sm:h-24 rounded object-cover relative;
}
.product-single-parent-details {
  @apply flex flex-col w-full relative items-center;
}
.product-single-details {
  @apply flex flex-col absolute py-5 w-4/5 px-20 lg:-translate-y-20 -translate-y-10 bg-white  shadow-lg rounded;
}
.product-single-group {
  @apply px-10 py-5 w-full;
}
.product-single-title {
  @apply text-lg font-roboto font-semibold;
}
.product-single-data {
  @apply font-roboto text-GTI-BLUE-default;
}

.product-modal-main {
  @apply z-10 top-10 flex flex-col w-3/5 h-fit items-center space-y-5 bg-white duration-200 ease-in-out border border-gray-300 rounded justify-center p-10;
}

/* Modern UI Components */
.modern-dropdown-button {
  @apply relative w-48 bg-white border border-gray-300 rounded-lg px-4 py-2.5 text-left text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default transition-all duration-200 cursor-pointer flex items-center justify-between;
}

.modern-dropdown-content {
  @apply absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-60 overflow-auto;
}

.modern-dropdown-item {
  @apply w-full px-4 py-2.5 text-left text-sm text-gray-700 hover:bg-gray-50 hover:text-GTI-BLUE-default transition-colors duration-150 flex items-center;
}

.modern-search-input {
  @apply w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default transition-all duration-200 bg-white shadow-sm;
}

/* Layout Fixes */
.max-w-none {
  max-width: none !important;
}

/* Full Width Container */
.full-width-container {
  width: 100%;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}

/* Ensure proper spacing and centering */
.container-spacing {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-spacing {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-spacing {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Premium Technology Card Styles */
.premium-tech-card {
  @apply bg-white rounded-3xl shadow-lg border border-gray-100/50 hover:shadow-2xl hover:shadow-blue-500/20 flex flex-col overflow-hidden backdrop-blur-sm;
  min-height: 500px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-tech-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

.modern-product-grid {
  @apply grid gap-8;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
}

/* Modern Product Card Styles - Legacy Support */
.modern-product-card {
  @apply bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300 transform hover:-translate-y-1 flex flex-col overflow-hidden;
  min-height: 400px;
}

/* Enhanced Card Styles - Legacy Support */
.product-card-main {
  @apply flex flex-col justify-start duration-300 border border-gray-200 shadow-sm hover:shadow-xl hover:shadow-GTI-BLUE-default/20 space-y-1 rounded-2xl cursor-pointer bg-white transform hover:-translate-y-1 transition-all;
  width: 100%;
}

.product-card-img {
  @apply flex w-full duration-300 overflow-hidden rounded-t-2xl;
}

.product-card-img img {
  @apply transition-transform duration-300 hover:scale-105;
}

.product-list-main {
  @apply grid w-full gap-6 lg:grid-cols-3 md:grid-cols-2 grid-cols-1 py-8;
  max-width: 100%;
  margin: 0 auto;
}

/* Line Clamp Utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Modern Button Styles */
.modern-dropdown-button {
  @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.modern-create-button {
  @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

.modern-edit-button {
  @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

.modern-pagination-button {
  @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

/* Animation Enhancements */
/* Advanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-slideInDown {
  animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes bounce-subtle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s ease-in-out infinite;
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8),
      0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Mobile-First Responsive Design */
@media (max-width: 640px) {
  .modern-dropdown-button {
    @apply w-full text-base py-3;
  }

  .modern-product-grid {
    @apply grid-cols-1 gap-6;
    grid-template-columns: 1fr;
  }

  .premium-tech-card {
    min-height: 450px;
    transform: none !important;
  }

  .premium-tech-card:hover {
    transform: translateY(-4px) !important;
  }

  .modern-product-card {
    @apply hover:transform-none;
    min-height: 350px;
  }

  .product-list-main {
    @apply px-4 gap-4;
  }

  .product-card-main {
    @apply mx-0 hover:transform-none;
  }

  .modern-search-input {
    @apply text-base py-3;
  }

  /* Touch-friendly buttons */
  .product-card-main {
    @apply min-h-[120px];
  }

  /* Larger tap targets */
  .modern-dropdown-item {
    @apply py-4 text-base;
  }

  /* Disable complex animations on mobile */
  .premium-tech-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
}

@media (max-width: 768px) {
  .modern-product-grid {
    @apply grid-cols-1 gap-4;
    grid-template-columns: 1fr;
  }

  .product-list-main {
    @apply grid-cols-1 gap-4;
    padding: 0 1rem;
  }

  .product-card-main {
    @apply shadow-md;
  }

  .modern-product-card {
    min-height: 380px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .modern-product-grid {
    @apply grid-cols-2 gap-5;
    grid-template-columns: repeat(2, 1fr);
  }

  .product-list-main {
    @apply grid-cols-2 gap-5;
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .modern-product-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

/* Modern Visual Enhancements */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
}

.gradient-border {
  position: relative;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(45deg, #3b82f6, #8b5cf6) border-box;
  border: 2px solid transparent;
}

/* Status Badge Animations */
.status-badge {
  @apply transition-all duration-200 hover:scale-105;
}

/* Card Hover Effects */
.modern-product-card:hover .status-badge {
  @apply scale-105;
}

/* Loading States */
.skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.skeleton-text {
  @apply h-4 bg-gray-200 rounded animate-pulse;
}

.skeleton-avatar {
  @apply w-12 h-12 bg-gray-200 rounded-full animate-pulse;
}

/* Focus States */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Smooth Transitions */
.smooth-transition {
  @apply transition-all duration-300 ease-in-out;
}

/* Modern Shadows */
.shadow-modern {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-modern-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-modern-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Dropdown Z-Index Fix */
.dropdown-overlay {
  position: absolute !important;
  z-index: 99999 !important;
  background: white !important;
  border-radius: 1rem !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  border: 1px solid rgba(229, 231, 235, 1) !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
}

/* Ensure parent containers don't clip dropdowns */
.controls-container {
  overflow: visible !important;
  position: relative;
  z-index: 100;
}

.filter-container {
  position: relative;
  z-index: 10000;
  overflow: visible !important;
}

/* Force visibility for dropdown */
.dropdown-overlay * {
  z-index: inherit !important;
}

/* Ensure main content doesn't interfere */
.modern-product-grid {
  position: relative;
  z-index: 1;
}

.premium-tech-card {
  position: relative;
  z-index: 2;
}

@media (min-width: 1024px) {
  .product-list-main {
    @apply grid-cols-3 gap-6;
    padding: 0 2rem;
  }
}

@media (min-width: 1280px) {
  .product-list-main {
    @apply grid-cols-4 gap-6;
    padding: 0 2rem;
  }
}

/* Focus and Accessibility Improvements */
.modern-dropdown-button:focus,
.modern-search-input:focus {
  @apply ring-2 ring-GTI-BLUE-default ring-opacity-50 outline-none;
}

.product-card-main:focus,
.product-card-main:focus-within {
  @apply ring-2 ring-GTI-BLUE-default ring-opacity-50 outline-none;
}

.modern-dropdown-item:focus {
  @apply bg-GTI-BLUE-default text-white outline-none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .product-card-main {
    @apply border-2 border-gray-900;
  }

  .modern-dropdown-button,
  .modern-search-input {
    @apply border-2 border-gray-900;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .product-card-main,
  .modern-dropdown-button,
  .modern-search-input,
  .product-card-img img {
    @apply transition-none;
  }

  .animate-pulse {
    animation: none;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .product-card-main:hover {
    @apply transform-none shadow-lg;
  }

  .product-card-img img {
    @apply transform-none;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .product-card-img img {
    image-rendering: crisp-edges;
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Advanced Filtering Styles */
.filter-tag {
  @apply px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-GTI-BLUE-default hover:text-white transition-colors cursor-pointer;
}

.filter-tag:hover {
  @apply transform scale-105;
}

.advanced-filter-panel {
  @apply mt-6 pt-6 border-t border-gray-200 animate-fadeInUp;
}

.sort-dropdown {
  @apply appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:border-GTI-BLUE-default transition-all duration-200;
}

.search-suggestions {
  @apply absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-60 overflow-auto animate-fadeInUp;
}

.search-suggestion-item {
  @apply w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 hover:text-GTI-BLUE-default transition-colors duration-150 flex items-center;
}

/* Filter badge animations */
.filter-badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-all duration-200;
}

.filter-badge:hover {
  @apply transform scale-105;
}

/* Loading state improvements */
.skeleton-pulse {
  @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200;
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
