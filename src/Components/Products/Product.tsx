import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useParams, useLocation, useNavigate, Link } from "react-router-dom";
import axios from "axios";
import { BiLinkExternal } from "react-icons/bi";
import { useSelector } from "react-redux";
import ReactCountryFlag from "react-country-flag";
import Slider from "react-slick";
import { Helmet } from "react-helmet";

import { NONE } from "../constants";
import productbanner from "../../assests/banners/product_banner_alt.png";
import companyLogo from "../../assests/banners/company_logo.png";
import {
  followUser,
  unfollowUser,
} from "../../store/actioncreators/followactions";
import { sendConnection } from "../../store/actioncreators/connectionactions";
import { ScreenSpinner } from "../utils/loader";
import { spinnerLoaderStop } from "../../store/actioncreators/loaderactions";
import RenderHTML from "../utils/RenderHTML";
import PdfDownloader from "../../shared/PdfDownloader";
import { ConnectionResponse, PROFILE_TYPES } from "../../shared/enum";

const Product = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  const dispatch: Dispatch<any> = useDispatch();
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);
  let [product, setProduct] = useState({
    _id: "",
    name: "",
    description: "",
    image: "",
    images: [],
    video: "",
    displayOnHomePage: false,
    isApprovedBySubAdmin: false,
    isApprovedByAdmin: false,
    isRejected: false,
    document: "",
    sectorId: "",
    subSectorId: "",
    userId: "",
    createdAt: "",
    developmentStage: "",
    iprStatus: [],
    videos: [],
    documents: [],
    __v: -1,
    users: {
      _id: "",
      fullName: "",
      email: "",
      phoneNumber: "",
      countryCode: "",
      referenceCode: "",
      isEmailVerified: false,
      isUserVerified: false,
      isRejected: false,
      password: "",
      userRole: -1,
      userType: "",
      companyId: "",
      follower: [],
      following: [],
      connections: [
        {
          connectionStatus: "",
          userId: "",
        },
      ],
      createdAt: "",
      __v: -1,
    },
    company: {
      _id: "",
      name: "",
      logo: "",
      description: "",
      address: "",
      website: "",
      country: "",
      companyTurnover: -1,
      companyId: "",
      typeAndSizeOfPartnersRequired: [],
      typesOfPartnershipConsidered: [],
      createdAt: "",
      developmentStage: "",
      iprStatus: [],
      __v: -1,
    },
    sectors: {
      _id: "",
      name: "",
      slug: "",
      image: "",
      createdAt: "",
      __v: -1,
    },
    subsectors: {
      _id: "",
      name: "",
      slug: "",
      sectorId: "",
      createdAt: "",
      __v: -1,
    },
  });

  const sidebar_carousal_settings = {
    dots: true,
    infinite: true,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    swipeToSlide: true,
    autoplay: true,
  };

  const user: USER = useSelector((state: STATE) => state.USER.USER);
  const location = useLocation();
  const navigate = useNavigate();

  const [following, setFollowing] = useState(false);
  const [connected, setConnection] = useState("Connect");

  const addQuery = async (companyName: string, userType: string) => {
    const regex = /[!@#$%^&*()_+{}\[\]:;<>,.?~\\/-]/g;

    const queryParams = new URLSearchParams(location.search);
    queryParams.set(
      "company",
      companyName.toLocaleLowerCase().replaceAll(regex, "").replaceAll(" ", "_")
    );
    queryParams.set("type", userType.toLocaleLowerCase());
    const newSearchString = queryParams.toString();
    navigate({
      pathname: location.pathname,
      search: newSearchString,
    });
  };

  const loadProduct = async (id: string) => {
    const extoken: string =
      localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";
    let configSigned = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/products/loggedinusers/${id}`,
      headers: {
        Authorization: `Bearer ${extoken}`,
        "Content-Type": "application/json",
      },
    };
    let configUnsigned = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/products/${id}`,
      headers: {
        Authorization: `Bearer ${extoken}`,
        "Content-Type": "application/json",
      },
    };

    await axios(user?.admin !== -1 ? configSigned : configUnsigned)
      .then(function (response) {
        setProduct(response.data);
        dispatch(spinnerLoaderStop());
      })
      .catch(function (error) {
        dispatch(spinnerLoaderStop());
      });
  };

  const isFollowing = async (userId: string) => {
    if (!userId) return;

    const extoken: string =
      localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";

    const config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/users/checkisfollowing/${userId}`,
      headers: {
        Authorization: `Bearer ${extoken}`,
        "Content-Type": "application/json",
      },
    };

    await axios(config)
      .then(function (response) {
        setFollowing(response.data);
      })
      .catch(function (error) {});
  };

  const isConnected = async (userId: string) => {
    if (!userId) return;

    const extoken: string =
      localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";

    const config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/users/checkisconnected/${userId}`,
      headers: {
        Authorization: `Bearer ${extoken}`,
        "Content-Type": "application/json",
      },
    };

    await axios(config)
      .then(function (response) {
        if (typeof response.data == "string") {
          setConnection(response.data);
        }
      })
      .catch(function (error) {});
  };

  let { id } = useParams();
  let productId: string = id ? id : "";

  useEffect(() => {
    loadProduct(productId);
  }, []);

  useEffect(() => {
    isFollowing(product?.userId);
    isConnected(product?.userId);
  }, [product?.userId]);

  const DOC = new Date(product.createdAt);

  const handleFollow = () => {
    if (user?.admin !== -1) {
      if (!following) {
        dispatch(followUser(product.userId));
        setFollowing(true);
      }
      return;
    }
    handleLoginModal();
  };
  const handleUnfollow = () => {
    if (user?.admin !== -1) {
      if (following) {
        dispatch(unfollowUser(product.userId));
        setFollowing(false);
      }
      return;
    }
    handleLoginModal();
  };
  const handleConnect = () => {
    if (user?.admin !== -1) {
      if (connected === "Connect") {
        dispatch(sendConnection(product.userId));
        setConnection("Connection Requested");
      }
      return;
    }
    handleLoginModal();
  };

  useEffect(() => {
    console.log(product?.company?.name, product?.users?.userType);
    if (product?.company?.name)
      addQuery(product?.company?.name, product?.users?.userType);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product]);

  return (
    <React.Fragment>
      <Helmet>
        <title>{product?.company?.name}</title>
        <meta
          name="description"
          key="description"
          content={
            product?.description
              ? product?.description
              : product?.company?.description
          }
        />
        <meta name="title" key="title" content={product?.company?.name} />
        <meta property="og:title" content={product.company.name} />
        <meta
          property="og:description"
          content={
            product.description
              ? product.description
              : product.company.description
          }
        />
        <meta
          property="og:image"
          content={product.image === NONE ? productbanner : product.image}
        />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/product/${product._id}`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content={product?.company?.name} />
        <meta
          name="twitter:description"
          content={
            product?.description
              ? product?.description
              : product?.company?.description
          }
        />
        <meta
          name="twitter:image"
          content={product?.image === NONE ? productbanner : product?.image}
        />
        <meta name="twitter:card" content={product?.company?.name} />
      </Helmet>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        {spinner.SPINNER ? (
          <ScreenSpinner />
        ) : (
          <>
            {/* Breadcrumb Navigation */}
            <div className="bg-white border-b border-gray-100">
              <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <nav className="flex" aria-label="Breadcrumb">
                  <ol className="inline-flex items-center space-x-1 md:space-x-3">
                    <li className="inline-flex items-center">
                      <a
                        href="/"
                        className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default transition-colors"
                      >
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        Home
                      </a>
                    </li>
                    <li>
                      <div className="flex items-center">
                        <svg
                          className="w-6 h-6 text-gray-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                        <a
                          href="/technology"
                          className="ml-1 text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default md:ml-2 transition-colors"
                        >
                          Technologies
                        </a>
                      </div>
                    </li>
                    <li>
                      <div className="flex items-center">
                        <svg
                          className="w-6 h-6 text-gray-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                        <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 truncate">
                          {product.name}
                        </span>
                      </div>
                    </li>
                  </ol>
                </nav>
              </div>
            </div>

            {/* Main Content */}
            <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Left Column - Main Content */}
                <div className="lg:col-span-2 space-y-8">
                  {/* Header Section */}
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                    <div className="flex items-start justify-between mb-6">
                      <div className="flex-1">
                        <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 font-roboto mb-4">
                          {product.name}
                        </h1>

                        {/* Company Info */}
                        {product.company?.name && (
                          <div className="flex items-center mb-4">
                            {product.company?.logo && (
                              <img
                                src={product.company.logo}
                                alt={product.company.name}
                                className="w-12 h-12 rounded-lg object-cover mr-3"
                              />
                            )}
                            <div>
                              <p className="text-lg font-semibold text-gray-900">
                                {product.company.name}
                              </p>
                              <p className="text-sm text-gray-600">
                                Technology Provider
                              </p>
                            </div>
                          </div>
                        )}

                        {/* Tags and Badges */}
                        <div className="flex flex-wrap gap-3 mb-6">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-GTI-BLUE-default text-white">
                            {product.sectors?.name}
                          </span>
                          {product.subsectors?.name && (
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                              {product.subsectors.name}
                            </span>
                          )}
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            {product.developmentStage}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Meta Information */}
                    <div className="flex items-center text-sm text-gray-500 mb-6">
                      <svg
                        className="w-4 h-4 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                      Posted on{" "}
                      {DOC.toLocaleString("default", {
                        month: "long",
                        day: "2-digit",
                        year: "numeric",
                      })}
                    </div>
                  </div>

                  {/* Description Section */}
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6">
                      Technology Description
                    </h2>
                    <div className="prose prose-lg max-w-none text-gray-700">
                      <RenderHTML html={product.description} />
                    </div>
                  </div>

                  {/* IPR Status Section */}
                  {product?.iprStatus?.length > 0 && (
                    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">
                        Intellectual Property Rights
                      </h2>
                      <div className="flex flex-wrap gap-3">
                        {product.iprStatus.map((status, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-purple-100 text-purple-800 border border-purple-200"
                          >
                            {status}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Right Column - Media and Actions */}
                <div className="lg:col-span-1 space-y-6">
                  {/* Media Gallery */}
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Media Gallery
                    </h3>

                    {/* Main Image/Video Display */}
                    <div className="aspect-w-16 aspect-h-9 mb-4">
                      <Slider
                        {...sidebar_carousal_settings}
                        className="w-full rounded-xl overflow-hidden"
                      >
                        {product?.images?.length > 0 ? (
                          product.images.map((image, index) => (
                            <div key={index} className="relative">
                              <img
                                className="w-full h-64 object-cover rounded-xl"
                                src={image}
                                alt={`${product.name} - Image ${index + 1}`}
                              />
                            </div>
                          ))
                        ) : (
                          <div className="relative">
                            <img
                              className="w-full h-64 object-cover rounded-xl"
                              src={
                                product.image === NONE
                                  ? productbanner
                                  : product.image
                              }
                              alt={product.name}
                            />
                          </div>
                        )}

                        {/* Videos */}
                        {product?.video && (
                          <div className="relative">
                            <video
                              className="w-full h-64 object-cover rounded-xl"
                              controls
                              poster={
                                product.image !== NONE
                                  ? product.image
                                  : productbanner
                              }
                            >
                              <source src={product.video} type="video/mp4" />
                              Your browser does not support the video tag.
                            </video>
                          </div>
                        )}

                        {product?.videos?.map((video, index) => (
                          <div key={index} className="relative">
                            <video
                              className="w-full h-64 object-cover rounded-xl"
                              controls
                              poster={
                                product.image !== NONE
                                  ? product.image
                                  : productbanner
                              }
                            >
                              <source src={video} type="video/mp4" />
                              Your browser does not support the video tag.
                            </video>
                          </div>
                        ))}
                      </Slider>
                    </div>
                  </div>

                  {/* Documents Section */}
                  {(product?.document || product?.documents?.length > 0) && (
                    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        Documents
                      </h3>
                      <div className="space-y-3">
                        {product?.document && (
                          <PdfDownloader
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                            url={product.document}
                          >
                            <div className="flex items-center">
                              <svg
                                className="w-8 h-8 text-red-500 mr-3"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              <div>
                                <p className="font-medium text-gray-900">
                                  Technology Document
                                </p>
                                <p className="text-sm text-gray-500">
                                  PDF Document
                                </p>
                              </div>
                            </div>
                            <BiLinkExternal className="w-5 h-5 text-gray-400" />
                          </PdfDownloader>
                        )}

                        {product?.documents?.map((document, index) => (
                          <PdfDownloader
                            key={index}
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                            url={document}
                          >
                            <div className="flex items-center">
                              <svg
                                className="w-8 h-8 text-red-500 mr-3"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              <div>
                                <p className="font-medium text-gray-900">
                                  Document {index + 1}
                                </p>
                                <p className="text-sm text-gray-500">
                                  PDF Document
                                </p>
                              </div>
                            </div>
                            <BiLinkExternal className="w-5 h-5 text-gray-400" />
                          </PdfDownloader>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Contact/Action Section */}
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Get in Touch
                    </h3>
                    <div className="space-y-4">
                      <button className="w-full bg-GTI-BLUE-default text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center">
                        <svg
                          className="w-5 h-5 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                        Contact Provider
                      </button>

                      <button className="w-full bg-white text-GTI-BLUE-default py-3 px-4 rounded-lg font-medium border-2 border-GTI-BLUE-default hover:bg-blue-50 transition-colors flex items-center justify-center">
                        <svg
                          className="w-5 h-5 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                          />
                        </svg>
                        Save to Favorites
                      </button>

                      <button className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center">
                        <svg
                          className="w-5 h-5 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                          />
                        </svg>
                        Share Technology
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </React.Fragment>
  );
};

export default Product;
