import React, { useState } from "react";
import { useSelector } from "react-redux";
import {
  CONTENT_TYPE,
  CONTENT_TYPE_DOC,
  FILE_PATH,
  FILE_TYPE,
  developmentStage,
  iprStatus,
  presignedData,
  productItem,
  sectorItem,
  subsectorItem,
} from "../constants";
import { useDispatch } from "react-redux";
import { Dispatch } from "redux";
import axios from "axios";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import { createProduct } from "../../store/actioncreators/productactions";
import { Formik } from "formik";
import { productSchema } from "../validations/productValidations";
import { Form } from "react-router-dom";
import { Editor } from "@tinymce/tinymce-react";

interface INewProduct {
  handleLoginModal: () => void;
}

interface MyFormValues {
  name: string;
  description: string;
  sectorId: string;
  subSectorId: string;
  developmentStage: string;
  iprStatus: string[];
}

type files = {
  image: Boolean;
  document: Boolean;
  imageFile: File | null;
  documentFiles: FileList | null;
};

// const NewProduct: React.FC<INewProduct> = ({ handleLoginModal }) => {
// const sectorlist: SECTOR = useSelector((state: STATE) => state.SECTOR.SECTOR);
// const subsectorlist: SUB_SECTOR = useSelector(
//   (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
// );
// const initialValues: MyFormValues = {
//   name: "",
//   description: "",
//   sectorId: sectorlist?.SECTOR_LIST?.length
//     ? sectorlist.SECTOR_LIST[0]._id
//     : "",
//   subSectorId: subsectorlist?.SUB_SECTOR_LIST?.length
//     ? subsectorlist.SUB_SECTOR_LIST[0]._id
//     : "",
//   developmentStage: developmentStage[0].value,
//   iprStatus: [],
// };
// const dispatch: Dispatch<any> = useDispatch();
// const [iprStatusCheckbox, setIprStatusCheckbox] = useState(iprStatus);
// function handleIprStatus(id: Number) {
//   const updatedOptions = iprStatusCheckbox.map((option) => {
//     if (option.id === id) {
//       return { ...option, checked: !option.checked };
//     }
//     return option;
//   });
//   setIprStatusCheckbox(updatedOptions);
// }
// function getIprStatus() {
//   const iprStatus = [];
//   for (let i = 0; i < iprStatusCheckbox.length; i++) {
//     if (iprStatusCheckbox[i].checked) {
//       iprStatus.push(iprStatusCheckbox[i].value);
//     }
//   }
//   return iprStatus;
// }
// const [files, setFiles] = useState<files>({
//   image: false,
//   document: false,
//   imageFile: null,
//   documentFiles: null,
// });
// const handleImage = function (e: React.ChangeEvent<HTMLInputElement>) {
//   const fileList = e.target.files;
//   if (!fileList) return;
//   setFiles({ ...files, imageFile: fileList[0], image: false });
//   // files.imageFile = fileList;
// };
// const handleDocuments = function (e: React.ChangeEvent<HTMLInputElement>) {
//   const fileList = e.target.files;
//   if (!fileList) return;
//   // initialValues.document = fileList
//   setFiles({ ...files, documentFiles: fileList, document: false });
// };
// const getPresigned = async (content: presignedData) => {
//   const data = JSON.stringify(content);
//   let result: string = "";
//   const config = {
//     method: "post",
//     url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
//     headers: {
//       "Content-Type": "application/json",
//     },
//     data: data,
//   };
//   await axios(config)
//     .then(function (response) {
//       result = response.data;
//     })
//     .catch(function (error) {
//       // console.log("Error", error);
//       result = "error";
//       dispatch(failToast());
//     });
//   return result;
// };
// const postLogo = async (signed: string) => {
//   var config = {
//     method: "put",
//     url: signed,
//     headers: {
//       "Content-Type": CONTENT_TYPE,
//       "Access-Control-Allow-Origin": true,
//     },
//     data: files.imageFile,
//   };
//   await axios(config)
//     .then(function (response) {
//       dispatch(successToast());
//     })
//     .catch(function (error) {
//       // console.log("error uploading logo");
//     });
// };
// const postDocument = async (signed: string, file: File) => {
//   var config = {
//     method: "put",
//     url: signed,
//     headers: {
//       "Content-Type": CONTENT_TYPE_DOC,
//       "Access-Control-Allow-Origin": true,
//     },
//     data: file,
//   };
//   await axios(config)
//     .then(function (response) {
//       dispatch(successToast());
//     })
//     .catch(function (error) {});
// };
// const handleCreate = async (values: MyFormValues) => {
//   if (!files.imageFile) {
//     return setFiles({ ...files, image: true });
//   }
//   if (!files.documentFiles) {
//     return setFiles({ ...files, document: true });
//   }
//   setFiles({ ...files, document: false, image: false });
//   let signedLogoURL: string = "";
//   let signedDocumentURLWhole: string = "";
//   const signedLogoData: presignedData = {
//     fileName: files.imageFile.name || values.name,
//     filePath: FILE_PATH.PRODUCTS_IMAGE,
//     fileType: FILE_TYPE.PNG,
//   };
//   signedLogoURL = await getPresigned(signedLogoData);
//   await postLogo(signedLogoURL);
//   if (files.documentFiles) {
//     Array.from(files.documentFiles).forEach(async (document, i) => {
//       let signedDocumentData: presignedData = {
//         fileName: document.name || values.name,
//         filePath: FILE_PATH.COMPANY_DOCS,
//         fileType: FILE_TYPE.PDF,
//       };
//       let tempurl = (await getPresigned(signedDocumentData)) + " ";
//       signedDocumentURLWhole = tempurl.split("?")[0] + " ";
//       postDocument(tempurl, document);
//       // signedDocumentURL.push(tempurl);
//     });
//   }
//   setTimeout(() => {
//     const data: productItem = {
//       name: values.name,
//       description: values.description,
//       document: signedDocumentURLWhole,
//       image: signedLogoURL.split("?")[0],
//       images: [],
//       sectorId: values.sectorId,
//       subSectorId: values.subSectorId,
//       developmentStage: values.developmentStage,
//       iprStatus: getIprStatus(),
//     };
//     dispatch(createProduct(data));
//   }, 2000);
// };
// return (
//   <div className="product-modal-main">
//     <div className="flex">
//       <h4 className="text-lg font-roboto">Create Product</h4>
//     </div>
//     <Formik
//       initialValues={initialValues}
//       validationSchema={productSchema}
//       onSubmit={(values) => handleCreate(values)}
//     >
//       {({ handleChange, setFieldValue, handleSubmit, errors, values }) => (
//         <>
//           <Form className="flex flex-col w-full space-y-4 justify-center items-center">
//             <div className="flex flex-col w-full space-x-2 relative">
//               <div className="relative">
//                 <input
//                   onChange={(e) => setFieldValue("name", e.target.value)}
//                   type="text"
//                   id="floating_outlined"
//                   className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-blue-600 peer"
//                   placeholder=" "
//                 />
//                 <label
//                   htmlFor="floating_outlined"
//                   className="absolute text-sm text-gray-500  duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white  px-2 peer-focus:px-2 peer-focus:text-blue-600  peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
//                 >
//                   Product Name
//                 </label>
//               </div>
//               {errors.name && (
//                 <p
//                   id="filled_error_help"
//                   className="mt-2 text-xs text-red-600 dark:text-red-400"
//                 >
//                   {errors.name}
//                 </p>
//               )}
//             </div>
//             <div className="flex flex-col w-full space-x-2 relative">
//               <div className="relative">
//                 <Editor
//                   apiKey={process.env.REACT_APP_TINYMCE_API_KEY}
//                   init={{
//                     height: 200,
//                     menubar: false,
//                     plugins: [
//                       "advlist autolink lists link image charmap print preview anchor",
//                       "searchreplace visualblocks code fullscreen",
//                       "insertdatetime media table paste code help wordcount",
//                     ],
//                     toolbar:
//                       "undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help",
//                     placeholder: "Write your products description here...",
//                   }}
//                   onEditorChange={(e) => setFieldValue("description", e)}
//                 />
//               </div>
//               {errors.description && (
//                 <p
//                   id="filled_error_help"
//                   className="mt-2 text-xs text-red-600 dark:text-red-400"
//                 >
//                   {errors.description}
//                 </p>
//               )}
//             </div>
//             <div className="flex flex-col w-full">
//               <div className="flex flex-row w-full space-x-5 items-center">
//                 <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
//                   Sector Type:
//                 </h3>
//                 <select
//                   onChange={(e) => setFieldValue("sectorId", e.target.value)}
//                   defaultValue={values.sectorId}
//                   className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
//                 >
//                   {sectorlist.SECTOR_LIST.map((item: sectorItem, id) => {
//                     return <option value={item._id}>{item.name}</option>;
//                   })}
//                 </select>
//               </div>
//               {errors.sectorId && (
//                 <p
//                   id="filled_error_help"
//                   className="mt-2 text-xs text-red-600 dark:text-red-400"
//                 >
//                   {errors.sectorId}
//                 </p>
//               )}
//             </div>
//             <div className="flex flex-col w-full">
//               <div className="flex flex-row w-full space-x-5 items-center">
//                 <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
//                   Sub Sector Type:
//                 </h3>
//                 <select
//                   onChange={(e) => setFieldValue("subsecId", e.target.value)}
//                   defaultValue={values.subSectorId}
//                   className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
//                 >
//                   {subsectorlist.SUB_SECTOR_LIST.map(
//                     (item: subsectorItem, id) => {
//                       return <option value={item._id}>{item.name}</option>;
//                     }
//                   )}
//                 </select>
//               </div>
//               {errors.subSectorId && (
//                 <p
//                   id="filled_error_help"
//                   className="mt-2 text-xs text-red-600 dark:text-red-400"
//                 >
//                   {errors.subSectorId}
//                 </p>
//               )}
//             </div>
//             <div className="flex flex-col w-full">
//               <div className="flex flex-row w-full space-x-5 items-center">
//                 <h3 className="font-robot text-gray-800 text-sm whitespace-nowrap  ">
//                   Development Stage:
//                 </h3>
//                 <select
//                   onChange={(e) =>
//                     setFieldValue("developmentStage", e.target.value)
//                   }
//                   defaultValue={values.developmentStage}
//                   className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
//                 >
//                   {developmentStage.map((stage, id) => {
//                     return <option value={stage.value}>{stage.value}</option>;
//                   })}
//                 </select>
//               </div>
//             </div>
//             <div className="flex flex-col w-full space-x-2 relative">
//               <div className="relative">
//                 <div className="relative mb-3">
//                   <label className="profile-content-head-2">IPR Status</label>
//                 </div>
//                 <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
//                   {iprStatusCheckbox.map((option) => (
//                     <label
//                       className="flex items-center space-x-2 ml-4"
//                       key={option.id}
//                     >
//                       <input
//                         type="checkbox"
//                         className="h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500"
//                         checked={option.checked}
//                         onChange={() => handleIprStatus(option.id)}
//                       />
//                       <span className="text-gray-700 personal-input">
//                         {option.value}
//                       </span>
//                     </label>
//                   ))}
//                 </div>
//               </div>
//             </div>
//             <div className="flex flex-col w-full">
//               <label
//                 className="block mb-2 text-sm font-medium text-gray-900"
//                 htmlFor="logo"
//               >
//                 Upload Product Image{" "}
//                 <span className="text-red-500 text-xs">(.png only)</span>
//               </label>
//               <input
//                 onChange={handleImage}
//                 accept=".png"
//                 type="file"
//                 id="logo"
//                 aria-label="company-logo"
//                 className="modal-input"
//                 placeholder="Click to upload Company's Logo"
//               />
//               <p
//                 id="filled_error_help"
//                 className={
//                   "mt-2 text-xs text-red-600 dark:text-red-400 " +
//                   (!files.image ? "hidden" : "")
//                 }
//               >
//                 {"Please upload product Image"}
//               </p>
//             </div>
//             <div className="flex flex-col w-full">
//               <label
//                 className="block mb-2 text-sm font-medium text-gray-900"
//                 htmlFor="documents"
//               >
//                 Upload Product Documents
//                 <span className="text-red-500 text-xs">(.pdf only)</span>
//               </label>
//               <input
//                 onChange={handleDocuments}
//                 accept=".pdf"
//                 type="file"
//                 id="documents"
//                 multiple
//                 aria-label="company-documents"
//                 className="modal-input"
//                 placeholder="Click to upload Document"
//               />
//               <p
//                 id="filled_error_help"
//                 className={
//                   "mt-2 text-xs text-red-600 dark:text-red-400 " +
//                   (!files.document ? "hidden" : "")
//                 }
//               >
//                 {"Please upload product documents"}
//               </p>
//             </div>
//             <button
//               type="submit"
//               onClick={() => handleSubmit}
//               className="button active"
//             >
//               Create
//             </button>
//           </Form>
//         </>
//       )}
//     </Formik>
//   </div>
// );
// };

const NewProduct = () => {
  return <p>NewProduct</p>;
};

export default NewProduct;
