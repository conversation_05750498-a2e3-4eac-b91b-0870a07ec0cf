import React from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet";
import { AiOutlineClose } from "react-icons/ai";
import { YOUR_TECHNOLOGY } from "../constants";

const CreateTechnology: React.FC = () => {
  const navigate = useNavigate();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // For now, just show an alert and navigate back
    alert("Technology creation functionality will be implemented soon!");
    navigate(YOUR_TECHNOLOGY);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/20 to-indigo-50/30">
      <Helmet>
        <title>Create Technology | GTI</title>
        <meta
          name="description"
          content="Create and submit your innovative technology for review and approval"
        />
      </Helmet>

      {/* Header Section */}
      <div className="bg-white shadow-sm border-b border-gray-100 w-full">
        <div className="w-full px-2 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Create New Technology
              </h1>
              <p className="text-gray-600">
                Submit your innovative technology for review and approval by our
                admin team.
              </p>
            </div>
            <button
              onClick={() => navigate(YOUR_TECHNOLOGY)}
              className="inline-flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 font-medium rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <AiOutlineClose className="w-5 h-5" />
              Cancel
            </button>
          </div>
        </div>
      </div>

      {/* Form Section */}
      <div className="w-full">
        <div className="w-full">
          <div className="bg-white w-full px-2 py-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Basic Information Section */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 border-b border-gray-200 pb-3">
                  Basic Information
                </h2>

                {/* Technology Name */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Technology Name *
                  </label>
                  <input
                    type="text"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="Enter your technology name"
                  />
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Description *
                  </label>
                  <textarea
                    rows={6}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="Describe your technology in detail..."
                  />
                </div>
              </div>

              {/* Sector and Category Section */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 border-b border-gray-200 pb-3">
                  Category & Classification
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Sector */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Sector *
                    </label>
                    <select
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    >
                      <option value="">Select Sector</option>
                      <option value="technology">Technology</option>
                      <option value="healthcare">Healthcare</option>
                      <option value="finance">Finance</option>
                      <option value="manufacturing">Manufacturing</option>
                      <option value="agriculture">Agriculture</option>
                    </select>
                  </div>

                  {/* Sub Sector */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Sub Sector
                    </label>
                    <select className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                      <option value="">Select Sub Sector</option>
                      <option value="ai">Artificial Intelligence</option>
                      <option value="blockchain">Blockchain</option>
                      <option value="iot">Internet of Things</option>
                      <option value="robotics">Robotics</option>
                      <option value="biotech">Biotechnology</option>
                    </select>
                  </div>
                </div>

                {/* Development Stage */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Development Stage
                  </label>
                  <select className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                    <option value="">Select Development Stage</option>
                    <option value="concept">Concept</option>
                    <option value="prototype">Prototype</option>
                    <option value="testing">Testing</option>
                    <option value="pilot">Pilot</option>
                    <option value="production">Production Ready</option>
                    <option value="commercialized">Commercialized</option>
                  </select>
                </div>
              </div>

              {/* IPR Status Section */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 border-b border-gray-200 pb-3">
                  Intellectual Property Rights (IPR) Status
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    "Patent Filed",
                    "Patent Granted",
                    "Trademark Filed",
                    "Trademark Granted",
                    "Copyright Filed",
                    "Copyright Granted",
                    "Trade Secret",
                    "No IP Protection",
                  ].map((status, index) => (
                    <label
                      key={index}
                      className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                    >
                      <input
                        type="checkbox"
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">
                        {status}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* File Uploads Section */}
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 border-b border-gray-200 pb-3">
                  Media & Documents
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Images */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Images *
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors duration-200">
                      <input
                        type="file"
                        accept="image/*"
                        multiple
                        required
                        className="hidden"
                        id="images"
                      />
                      <label htmlFor="images" className="cursor-pointer">
                        <div className="text-gray-500">
                          <svg
                            className="mx-auto h-12 w-12 mb-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          <p className="text-sm">Click to upload images</p>
                          <p className="text-xs text-gray-400">
                            PNG, JPG up to 10MB each
                          </p>
                        </div>
                      </label>
                    </div>
                  </div>

                  {/* Documents */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Documents
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors duration-200">
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx"
                        multiple
                        className="hidden"
                        id="documents"
                      />
                      <label htmlFor="documents" className="cursor-pointer">
                        <div className="text-gray-500">
                          <svg
                            className="mx-auto h-12 w-12 mb-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          <p className="text-sm">Click to upload documents</p>
                          <p className="text-xs text-gray-400">
                            PDF, DOC up to 10MB each
                          </p>
                        </div>
                      </label>
                    </div>
                  </div>

                  {/* Videos */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Videos
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors duration-200">
                      <input
                        type="file"
                        accept=".mp4,.webm"
                        multiple
                        className="hidden"
                        id="videos"
                      />
                      <label htmlFor="videos" className="cursor-pointer">
                        <div className="text-gray-500">
                          <svg
                            className="mx-auto h-12 w-12 mb-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                            />
                          </svg>
                          <p className="text-sm">Click to upload videos</p>
                          <p className="text-xs text-gray-400">
                            MP4, WEBM up to 50MB each
                          </p>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => navigate(YOUR_TECHNOLOGY)}
                  className="px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-xl hover:bg-gray-50 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
                >
                  Create Technology
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateTechnology;
