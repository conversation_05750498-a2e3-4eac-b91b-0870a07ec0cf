import React, { useState } from "react";
import {
  StarIcon,
  HeartIcon,
  UserIcon,
  EnvelopeIcon,
  LockClosedIcon,
  SparklesIcon,
  RocketLaunchIcon,
} from "@heroicons/react/24/outline";

// Simple placeholder components
const InteractiveButton: React.FC<any> = ({
  children,
  variant,
  animation,
  size,
  disabled,
  loading,
  icon,
  onClick,
  ...props
}) => (
  <button
    className={`
      px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2
      ${variant === "primary" ? "bg-blue-600 text-white hover:bg-blue-700" : ""}
      ${
        variant === "outline"
          ? "border-2 border-blue-600 text-blue-600 hover:bg-blue-50"
          : ""
      }
      ${variant === "ghost" ? "text-blue-600 hover:bg-blue-50" : ""}
      ${variant === "danger" ? "bg-red-600 text-white hover:bg-red-700" : ""}
      ${size === "lg" ? "px-6 py-3 text-lg" : ""}
      ${size === "xl" ? "px-8 py-4 text-xl" : ""}
      ${disabled ? "opacity-50 cursor-not-allowed" : "hover:scale-105"}
      ${animation === "lift" ? "hover:shadow-lg" : ""}
      ${animation === "glow" ? "hover:shadow-blue-300 hover:shadow-lg" : ""}
      ${animation === "bounce" ? "hover:animate-bounce" : ""}
      ${animation === "pulse" ? "hover:animate-pulse" : ""}
      ${animation === "shake" ? "hover:animate-pulse" : ""}
      ${
        animation === "shimmer"
          ? "bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-size-200 animate-shimmer"
          : ""
      }
    `}
    disabled={disabled || loading}
    onClick={onClick}
    {...props}
  >
    {loading ? (
      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
    ) : (
      icon
    )}
    {children}
  </button>
);

const InteractiveCard: React.FC<any> = ({
  children,
  variant,
  animation,
  hoverEffect,
  ...props
}) => (
  <div
    className={`
      p-6 rounded-xl transition-all duration-300
      ${variant === "elevated" ? "bg-white shadow-lg hover:shadow-xl" : ""}
      ${
        variant === "glass"
          ? "bg-white/80 backdrop-blur-sm border border-white/20"
          : ""
      }
      ${variant === "outlined" ? "bg-white border-2 border-gray-200" : ""}
      ${animation === "lift" ? "hover:-translate-y-2" : ""}
      ${animation === "tilt" ? "hover:rotate-1" : ""}
      ${animation === "glow" ? "hover:shadow-blue-300 hover:shadow-lg" : ""}
      ${hoverEffect ? "hover:scale-105" : ""}
    `}
    {...props}
  >
    {children}
  </div>
);

const InteractiveInput: React.FC<any> = ({
  label,
  type = "text",
  placeholder,
  value,
  onChange,
  error,
  icon,
  ...props
}) => (
  <div className="space-y-2">
    {label && (
      <label className="block text-sm font-medium text-gray-700">{label}</label>
    )}
    <div className="relative">
      {icon && (
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
          {icon}
        </div>
      )}
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        className={`
          w-full px-4 py-3 border rounded-lg transition-all duration-300
          ${icon ? "pl-10" : ""}
          ${
            error
              ? "border-red-500 focus:border-red-500"
              : "border-gray-300 focus:border-blue-500"
          }
          focus:outline-none focus:ring-2 focus:ring-blue-500/20
        `}
        {...props}
      />
    </div>
    {error && <p className="text-sm text-red-600">{error}</p>}
  </div>
);

const LoadingSpinner: React.FC<any> = ({ variant, size, color, text }) => (
  <div className="flex flex-col items-center gap-2">
    {variant === "spinner" && (
      <div
        className={`
        border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin
        ${size === "lg" ? "w-8 h-8" : "w-6 h-6"}
      `}
      />
    )}
    {variant === "dots" && (
      <div className="flex gap-1">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={`
              rounded-full animate-pulse
              ${size === "lg" ? "w-3 h-3" : "w-2 h-2"}
              ${
                color === "blue"
                  ? "bg-blue-600"
                  : color === "green"
                  ? "bg-green-600"
                  : color === "red"
                  ? "bg-red-600"
                  : "bg-gray-600"
              }
            `}
            style={{ animationDelay: `${i * 0.2}s` }}
          />
        ))}
      </div>
    )}
    {variant === "bars" && (
      <div className="flex gap-1">
        {[0, 1, 2, 3].map((i) => (
          <div
            key={i}
            className={`
              animate-pulse
              ${size === "lg" ? "w-2 h-8" : "w-1 h-6"}
              ${
                color === "green"
                  ? "bg-green-600"
                  : color === "red"
                  ? "bg-red-600"
                  : "bg-blue-600"
              }
            `}
            style={{ animationDelay: `${i * 0.1}s` }}
          />
        ))}
      </div>
    )}
    {variant === "ring" && (
      <div
        className={`
        border-4 border-gray-200 rounded-full animate-spin
        ${size === "lg" ? "w-8 h-8" : "w-6 h-6"}
        ${color === "red" ? "border-t-red-600" : "border-t-blue-600"}
      `}
      />
    )}
    {text && <span className="text-sm text-gray-600">{text}</span>}
  </div>
);

const SkeletonCard: React.FC = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg animate-pulse">
    <div className="h-4 bg-gray-200 rounded mb-4"></div>
    <div className="h-3 bg-gray-200 rounded mb-2"></div>
    <div className="h-3 bg-gray-200 rounded mb-4 w-3/4"></div>
    <div className="h-8 bg-gray-200 rounded"></div>
  </div>
);

const LoadingOverlay: React.FC<any> = ({ isVisible, text, variant }) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-white p-8 rounded-xl shadow-2xl flex flex-col items-center gap-4">
        <LoadingSpinner variant={variant} size="lg" />
        <p className="text-gray-700 font-medium">{text}</p>
      </div>
    </div>
  );
};

const InteractiveDemo: React.FC = () => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [emailValue, setEmailValue] = useState("");
  const [passwordValue, setPasswordValue] = useState("");
  const [inputError, setInputError] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);

  const handleButtonClick = () => {
    setShowOverlay(true);
    setTimeout(() => setShowOverlay(false), 3000);
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
    if (value.length < 3) {
      setInputError("Must be at least 3 characters");
    } else {
      setInputError("");
      setShowSuccess(true);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-12 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 animate-fade-in-down">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-GTI-BLUE-default via-purple-600 to-blue-600 bg-clip-text text-transparent mb-4">
            Interactive Elements Demo
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Showcase of enhanced UI components with micro-animations and
            interactive elements
          </p>
        </div>

        {/* Interactive Buttons Section */}
        <section className="mb-16 animate-fade-in-up">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Interactive Buttons
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 stagger-children">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">
                Primary Variants
              </h3>
              <InteractiveButton
                variant="primary"
                animation="lift"
                icon={<RocketLaunchIcon className="w-5 h-5" />}
                onClick={handleButtonClick}
              >
                Launch Demo
              </InteractiveButton>
              <InteractiveButton
                variant="primary"
                animation="glow"
                size="lg"
                icon={<SparklesIcon className="w-5 h-5" />}
              >
                Glow Effect
              </InteractiveButton>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">
                Secondary Variants
              </h3>
              <InteractiveButton
                variant="outline"
                animation="bounce"
                icon={<HeartIcon className="w-5 h-5" />}
              >
                Bounce Animation
              </InteractiveButton>
              <InteractiveButton
                variant="ghost"
                animation="pulse"
                icon={<StarIcon className="w-5 h-5" />}
              >
                Pulse Effect
              </InteractiveButton>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">
                Special States
              </h3>
              <InteractiveButton variant="danger" animation="shake" disabled>
                Disabled Button
              </InteractiveButton>
              <InteractiveButton variant="primary" loading>
                Loading State
              </InteractiveButton>
            </div>
          </div>
        </section>

        {/* Interactive Cards Section */}
        <section className="mb-16 animate-fade-in-up">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Interactive Cards
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 stagger-children">
            <InteractiveCard
              variant="elevated"
              animation="lift"
              hoverEffect={true}
              clickable={true}
              onClick={() => alert("Card clicked!")}
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-GTI-BLUE-default/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <StarIcon className="w-8 h-8 text-GTI-BLUE-default" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Lift Animation
                </h3>
                <p className="text-gray-600">
                  Hover to see the lift effect with enhanced shadows
                </p>
              </div>
            </InteractiveCard>

            <InteractiveCard
              variant="glass"
              animation="tilt"
              hoverEffect={true}
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <SparklesIcon className="w-8 h-8 text-purple-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Tilt Effect
                </h3>
                <p className="text-gray-600">
                  Glass morphism with 3D tilt animation
                </p>
              </div>
            </InteractiveCard>

            <InteractiveCard
              variant="outlined"
              animation="glow"
              hoverEffect={true}
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <RocketLaunchIcon className="w-8 h-8 text-green-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Glow Effect
                </h3>
                <p className="text-gray-600">
                  Outlined card with glowing hover state
                </p>
              </div>
            </InteractiveCard>
          </div>
        </section>

        {/* Interactive Inputs Section */}
        <section className="mb-16 animate-fade-in-up">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Interactive Inputs
          </h2>
          <div className="max-w-2xl mx-auto space-y-8">
            <InteractiveInput
              label="Username"
              placeholder="Enter your username"
              value={inputValue}
              onChange={handleInputChange}
              icon={<UserIcon className="w-5 h-5" />}
              error={inputError}
              success={showSuccess && !inputError}
              animation="glow"
              required
            />

            <InteractiveInput
              label="Email Address"
              type="email"
              placeholder="<EMAIL>"
              value={emailValue}
              onChange={setEmailValue}
              variant="floating"
              icon={<EnvelopeIcon className="w-5 h-5" />}
              animation="pulse"
            />

            <InteractiveInput
              label="Password"
              type="password"
              placeholder="Enter secure password"
              value={passwordValue}
              onChange={setPasswordValue}
              variant="filled"
              icon={<LockClosedIcon className="w-5 h-5" />}
              animation="bounce"
            />
          </div>
        </section>

        {/* Loading States Section */}
        <section className="mb-16 animate-fade-in-up">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Loading States
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Spinner</h3>
              <LoadingSpinner variant="spinner" size="lg" text="Loading..." />
            </div>
            <div className="text-center space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Dots</h3>
              <LoadingSpinner variant="dots" size="lg" color="blue" />
            </div>
            <div className="text-center space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Bars</h3>
              <LoadingSpinner variant="bars" size="lg" color="green" />
            </div>
            <div className="text-center space-y-4">
              <h3 className="text-lg font-semibold text-gray-700">Ring</h3>
              <LoadingSpinner variant="ring" size="lg" color="red" />
            </div>
          </div>
        </section>

        {/* Skeleton Loading Section */}
        <section className="mb-16 animate-fade-in-up">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Skeleton Loading
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
          </div>
        </section>

        {/* Animation Classes Demo */}
        <section className="mb-16 animate-fade-in-up">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Animation Classes
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 stagger-children">
            <div className="bg-white p-6 rounded-xl shadow-md hover-lift">
              <h3 className="font-semibold mb-2">Hover Lift</h3>
              <p className="text-gray-600 text-sm">Hover to see lift effect</p>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-md hover-scale">
              <h3 className="font-semibold mb-2">Hover Scale</h3>
              <p className="text-gray-600 text-sm">Hover to see scale effect</p>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-md hover-glow">
              <h3 className="font-semibold mb-2">Hover Glow</h3>
              <p className="text-gray-600 text-sm">Hover to see glow effect</p>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-md hover-tilt">
              <h3 className="font-semibold mb-2">Hover Tilt</h3>
              <p className="text-gray-600 text-sm">Hover to see 3D tilt</p>
            </div>
          </div>
        </section>

        {/* Demo Button */}
        <div className="text-center animate-fade-in-up">
          <InteractiveButton
            variant="primary"
            size="xl"
            animation="shimmer"
            icon={<SparklesIcon className="w-6 h-6" />}
            onClick={handleButtonClick}
          >
            Show Loading Overlay Demo
          </InteractiveButton>
        </div>
      </div>

      {/* Loading Overlay */}
      <LoadingOverlay
        isVisible={showOverlay}
        text="Loading awesome content..."
        variant="spinner"
        backdrop="blur"
      />
    </div>
  );
};

export default InteractiveDemo;
