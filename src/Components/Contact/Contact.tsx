import React, { Dispatch } from "react";
import { Form, Formik } from "formik";
import { BsFillTelephoneFill } from "react-icons/bs";
import { GrMail } from "react-icons/gr";
import { IoLocationSharp } from "react-icons/io5";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet";

import { createContact } from "../../store/actioncreators/contactactions";
import {
  contactItem,
  CONTACT_ADDRESS,
  CONTACT_EMAIL,
  CONTACT_NUMBER,
  HOME,
  title,
  metaData,
  NONE,
} from "../constants";
import { contactSchema } from "../validations/contactValidations";
// import { InteractiveInput, InteractiveButton } from "../ui";
// import "../../styles/animations.css";

interface MyFormValues {
  fullName: string;
  phone: string;
  email: string;
  message: string;
}
const Contact = () => {
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();
  const initialValues: MyFormValues = {
    fullName: "",
    phone: "",
    email: "",
    message: "",
  };

  const handleSubmit = (values: MyFormValues) => {
    const data: contactItem = {
      email: values.email,
      name: values.fullName,
      message: values.message,
      phoneNumber: values.phone,
    };
    dispatch(createContact(data));
    navigate(HOME);
  };

  return (
    <div className="md:flex h-full flex-row w-11/12 md:w-4/6 mt-0 md:mt-20 m-20 mx-auto rounded-lg">
      <Helmet>
        <title>{title.CONTACT}</title>
        <meta name="description" key="description" content={metaData.CONTACT} />
        <meta name="title" key="title" content="Contact Us" />
        <meta property="og:title" content="Contact Us" />
        <meta property="og:description" content="Contact Us" />
        <meta property="og:image" content={NONE} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/contact-us`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Contact Us" />
        <meta name="twitter:description" content={metaData.CONTACT} />
        <meta name="twitter:image" content={NONE} />
        <meta name="twitter:card" content="Contact Us" />
      </Helmet>
      <div className="flex rounded-lg md:rounded-l-lg flex-col p-6 md:p-10 bg-GTI-BLUE-default justify-center md:w-1/2">
        <p className="text-white font-roboto text-2xl md:text-4xl -translate-y-5 ">
          Contact Us
        </p>
        <p className="text-white items-center py-2 inline-flex font-roboto text-sm md:text-lg">
          <span>
            <BsFillTelephoneFill className="mr-2 w-5 h-5" />
          </span>
          {CONTACT_NUMBER}
        </p>
        <p className="text-white items-center py-2 inline-flex font-roboto text-sm md:text-lg">
          <span>
            <GrMail className="mr-2 w-5 h-5" />
          </span>
          {CONTACT_EMAIL}
        </p>
        <p className="text-white items-center py-2 inline-flex font-roboto text-sm md:text-lg">
          <span>
            <IoLocationSharp className="mr-2 w-5 h-5" />
          </span>
          {CONTACT_ADDRESS}
        </p>
      </div>
      <div className="flex flex-col md:w-1/2 ">
        <Formik
          initialValues={initialValues}
          validationSchema={contactSchema}
          onSubmit={(values, { resetForm }) => {
            handleSubmit(values);
            resetForm();
          }}
        >
          {({ handleChange, setFieldValue, handleSubmit, errors, values }) => (
            <>
              <Form className="flex flex-col rounded-2xl justify-center items-center pt-8 pb-8 md:p-12 border-2 border-gray-200 bg-white shadow-xl animate-fade-in-up">
                <div className="w-full px-6 space-y-6">
                  <div className="relative">
                    <input
                      onChange={(e) =>
                        setFieldValue("fullName", e.target.value)
                      }
                      type="text"
                      value={values.fullName}
                      className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder="Enter your full name"
                    />
                    {errors.fullName && (
                      <p className="mt-2 text-xs text-red-600">
                        {errors.fullName}
                      </p>
                    )}
                  </div>
                  <div className="relative">
                    <input
                      onChange={(e) => setFieldValue("phone", e.target.value)}
                      type="tel"
                      value={values.phone}
                      className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder="Enter your phone number"
                    />
                    {errors.phone && (
                      <p className="mt-2 text-xs text-red-600">
                        {errors.phone}
                      </p>
                    )}
                  </div>
                  <div className="relative">
                    <input
                      onChange={(e) => setFieldValue("email", e.target.value)}
                      type="email"
                      value={values.email}
                      className="block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-blue-600 peer"
                      placeholder="Enter your email address"
                    />
                    {errors.email && (
                      <p className="mt-2 text-xs text-red-600">
                        {errors.email}
                      </p>
                    )}
                  </div>
                  {/* Enhanced Message Textarea */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Message <span className="text-red-500">*</span>
                    </label>
                    <div className="relative group">
                      <textarea
                        value={values.message}
                        onChange={(e) =>
                          setFieldValue("message", e.target.value)
                        }
                        placeholder="Enter your message here..."
                        rows={5}
                        className="w-full px-4 py-3 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-300 bg-gradient-to-r from-gray-50 to-white focus:from-white focus:to-blue-50 placeholder-gray-400 text-gray-800 resize-vertical min-h-[120px] max-h-[300px]"
                      />
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-GTI-BLUE-default/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                    {errors.message && (
                      <div className="flex items-center space-x-2 text-red-600 bg-red-50 px-4 py-2 rounded-xl border border-red-200">
                        <span className="text-sm font-medium">
                          {errors.message}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Submit Button */}
                  <div className="pt-4">
                    <button
                      type="submit"
                      className="w-full px-4 py-2 rounded-lg bg-GTI-BLUE-default text-white font-roboto hover:bg-blue-700 transition-colors duration-300"
                    >
                      Send Message
                    </button>
                  </div>
                </div>
              </Form>
            </>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default Contact;
