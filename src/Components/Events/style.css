/* Modern Button Styles */
.event-button {
  @apply font-medium rounded-xl text-sm px-6 py-3 mx-1 mb-2 focus:outline-none transition-all duration-200 shadow-sm hover:shadow-md;
}

.active {
  @apply bg-GTI-BLUE-default text-white hover:bg-blue-800 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
}

.not-active {
  @apply bg-white text-GTI-BLUE-default border border-gray-200 hover:border-GTI-BLUE-default/30 hover:bg-gray-50;
}

/* Modern Event Card Styles */
.event-card-main {
  @apply flex flex-col duration-300 border border-gray-200 justify-start items-stretch shadow-sm hover:shadow-xl hover:shadow-GTI-BLUE-default/20 space-y-0 rounded-2xl cursor-pointer bg-white transform hover:-translate-y-1 transition-all overflow-hidden;
  width: 100%;
  max-width: 380px;
}

.event-card-img {
  @apply flex w-full overflow-hidden rounded-t-2xl;
  height: 220px;
}

.event-card-img img {
  @apply w-full h-full object-cover transition-transform duration-300 hover:scale-105;
}

.event-card-content {
  @apply flex flex-col p-6 space-y-4 flex-1;
}

.event-card-title {
  @apply flex flex-col space-y-2;
}

.event-card-button {
  @apply w-full flex flex-row justify-center items-center;
}

/* Modern Grid Layout */
.event-list-main {
  @apply grid w-full container-spacing mx-auto gap-6 lg:grid-cols-3 md:grid-cols-2 grid-cols-1;
}

/* Main Event Page Layout */
.event-main {
  @apply flex flex-col w-full min-h-screen bg-gray-50;
}

.event-hero-section {
  @apply relative w-full h-96 lg:h-[500px] overflow-hidden;
}

.event-hero-image {
  @apply w-full h-full object-cover;
}

.event-hero-overlay {
  @apply absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent;
}

.event-content-container {
  @apply relative -mt-20 z-10 max-w-4xl mx-auto px-6 lg:px-8;
}

.event-content-card {
  @apply bg-white rounded-3xl shadow-2xl p-8 lg:p-12 space-y-8;
}

.event-header {
  @apply space-y-4;
}

.event-title {
  @apply text-3xl lg:text-4xl font-bold text-gray-900 leading-tight;
}

.event-meta {
  @apply flex flex-wrap items-center gap-4 text-sm text-gray-600;
}

.event-meta-item {
  @apply flex items-center space-x-2 bg-gray-100 px-3 py-1 rounded-full;
}

.event-description {
  @apply text-gray-700 leading-relaxed text-lg;
}

.event-details-grid {
  @apply grid md:grid-cols-2 gap-6;
}

.event-detail-item {
  @apply bg-gray-50 rounded-xl p-4 space-y-2;
}

.event-detail-label {
  @apply text-sm font-semibold text-gray-500 uppercase tracking-wide;
}

.event-detail-value {
  @apply text-lg font-medium text-gray-900;
}

/* Payment Integration Styles */
.payment-section {
  @apply bg-gradient-to-r from-GTI-BLUE-default to-blue-600 rounded-2xl p-6 lg:p-8 text-white;
}

.payment-header {
  @apply text-center space-y-2 mb-6;
}

.payment-title {
  @apply text-2xl font-bold;
}

.payment-subtitle {
  @apply text-blue-100;
}

.payment-price {
  @apply text-4xl font-bold text-center mb-6;
}

.payment-button {
  @apply w-full bg-white text-GTI-BLUE-default font-semibold py-4 px-6 rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
}

.payment-features {
  @apply space-y-3 mt-6;
}

.payment-feature {
  @apply flex items-center space-x-3 text-blue-100;
}

/* Event Video Styles */
.event-video {
  @apply flex flex-col w-full min-h-screen bg-gray-50 items-center py-8;
}

.event-video-container {
  @apply max-w-6xl mx-auto px-6 lg:px-8 space-y-8;
}

.event-video-header {
  @apply text-center space-y-4;
}

.event-video-title {
  @apply text-3xl lg:text-4xl font-bold text-gray-900;
}

.event-video-description {
  @apply text-gray-700 text-center text-lg leading-relaxed;
}

.event-video-player {
  @apply w-full max-w-4xl mx-auto rounded-2xl overflow-hidden shadow-2xl;
}

/* Page Header Styles */
.events-page-header {
  @apply bg-gradient-to-br from-GTI-BLUE-default via-blue-700 to-blue-800 text-white py-16 lg:py-20;
}

.events-page-content {
  @apply max-w-4xl mx-auto px-6 lg:px-8 text-center space-y-6;
}

.events-page-title {
  @apply flex items-center justify-center space-x-4 mb-6;
}

.events-page-title-icon {
  @apply w-12 h-12 lg:w-16 lg:h-16 filter brightness-0 invert;
}

.events-page-title-text {
  @apply text-4xl lg:text-5xl font-bold tracking-tight;
}

.events-page-description {
  @apply text-lg lg:text-xl text-blue-100 leading-relaxed max-w-3xl mx-auto;
}

/* Filter Section */
.events-filters-container {
  @apply bg-white border-b border-gray-100 py-6;
}

.events-filters-content {
  @apply max-w-6xl mx-auto px-6 lg:px-8 flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0;
}

.events-filter-dropdown {
  @apply relative;
}

.events-filter-button {
  @apply flex items-center justify-between w-48 px-4 py-3 text-sm font-medium text-GTI-BLUE-default bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:ring-opacity-50 transition-all duration-200;
}

.events-filter-dropdown-menu {
  @apply absolute top-full left-0 mt-2 w-full bg-white border border-gray-200 rounded-xl shadow-lg z-10 overflow-hidden;
}

.events-filter-dropdown-item {
  @apply block w-full px-4 py-3 text-sm text-gray-700 hover:bg-GTI-BLUE-default hover:text-white transition-colors duration-150 cursor-pointer;
}

/* Container Spacing */
.container-spacing {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-spacing {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-spacing {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Responsive Design Improvements */
@media (max-width: 640px) {
  .events-filter-button {
    @apply w-full text-base py-3;
  }

  .event-list-main {
    @apply px-4 gap-4;
  }

  .event-card-main {
    @apply mx-0 hover:transform-none;
  }

  .event-card-main {
    @apply min-h-[350px];
  }

  .events-filter-dropdown-item {
    @apply py-4 text-base;
  }

  .events-page-title-text {
    @apply text-3xl;
  }

  .events-filters-container {
    @apply px-4 py-4;
  }

  .event-content-card {
    @apply p-6;
  }

  .event-title {
    @apply text-2xl lg:text-3xl;
  }

  .payment-section {
    @apply p-4;
  }
}

@media (max-width: 768px) {
  .event-list-main {
    @apply grid-cols-1 gap-4;
    padding: 0 1rem;
  }

  .event-card-main {
    @apply shadow-md;
  }

  .event-details-grid {
    @apply grid-cols-1;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .event-list-main {
    @apply grid-cols-2 gap-5;
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .event-list-main {
    @apply grid-cols-3 gap-6;
    padding: 0 2rem;
  }
}

@media (min-width: 1280px) {
  .event-list-main {
    @apply grid-cols-3 gap-8;
    padding: 0 2rem;
  }
}

/* Focus and Accessibility Improvements */
.events-filter-button:focus,
.payment-button:focus,
.event-button:focus {
  @apply ring-2 ring-GTI-BLUE-default ring-opacity-50 outline-none;
}

.event-card-main:focus,
.event-card-main:focus-within {
  @apply ring-2 ring-GTI-BLUE-default ring-opacity-50 outline-none;
}

.events-filter-dropdown-item:focus {
  @apply bg-GTI-BLUE-default text-white outline-none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .event-card-main {
    @apply border-2 border-gray-900;
  }

  .events-filter-button {
    @apply border-2 border-gray-900;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .event-card-main,
  .events-filter-button,
  .payment-button,
  .event-card-img img {
    @apply transition-none;
  }

  .animate-pulse {
    animation: none;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .event-card-main:hover {
    @apply transform-none shadow-lg;
  }

  .event-card-img img {
    @apply transform-none;
  }
}

/* Loading States */
.event-card-skeleton {
  @apply animate-pulse bg-gray-200 rounded-2xl;
}

.event-card-skeleton-img {
  @apply bg-gray-300 rounded-t-2xl;
  height: 220px;
}

.event-card-skeleton-content {
  @apply p-6 space-y-4;
}

.event-card-skeleton-title {
  @apply h-6 bg-gray-300 rounded;
}

.event-card-skeleton-text {
  @apply h-4 bg-gray-300 rounded;
}

.event-card-skeleton-button {
  @apply h-10 bg-gray-300 rounded-xl;
}
