import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Helmet } from "react-helmet";
import { useDetectClickOutside } from "react-detect-click-outside";

import events from "../../assests/images/events.png";
import { LIMIT, SKIP, title, metaData } from "../constants";
import EventList from "./EventsList";
import { getSector } from "../../store/actioncreators/sectoractions";
import { getSubSector } from "../../store/actioncreators/sub-sectoractions";

const Events = () => {
  const dispatch: Dispatch<any> = useDispatch();

  useEffect(() => {
    dispatch(getSector());
    dispatch(getSubSector());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const eventTypeList = ["Online", "Offline", "Videos"];

  const [page, setPage] = useState({
    skip: SKIP,
    limit: LIMIT,
  });

  const [eventType, setEventType] = useState({
    drop: false,
    selected: "All Events",
    id: "",
  });

  const ref1 = useDetectClickOutside({
    onTriggered: () => {
      setEventType({ ...eventType, drop: false });
    },
  });

  useEffect(() => {
    setEventType({
      ...eventType,
      selected: "All Events",
      id: "",
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex flex-col w-full min-h-screen bg-gray-50">
      <Helmet>
        <title>{title.EVENTS}</title>
        <meta name="description" key="description" content={metaData.EVENTS} />
        <meta name="title" key="title" content="Events" />
        <meta property="og:title" content="Events" />
        <meta property="og:description" content={metaData.EVENTS} />
        <meta property="og:image" content={events} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/events`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Events" />
        <meta name="twitter:description" content={metaData.EVENTS} />
        <meta name="twitter:image" content={events} />
        <meta name="twitter:card" content="Events" />
      </Helmet>

      {/* Modern Header Section */}
      <div className="events-page-header">
        <div className="events-page-content">
          <div className="events-page-title">
            <div className="flex items-center justify-center w-16 h-16 bg-white/20 rounded-2xl shadow-lg">
              <img
                src={events}
                alt="Events"
                className="events-page-title-icon"
              />
            </div>
            <div>
              <h1 className="events-page-title-text">Events</h1>
              <p className="text-blue-100 mt-2 font-roboto">
                Discover innovation partnerships and technology collaborations
              </p>
            </div>
          </div>
          <p className="events-page-description">
            GTI® team regularly hosts curated events to help governments,
            corporates and enterprises gain access to new markets, technologies,
            facilitate innovation partnerships and participate in research
            collaborations. To facilitate technology partnerships, knowledge
            exchange, education and capacity building, GTI® team hosts these
            events with hands-on support and assistance from our team to
            seamlessly and efficiently take forward your business needs.
          </p>
        </div>
      </div>

      {/* Modern Filter Section */}
      <div className="events-filters-container">
        <div className="events-filters-content">
          <div className="events-filter-dropdown" ref={ref1}>
            <button
              id="dropdownDefault"
              data-dropdown-toggle="dropdown"
              className="events-filter-button"
              type="button"
              onClick={() => {
                setEventType({ ...eventType, drop: !eventType.drop });
              }}
            >
              <span>{eventType.selected}</span>
              <svg
                className={`w-4 h-4 transition-transform duration-200 ${
                  eventType.drop ? "rotate-180" : ""
                }`}
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            {eventType.drop && (
              <div className="events-filter-dropdown-menu">
                <div
                  className="events-filter-dropdown-item"
                  onClick={() => {
                    setEventType({
                      drop: false,
                      selected: "All Events",
                      id: "",
                    });
                  }}
                >
                  All Events
                </div>
                {eventTypeList &&
                  eventTypeList.map((item, id) => {
                    return (
                      <div
                        key={id}
                        className="events-filter-dropdown-item"
                        onClick={() => {
                          setEventType({
                            id: id.toString(),
                            drop: false,
                            selected: item,
                          });
                        }}
                      >
                        {item}
                      </div>
                    );
                  })}
              </div>
            )}
          </div>

          <div className="text-sm text-gray-600">
            Showing events for:{" "}
            <span className="font-semibold text-GTI-BLUE-default">
              {eventType.selected}
            </span>
          </div>
        </div>
      </div>

      {/* Events List */}
      <div className="flex-1 py-8">
        <EventList
          skip={page.skip}
          limit={page.limit}
          eventType={eventType.selected}
        />
      </div>
    </div>
  );
};
export default Events;
