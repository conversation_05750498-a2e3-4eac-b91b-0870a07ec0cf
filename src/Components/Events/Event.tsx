import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import axios from "axios";
import { Helmet } from "react-helmet";

import { store } from "../../store";
import {
  registerEvent,
  registerEventPaymentLink,
} from "../../store/actioncreators/eventactionss";
import eventsDefault from "../../assests/images/events-default.jpg";
import gbi_home_logo from "../../assests/home/<USER>";
import { eventItemsFetched } from "../constants";
import { Company } from "../../shared/constants";
import TechnologyOpportunityRequiredPopUp from "../../shared/TechnologyOpportunityRequiredPopUp";
import { getYourProductCount } from "../../store/actioncreators/productactions";
import { getYourOppportunityCount } from "../../store/actioncreators/opportunityactions";

const Event = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  const dispatch: Dispatch<any> = useDispatch();
  const { id } = useParams();
  const navigate = useNavigate();
  const state = {
    id: id ?? "",
  };

  let [event, setEvent] = useState<eventItemsFetched>({
    _id: "",
    topic: "",
    description: "",
    shortDescription: "",
    sectorId: "",
    subSectorId: "",
    eventType: "",
    imageUrl: "",
    startDate: "",
    endDate: "",
    externalLink: "",
    youtubeLink: "",
    webinarKey: "",
    webinarOrganizerKey: "",
    organizedBy: "",
    webinarRegistrationLink: "",
    createdAt: "",
    startTime: "",
    endTime: "",
    meetingLink: "",
    price: 0,
    videoUrl: "",
    __v: -1,
  });

  let [registered, setRegister] = useState(false);
  const user: USER = useSelector((state: STATE) => state.USER.USER);
  const [
    technologyOpportunityRequiredPopUp,
    setTechnologyOpporunityRequiredPopUp,
  ] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState("");

  const loadEvent = (id: string) => {
    var config = {
      method: "PUT",
      url: `${process.env.REACT_APP_BASE_API}/events/${id}`,
      headers: {
        Authorization: `Bearer ${store.getState().USER.USER.token}`,
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setEvent(response.data);
      })
      .catch(function (error) {});
  };

  const getRegistration = () => {
    const token = localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";

    let config = {
      method: "GET",
      url: `${process.env.REACT_APP_BASE_API}/events/isUserRegistered/${state.id}`,
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setRegister(response.data);
      })
      .catch(function (error) {});
  };

  useEffect(() => {
    loadEvent(state.id);
    getRegistration();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [registered]);

  const DOS = new Date(event?.startDate);
  const DOE = new Date(event?.endDate);

  const paymentHandler = async (
    amount: number,
    currency: string,
    order_id: string,
    promotionId: string
  ) => {
    let options = {
      key: process.env.REACT_APP_RAZORPAY_KEY_ID,
      amount,
      currency,
      name: Company.NAME,
      description: Company.DESCRIPTION,
      image: gbi_home_logo,
      order_id,
      callback_url: `${process.env.REACT_APP_BASE_API}/payments/validate`,
      prefill: {
        name: user?.user?.name,
        email: user?.user?.email,
      },
      notes: {
        promotionId,
      },
      theme: {
        color: "#3399cc",
      },
    };

    let rzp1 = new (window as any).Razorpay(options);

    rzp1.on("payment.failed", function (response: any) {
      setIsProcessingPayment(false);
      setPaymentError("Payment failed. Please try again.");
      navigate("/featured/failed");
    });

    rzp1.on("payment.success", function (response: any) {
      setIsProcessingPayment(false);
    });

    rzp1.open();
  };

  const handleRegister = async () => {
    if (user.admin !== -1) {
      if (!registered) {
        const productsCount = await getYourProductCount();
        const opportunitiesCount = await getYourOppportunityCount();
        if (productsCount === 0 && opportunitiesCount === 0) {
          setTechnologyOpporunityRequiredPopUp(true);
        } else if (event?.webinarRegistrationLink) {
          window.open(event?.webinarRegistrationLink, "_blank");
        } else if (event?.price) {
          try {
            setIsProcessingPayment(true);
            setPaymentError("");
            const { order_id, currency, amount, promotionId } =
              await registerEventPaymentLink(event?._id);

            paymentHandler(amount, currency, order_id, promotionId);
          } catch (error) {
            setPaymentError("Failed to initiate payment. Please try again.");
            setIsProcessingPayment(false);
          }
        } else {
          dispatch(registerEvent(event?._id));
          setRegister(true);
        }
      } else if (event?.meetingLink) {
        dispatch(registerEvent(event?._id));
        window.open(event?.meetingLink, "_blank");
      } else if (event?.youtubeLink) {
        dispatch(registerEvent(event?._id));
        window.open(event?.youtubeLink, "_blank");
      } else if (event?.price) {
        navigate(`/eventvideo/${event._id}`);
      }
      return;
    }
    handleLoginModal();
  };

  const fetchButtonText = () => {
    let text = "";
    if (user.admin !== -1) {
      if (registered) {
        if (event?.youtubeLink) {
          text = "View Recording";
        } else if (event?.meetingLink) {
          text = "Join Meeting";
        } else if (event?.price) {
          text = "View Content";
        } else {
          text = "Already registered";
        }
      } else if (event?.price) {
        text = `Pay $${event?.price} to Register`;
      } else {
        text = "Register";
      }
    } else {
      text = "Create Profile / Register";
    }

    return text;
  };

  const isEventPaid = event.price && event.price > 0;
  const eventStatus =
    DOS > new Date()
      ? "upcoming"
      : DOS.toDateString() === new Date().toDateString()
      ? "today"
      : "ongoing";
  const isEventEnded = DOE < new Date();

  return (
    <React.Fragment>
      <div className="event-main">
        <Helmet>
          <title>{event.topic?.replace(/(<([^>]+)>)/gi, "")}</title>
          <meta
            name="description"
            key="description"
            content={event.description?.replace(/(<([^>]+)>)/gi, "")}
          />
          <meta name="title" key="title" content={event.topic} />
          <meta property="og:title" content={event.topic} />
          <meta property="og:description" content={event.description} />
          <meta
            property="og:image"
            content={event.imageUrl ? event.imageUrl : eventsDefault}
          />
          <meta
            property="og:url"
            content={`${process.env.REACT_APP_BASE_URL}/events/${event._id}`}
          />
          <meta property="og:type" content="website" />
          <meta name="twitter:title" content={event.topic} />
          <meta name="twitter:description" content={event.description} />
          <meta
            name="twitter:image"
            content={event.imageUrl ? event.imageUrl : eventsDefault}
          />
          <meta name="twitter:card" content={event.topic} />
        </Helmet>

        {/* Hero Section */}
        <div className="event-hero-section">
          <img
            src={event.imageUrl ? event.imageUrl : eventsDefault}
            className="event-hero-image"
            alt={event.topic?.replace(/(<([^>]+)>)/gi, "")}
            loading="lazy"
          />
          <div className="event-hero-overlay"></div>

          {/* Event Status Badge */}
          <div className="absolute top-8 left-8 z-10">
            <span
              className={`px-4 py-2 text-sm font-semibold rounded-full ${
                eventStatus === "upcoming"
                  ? "bg-green-100 text-green-800"
                  : eventStatus === "today"
                  ? "bg-yellow-100 text-yellow-800"
                  : isEventEnded
                  ? "bg-gray-100 text-gray-800"
                  : "bg-blue-100 text-blue-800"
              }`}
            >
              {eventStatus === "upcoming"
                ? "Upcoming Event"
                : eventStatus === "today"
                ? "Event Today"
                : isEventEnded
                ? "Event Ended"
                : "Live Event"}
            </span>
          </div>
        </div>

        {/* Content Container */}
        <div className="event-content-container">
          <div className="event-content-card">
            {/* Event Header */}
            <div className="event-header">
              <h1
                className="event-title"
                dangerouslySetInnerHTML={{
                  __html: event.topic?.replace(/(<([^>]+)>)/gi, "") || "",
                }}
              ></h1>

              <div className="event-meta">
                <div className="event-meta-item">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  <span>
                    {DOS.toLocaleString("default", {
                      month: "long",
                      day: "2-digit",
                      year: "numeric",
                    })}
                  </span>
                </div>

                <div className="event-meta-item">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <span>{event.eventType || "Event"}</span>
                </div>

                {event.organizedBy && (
                  <div className="event-meta-item">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                      />
                    </svg>
                    <span>Organized by {event.organizedBy}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Event Details Grid */}
            <div className="event-details-grid">
              <div className="event-detail-item">
                <div className="event-detail-label">Start Date & Time</div>
                <div className="event-detail-value">
                  {DOS.toLocaleString("default", {
                    month: "long",
                    day: "2-digit",
                    year: "numeric",
                  })}{" "}
                  | {event.startTime}
                </div>
              </div>

              <div className="event-detail-item">
                <div className="event-detail-label">End Date & Time</div>
                <div className="event-detail-value">
                  {DOE.toString() !== "Invalid Date"
                    ? DOE.toLocaleString("default", {
                        month: "long",
                        day: "2-digit",
                        year: "numeric",
                      })
                    : DOS.toLocaleString("default", {
                        month: "long",
                        day: "2-digit",
                        year: "numeric",
                      })}{" "}
                  | {event.endTime}
                </div>
              </div>
            </div>

            {/* Event Description */}
            <div className="event-description">
              <div
                dangerouslySetInnerHTML={{ __html: event.description }}
              ></div>
            </div>

            {/* Payment/Registration Section */}
            {isEventPaid ? (
              <div className="payment-section">
                <div className="payment-header">
                  <h3 className="payment-title">Event Registration</h3>
                  <p className="payment-subtitle">
                    Secure your spot at this exclusive event
                  </p>
                </div>

                <div className="payment-price">${event.price}</div>

                {paymentError && (
                  <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-xl text-red-700 text-sm">
                    {paymentError}
                  </div>
                )}

                <button
                  type="button"
                  className={`payment-button ${
                    isProcessingPayment ? "opacity-75 cursor-not-allowed" : ""
                  }`}
                  onClick={handleRegister}
                  disabled={isEventEnded || isProcessingPayment}
                >
                  {isProcessingPayment ? (
                    <div className="flex items-center justify-center space-x-2">
                      <svg
                        className="animate-spin h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <span>Processing...</span>
                    </div>
                  ) : isEventEnded ? (
                    "Event Ended"
                  ) : (
                    fetchButtonText()
                  )}
                </button>

                <div className="payment-features">
                  <div className="payment-feature">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>Instant access after payment</span>
                  </div>
                  <div className="payment-feature">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>Secure payment processing</span>
                  </div>
                  <div className="payment-feature">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span>Event materials included</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center">
                {paymentError && (
                  <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-xl text-red-700 text-sm">
                    {paymentError}
                  </div>
                )}

                <button
                  type="button"
                  className={`w-full bg-GTI-BLUE-default text-white font-semibold py-4 px-8 rounded-xl hover:bg-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none ${
                    isProcessingPayment ? "opacity-75 cursor-not-allowed" : ""
                  }`}
                  onClick={handleRegister}
                  disabled={isEventEnded || isProcessingPayment}
                >
                  {isProcessingPayment ? (
                    <div className="flex items-center justify-center space-x-2">
                      <svg
                        className="animate-spin h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <span>Processing...</span>
                    </div>
                  ) : isEventEnded ? (
                    "Event Ended"
                  ) : (
                    fetchButtonText()
                  )}
                </button>
              </div>
            )}
          </div>
        </div>

        {technologyOpportunityRequiredPopUp && (
          <TechnologyOpportunityRequiredPopUp
            userType={user?.userType}
            setTechnologyOpporunityRequiredPopUp={
              setTechnologyOpporunityRequiredPopUp
            }
          />
        )}
      </div>
    </React.Fragment>
  );
};

export default Event;
