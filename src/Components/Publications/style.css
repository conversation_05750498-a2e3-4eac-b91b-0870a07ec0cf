/* Modern Publications & News Styles */

/* Enhanced <PERSON>ton Styles */
.button {
  @apply font-medium rounded-lg text-sm px-5 py-2.5 mx-1 mb-2 focus:outline-none transition-all duration-200 transform hover:scale-105;
}

.active {
  @apply bg-GTI-BLUE-default text-white hover:bg-blue-800 shadow-lg hover:shadow-xl;
}

.not-active {
  @apply bg-white text-GTI-BLUE-default border-2 border-slate-200 hover:border-GTI-BLUE-default hover:shadow-md;
}

/* Modern Card Animations */
.publication-card {
  @apply transition-all duration-300 ease-in-out;
}

.publication-card:hover {
  @apply transform -translate-y-2 shadow-2xl;
}

/* Enhanced Modal Styles */
.modal-backdrop {
  @apply fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center;
}

.modal-content {
  @apply bg-white rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto;
}

/* Modern Form Styles */
.modern-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-GTI-BLUE-default focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white;
}

.modern-textarea {
  @apply w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-GTI-BLUE-default focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white min-h-[120px];
  resize: vertical;
}

/* Enhanced Button Variants */
.btn-primary {
  @apply bg-GTI-BLUE-default text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-800 focus:ring-4 focus:ring-GTI-BLUE-default/20 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 px-6 py-3 rounded-xl font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200 transform hover:scale-105;
}

.btn-danger {
  @apply bg-red-500 text-white px-6 py-3 rounded-xl font-medium hover:bg-red-600 focus:ring-4 focus:ring-red-200 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

/* Loading States */
.loading-shimmer {
  @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%];
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design Enhancements */
@media (max-width: 640px) {
  .modal-content {
    @apply mx-2 rounded-xl;
  }

  .btn-primary,
  .btn-secondary,
  .btn-danger {
    @apply px-4 py-2 text-sm;
  }
}
