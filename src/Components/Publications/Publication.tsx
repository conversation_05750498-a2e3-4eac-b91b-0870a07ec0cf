import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import axios from "axios";
import { Helmet } from "react-helmet";

import { publicationFetched } from "../constants";
import articlebanner from "../../assests/banners/articlebanner.png";
import PdfDownloader from "../../shared/PdfDownloader";

const Publication = ({
  handleLoginModal,
}: {
  handleLoginModal: () => void;
}) => {
  let [publication, setPublications] = useState<publicationFetched>({
    _id: "",
    topic: "",
    imageUrl: "",
    description: "",
    documentUrl: "",
    displayOnHomePage: false,
    isDeleted: false,
    createdAt: "",
    updatedAt: "",
  });
  const { id } = useParams();

  const loadPublication = (id: string) => {
    var config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/publications/${id}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setPublications(response.data);
      })
      .catch(function (error) {});
  };

  useEffect(() => {
    loadPublication(id ?? "");
  }, [id]);
  const DOC = new Date(publication.createdAt);

  return (
    <React.Fragment>
      <div
        className="flex flex-col w-full items-center bg-slate-100"
        style={{ msOverflowStyle: "none", scrollbarWidth: "none" }}
      >
        <Helmet>
          <title>{publication.topic}</title>
          <meta
            name="description"
            key="description"
            content={publication.description}
          />
          <meta name="title" key="title" content={publication?.topic} />
          <meta property="og:title" content={publication.topic} />
          <meta property="og:description" content={publication.description} />
          <meta
            property="og:image"
            content={
              publication.imageUrl ? publication.imageUrl : articlebanner
            }
          />
          <meta
            property="og:url"
            content={`${process.env.REACT_APP_BASE_URL}/publications/${publication._id}`}
          />
          <meta property="og:type" content="website" />
          <meta name="twitter:title" content={publication?.topic} />
          <meta name="twitter:description" content={publication?.description} />
          <meta
            name="twitter:image"
            content={
              publication.imageUrl ? publication.imageUrl : articlebanner
            }
          />
          <meta name="twitter:card" content={publication?.topic} />
        </Helmet>
        <article className="max-w-4xl mx-auto bg-white rounded-3xl shadow-xl overflow-hidden mt-8 mb-12">
          {/* Hero Image */}
          {publication.imageUrl && (
            <div className="relative h-64 md:h-96 overflow-hidden">
              <img
                src={publication.imageUrl}
                className="w-full h-full object-cover"
                alt={publication.topic}
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
            </div>
          )}

          {/* Content */}
          <div className="p-8 md:p-12">
            {/* Header */}
            <header className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className="bg-GTI-BLUE-default/10 rounded-lg px-3 py-1">
                    <span className="text-GTI-BLUE-default text-sm font-medium">
                      Publication
                    </span>
                  </div>
                </div>
                <time className="text-gray-500 text-sm">
                  {DOC.toLocaleString("default", {
                    month: "long",
                    day: "2-digit",
                    year: "numeric",
                  })}
                </time>
              </div>

              <h1 className="text-3xl md:text-5xl font-bold text-gray-900 leading-tight mb-4">
                {publication.topic?.replace(/(<([^>]+)>)/gi, "")}
              </h1>
            </header>

            {/* Content Body */}
            <div
              className="max-w-none text-gray-700 leading-relaxed mb-8 text-lg"
              style={{ lineHeight: "1.7" }}
              dangerouslySetInnerHTML={{ __html: publication.description }}
            ></div>

            {/* Download Section */}
            <div className="border-t border-gray-200 pt-8">
              <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    Download Publication
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Access the full document and research materials
                  </p>
                </div>
                <PdfDownloader
                  url={publication?.documentUrl}
                  className="btn-primary flex items-center space-x-2"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <span>Download Document</span>
                </PdfDownloader>
              </div>
            </div>
          </div>
        </article>
      </div>
    </React.Fragment>
  );
};

export default Publication;
