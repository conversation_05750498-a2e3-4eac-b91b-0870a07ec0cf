import React, { Dispatch, useEffect, useState, useRef } from "react";
import ReactDOM from "react-dom";
import { Editor } from "@tinymce/tinymce-react";
import axios from "axios";
import { useDispatch } from "react-redux";
import { Helmet } from "react-helmet";
import {
  BookOpenIcon,
  DocumentTextIcon,
  PlusIcon,
} from "@heroicons/react/24/outline";

import article from "../../assests/images/articles.png";
import {
  CONTENT_TYPE,
  presignedData,
  publicationCreate,
  title,
  metaData,
} from "../constants";
import PublicationsList from "./PublicationsList";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import { createPublication } from "../../store/actioncreators/publicationActions";
import "./style.css";
import globe from "../../assests/home/<USER>";
import CustomEditor from "../shared/CustomEditor";

type publicationModalItem = {
  topic: string;
  description: string;
  documentUrl: File | null;
  imageUrl: File | null;
};

const PublicationModal = ({ handleModel }: { handleModel: () => void }) => {
  const editorRef2 = useRef<any>(null);
  const dispatch: Dispatch<any> = useDispatch();

  const [publicationModal, setModalData] = useState<publicationModalItem>({
    topic: "",
    description: "",
    imageUrl: null,
    documentUrl: null,
  });

  const handleImageChange = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files;

    if (!fileList) return;

    setModalData({ ...publicationModal, imageUrl: fileList[0] });
  };
  const handleDocChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = e.target.files;

    if (!fileList) return;

    setModalData({ ...publicationModal, documentUrl: fileList[0] });
  };
  const getPresigned = async (content: presignedData) => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data: data,
    };

    await axios(config)
      .then(function (response) {
        result = response.data;
      })
      .catch(function (error) {
        result = "error";
        dispatch(failToast());
      });

    return result;
  };

  const postFile = async (signed: string) => {
    var config = {
      method: "put",
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: publicationModal.imageUrl,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const handleCreate = async () => {
    let signedURL: string = "",
      signedDocUrl = "";

    if (
      !publicationModal.imageUrl?.size ||
      !publicationModal?.documentUrl?.size
    )
      return;

    const signedData: presignedData = {
      fileName: publicationModal.imageUrl?.name || publicationModal.topic,
      filePath: "publication_image",
      fileType: publicationModal.imageUrl?.type ?? "png",
    };
    const signedDocData: presignedData = {
      fileName: publicationModal.documentUrl?.name || publicationModal.topic,
      filePath: "publication_doc",
      fileType: publicationModal.documentUrl?.type ?? "*",
    };
    if (publicationModal.imageUrl?.size) {
      signedURL = await getPresigned(signedData);
      postFile(signedURL);
    }
    if (publicationModal.documentUrl?.size) {
      signedDocUrl = await getPresigned(signedDocData);
      postFile(signedDocUrl);
    }

    const data: publicationCreate = {
      description: publicationModal?.description,
      documentUrl: signedDocUrl.split("?")[0] ?? "",
      imageUrl: signedURL.split("?")[0] ?? "",
      topic: publicationModal?.topic,
    };

    await dispatch(createPublication(data));
  };
  useEffect(() => {
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, []);
  const content = (
    <div className="modal-backdrop">
      <div className="modal-content">
        {/* Modern Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-GTI-BLUE-default/10 rounded-xl p-3">
              <PlusIcon className="h-6 w-6 text-GTI-BLUE-default" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                Create New Publication
              </h2>
              <p className="text-gray-600 text-sm">
                Share your research and insights with the community
              </p>
            </div>
          </div>
          <button
            onClick={() => {
              handleModel();
            }}
            className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200 group"
          >
            <svg
              className="w-5 h-5 text-gray-400 group-hover:text-gray-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Modern Form */}
        <div className="p-6">
          <form
            className="space-y-6"
            onSubmit={(e) => {
              e.preventDefault();
              handleCreate();
            }}
          >
            {/* Title Input */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Publication Title
              </label>
              <input
                type="text"
                onChange={(e) => {
                  setModalData({
                    ...publicationModal,
                    topic: e.target.value,
                  });
                }}
                required
                className="modern-input"
                placeholder="Enter publication title..."
              />
            </div>

            {/* Description Editor */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <CustomEditor
                onChange={(content: string) => {
                  setModalData({
                    ...publicationModal,
                    description: content,
                  });
                }}
              />
            </div>

            {/* File Uploads */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Image Upload */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Cover Image
                </label>
                <div className="relative">
                  <input
                    onChange={(e) => {
                      handleImageChange(e);
                    }}
                    type="file"
                    accept={CONTENT_TYPE}
                    required
                    className="modern-input file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-GTI-BLUE-default file:text-white hover:file:bg-blue-800"
                  />
                  <p className="text-xs text-gray-500 mt-1">PNG files only</p>
                </div>
              </div>

              {/* Document Upload */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Document
                </label>
                <div className="relative">
                  <input
                    onChange={(e) => {
                      handleDocChange(e);
                    }}
                    type="file"
                    required
                    className="modern-input file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-GTI-BLUE-default file:text-white hover:file:bg-blue-800"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Upload publication document
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => {
                  handleModel();
                }}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button type="submit" className="btn-primary">
                Create Publication
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
  return ReactDOM.createPortal(content, document.body);
};

const Publications = () => {
  return (
    <div className="flex flex-col relative w-full min-h-screen bg-gray-50">
      <Helmet>
        <title>{title.PUBLICATIONS}</title>
        <meta
          name="description"
          key="description"
          content={metaData.PUBLICATIONS}
        />
        <meta name="title" key="title" content={title.PUBLICATIONS} />
        <meta property="og:title" content={title.PUBLICATIONS} />
        <meta property="og:description" content={metaData.PUBLICATIONS} />
        <meta property="og:image" content={globe} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/publications`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content={title?.PUBLICATIONS} />
        <meta name="twitter:description" content={metaData?.PUBLICATIONS} />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content={title?.PUBLICATIONS} />
      </Helmet>

      {/* Modern Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-GTI-BLUE-default via-blue-700 to-indigo-800">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
          <div className="text-center">
            <div className="flex justify-center items-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-white/20 rounded-full blur-xl"></div>
                <div className="relative bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                  <DocumentTextIcon className="h-12 w-12 md:h-16 md:w-16 text-white" />
                </div>
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight">
              Publications
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              Discover cutting-edge research, insights, and innovations from the
              global technology community
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl px-6 py-3 border border-white/20">
                <span className="text-white/90 text-sm font-medium">
                  Latest Research & Publications
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
        </div>
      </div>

      {/* Content Section */}
      <div className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <PublicationsList />
      </div>
    </div>
  );
};
export default Publications;
