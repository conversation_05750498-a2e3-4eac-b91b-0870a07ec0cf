import React, { Dispatch, useEffect, useRef, useState } from "react";
import ReactDOM from "react-dom";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import {
  CONTENT_TYPE,
  LIMIT,
  presignedData,
  publicationFetched,
  publicationUpdate,
} from "../constants";
import axios from "axios";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import { useNavigate } from "react-router-dom";
import { Editor } from "@tinymce/tinymce-react";
import { ScreenSpinner } from "../utils/loader";
import { getQueryParams } from "../../utils";
import {
  getPublications,
  updatePublication,
} from "../../store/actioncreators/publicationActions";
import CustomEditor from "../shared/CustomEditor";

type publicationsModalItem = {
  _id: string;
  topic: string;
  description: string;
  imageUrl: File | null;
  documentUrl: File | null;
  displayOnHomePage: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
};

const PublicationsEditModel = ({
  currPublication,
  handleModelUpdate,
}: {
  currPublication: publicationFetched;
  handleModelUpdate: () => void;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const editorRef2 = useRef<any>(null);
  const [publicationModal, setModalData] = useState<publicationsModalItem>({
    ...currPublication,
    imageUrl: null,
    documentUrl: null,
  });
  const handleImageChange = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files;

    if (!fileList) return;

    setModalData({ ...publicationModal, imageUrl: fileList[0] });
  };
  const handleDocumentChange = function (
    e: React.ChangeEvent<HTMLInputElement>
  ) {
    const fileList = e.target.files;

    if (!fileList) return;

    setModalData({ ...publicationModal, documentUrl: fileList[0] });
  };

  const getPresigned = async (content: presignedData): Promise<string> => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data: data,
    };

    await axios(config)
      .then(function (response) {
        result = response.data;
        postFile(result);
      })
      .catch(function (error) {
        result = "error";
        dispatch(failToast());
      });

    return result;
  };

  const postFile = async (signed: string) => {
    var config = {
      method: "put",
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
      },
      data: publicationModal.imageUrl,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const handleUpdate = async () => {
    let signedURL: string = "";
    const signedData: presignedData = {
      fileName: publicationModal.imageUrl?.name || publicationModal.topic,
      filePath: "publication_image",
      fileType: "png",
    };
    if (publicationModal.imageUrl) {
      signedURL = await getPresigned(signedData);
      postFile(signedURL);
    }
    const data: publicationUpdate = {
      topic: publicationModal.topic,
      description: publicationModal.description,
      imageUrl: publicationModal.imageUrl
        ? signedURL.split("?")[0]
        : currPublication.imageUrl,
      documentUrl: publicationModal.documentUrl
        ? signedURL.split("?")[0]
        : currPublication.imageUrl,
      publicationId: publicationModal?._id,
    };
    await dispatch(updatePublication(data));
    handleModelUpdate();
  };

  useEffect(() => {
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, []);

  const content = (
    <div className="z-10 pb-[200px] pt-4 fixed w-full h-screen bg-slate-700 bg-opacity-70 top-0 left-0 flex justify-center overflow-auto">
      <div
        className={
          "duration-200 ease-in-out w-2/5 h-fit flex flex-col space-y-1 justify-evenly text-center bg-white shadow-lg p-10  shadow-GTI-BLUE-default rounded "
        }
      >
        <div className="flex flex-row justify-center relative">
          <h4 className="font-roboto text-xl pt-5 text-GTI-BLUE-default">
            Edit Publication
          </h4>
          <button
            onClick={() => {
              handleModelUpdate();
            }}
            className="absolute right-0 -top-5 font-bold hover:text-red-500 duration-300 border border-slate-100 px-3 py-1 rounded"
          >
            X
          </button>
        </div>
        <div className="flex flex-col w-full">
          <div className="flex flex-col space-y-3">
            <div className="flex flex-col justify-center space-y-3">
              <input
                type="text"
                defaultValue={publicationModal.topic}
                onChange={(e) => {
                  setModalData({
                    ...publicationModal,
                    topic: e.target.value,
                  });
                }}
                aria-label="Title"
                className="mb-6 bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                placeholder="Title"
              />
              <CustomEditor
                onChange={(content: string) => {
                  setModalData({
                    ...publicationModal,
                    description: content,
                  });
                }}
              />
              <input
                onChange={(e) => {
                  handleImageChange(e);
                }}
                type="file"
                accept={CONTENT_TYPE}
                aria-label="Click to uplod photos(*png only)"
                className="mb-6 bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                placeholder="Click to uplod photos(*png only)"
              />
              <input
                onChange={(e) => {
                  handleDocumentChange(e);
                }}
                accept=".pdf"
                type="file"
                aria-label="Click to uplod document(.pdf only)"
                className="mb-6 bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 "
                placeholder="Click to uplod document(.pdf only)"
              />
            </div>
            <div className="flex justify-center mx-10">
              <button
                type="button"
                className="button active"
                onClick={() => {
                  handleUpdate();
                }}
              >
                Update
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  return ReactDOM.createPortal(content, document.body);
};

const Card = ({
  item,
  handleModel,
}: {
  item: publicationFetched;
  handleModel: (data: publicationFetched) => void;
}) => {
  const DOC = new Date(item.createdAt);
  const navigate = useNavigate();

  const handleView = () => {
    navigate(`/publications/${item._id}`, { state: { id: item._id } });
  };

  return (
    <article
      className="group relative bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-xl hover:shadow-GTI-BLUE-default/10 transition-all duration-300 cursor-pointer transform hover:-translate-y-1 h-full"
      onClick={handleView}
    >
      {/* Image Container */}
      <div className="relative aspect-[16/10] overflow-hidden bg-gray-100">
        {item.imageUrl ? (
          <>
            <img
              src={item?.imageUrl ?? ""}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              alt={item.topic?.replace(/(<([^>]+)>)/gi, "")}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </>
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-GTI-BLUE-default/10 to-blue-100 flex items-center justify-center">
            <DocumentTextIcon className="h-16 w-16 text-GTI-BLUE-default/40" />
          </div>
        )}

        {/* Date Badge */}
        <div className="absolute top-4 left-4">
          <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1 shadow-sm">
            <time className="text-xs font-medium text-GTI-BLUE-default">
              {DOC.toLocaleString("default", {
                month: "short",
                day: "2-digit",
                year: "numeric",
              })}
            </time>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-GTI-BLUE-default transition-colors duration-200">
          {item.topic?.replace(/(<([^>]+)>)/gi, "")}
        </h3>
        <div
          className="text-gray-600 text-sm line-clamp-3 leading-relaxed"
          dangerouslySetInnerHTML={{
            __html: item.description?.split("<br>")?.join(""),
          }}
        ></div>

        {/* Read More Indicator */}
        <div className="mt-4 flex items-center text-GTI-BLUE-default text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <span>Read Publication</span>
          <svg
            className="ml-1 w-4 h-4 transition-transform group-hover:translate-x-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </div>
      </div>
    </article>
  );
};

const PublicationsList = () => {
  const dispatch: Dispatch<any> = useDispatch();
  const { publications, publicationsCount }: any = useSelector(
    (state: STATE) => state.PUBLICATION
  );
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);

  const [publicationModal, setPublicationModal] = useState(false);
  const [currPublication, setCurrPublication] = useState<publicationFetched>({
    topic: "",
    description: "",
    documentUrl: "",
    imageUrl: "",
    _id: "",
    displayOnHomePage: false,
    isDeleted: false,
    createdAt: "",
    updatedAt: "",
  });
  const navigate = useNavigate();
  const handleModelUpdate = async () => {
    setPublicationModal(!publicationModal);
    dispatch(getPublications(page.skip, page.limit));
  };
  const skip = getQueryParams("skip") ?? "0";
  const [maxSkip, setMaxSkip] = useState(0);

  const handleModel = (item: publicationFetched) => {
    setCurrPublication(item);
    setPublicationModal(!publicationModal);
  };

  const [page, setPage] = useState({
    skip: skip ? skip : "0",
    limit: LIMIT,
  });

  useEffect(() => {
    setMaxSkip(Math.ceil(publicationsCount / parseInt(LIMIT)));
  }, [page, publicationsCount]);

  useEffect(() => {
    dispatch(getPublications(page.skip, page.limit));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page]);

  const fetchData = (val: number) => {
    let newSkip = parseInt(page.skip) + val;
    if (newSkip >= 0) {
      setPage({
        skip: newSkip + "",
        limit: page.limit,
      });
      navigate(`/publications?skip=${newSkip}`);
      window.scrollTo(0, 0);
    }
  };

  return (
    <div className="flex flex-col w-full">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <div>
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            Latest Publications
          </h2>
          <p className="text-gray-600">
            Explore our collection of research papers and publications
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-GTI-BLUE-default/10 text-GTI-BLUE-default">
            {publicationsCount} Publications
          </span>
        </div>
      </div>

      {spinner.SPINNER ? (
        <ScreenSpinner />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {publications.map((item: publicationFetched, id: number) => {
            return <Card item={item} key={id} handleModel={handleModel} />;
          })}
        </div>
      )}
      {publicationModal && (
        <PublicationsEditModel
          currPublication={currPublication}
          handleModelUpdate={handleModelUpdate}
        />
      )}
      {/* Modern Pagination */}
      {publicationsCount > parseInt(page.limit) && (
        <div className="flex justify-center mt-12">
          <nav className="flex items-center space-x-2">
            <button
              disabled={page.skip === "0"}
              onClick={() => {
                fetchData(-1);
              }}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-GTI-BLUE-default disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-700 transition-colors duration-200"
            >
              <svg
                className="mr-2 w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              Previous
            </button>
            {/* Page Numbers */}
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, maxSkip) }, (_, i) => {
                const pageNum = parseInt(page.skip) + i + 1;
                const isCurrentPage = i === 0;

                if (pageNum <= maxSkip) {
                  return (
                    <button
                      key={pageNum}
                      onClick={() => {
                        if (!isCurrentPage) fetchData(i);
                      }}
                      className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                        isCurrentPage
                          ? "bg-GTI-BLUE-default text-white"
                          : "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-GTI-BLUE-default"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                }
                return null;
              })}

              {maxSkip > parseInt(page.skip) + 5 && (
                <>
                  <span className="px-2 text-gray-500">...</span>
                  <button
                    onClick={() => {
                      fetchData(maxSkip - parseInt(page.skip) - 1);
                    }}
                    className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-GTI-BLUE-default transition-colors duration-200"
                  >
                    {maxSkip}
                  </button>
                </>
              )}
            </div>

            <button
              disabled={
                parseInt(page.limit) * (parseInt(page.skip) + 1) >=
                publicationsCount
              }
              onClick={() => {
                fetchData(1);
              }}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-GTI-BLUE-default disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-700 transition-colors duration-200"
            >
              Next
              <svg
                className="ml-2 w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default PublicationsList;
