import React, { useState, useRef, useCallback, useEffect } from "react";
import {
  FaBold,
  FaItalic,
  FaUnderline,
  FaStrikethrough,
  FaListOl,
  FaListUl,
  FaAlignLeft,
  FaAlignCenter,
  FaAlignRight,
  FaAlignJustify,
  FaLink,
  FaImage,
  FaUndo,
  FaRedo,
  FaCode,
  FaQuoteRight,
  FaEllipsisH,
  // FaPalette, // Optional: Could use an icon if preferred over just the color input
} from "react-icons/fa";

interface CustomEditorProps {
  onChange: (content: string) => void;
  initialContent?: string;
  toolbarFeatures?: string[];
}

const defaultToolbarFeatures = [
  "bold",
  "italic",
  "underline",
  "strikethrough",
  "heading", // Note: Heading functionality isn't implemented in the original code
  "fontSize",
  "textColor", // Added text color
  "orderedList",
  "unorderedList",
  "alignLeft",
  "alignCenter",
  "alignRight",
  "alignJustify",
  "link",
  "image",
  "undo",
  "redo",
  "codeBlock",
  "quote",
];

const CustomEditor: React.FC<CustomEditorProps> = ({
  onChange,
  initialContent = "",
  toolbarFeatures = defaultToolbarFeatures,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  // No need for separate content state if we always read from the editor on change
  // const [content, setContent] = useState<string>(initialContent);
  const [showMoreOptions, setShowMoreOptions] = useState<boolean>(false);
  const [currentColor, setCurrentColor] = useState<string>("#000000"); // Track current color for input default

  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== initialContent) {
      // Only set initial content if it's different from the current state
      // This helps prevent resetting content unnecessarily on re-renders
      // (Though in this simple setup, it might not be strictly needed, good practice)
      editorRef.current.innerHTML = initialContent;
    }
  }, [initialContent]); // Depend only on initialContent

  // Debounce or throttle might be useful here for performance on very rapid typing
  const handleContentChange = useCallback(() => {
    if (editorRef.current) {
      const currentContent = editorRef.current.innerHTML;
      // setContent(currentContent); // Can remove if not used elsewhere
      onChange(currentContent);
    }
  }, [onChange]);

  const executeCommand = (command: string, value?: string) => {
    // Ensure editor has focus before executing command
    editorRef.current?.focus();
    document.execCommand(command, false, value);
    handleContentChange(); // Update state/prop after command execution
  };

  const handleBold = () => executeCommand("bold");
  const handleItalic = () => executeCommand("italic");
  const handleUnderline = () => executeCommand("underline");
  const handleStrikethrough = () => executeCommand("strikeThrough");

  // --- Font Size Handling ---
  // Note: execCommand('fontSize', false, size) exists but uses values 1-7.
  // Using span with style is more reliable for specific px values.
  const handleFontSize = (size: string) => {
    if (editorRef.current) {
      editorRef.current?.focus(); // Ensure focus
      const selection = window.getSelection();
      if (!selection?.rangeCount || !size) return; // Do nothing if no selection or no size

      const range = selection.getRangeAt(0);

      // Check if the selection is collapsed (caret)
      if (range.collapsed) {
        // If collapsed, maybe apply style for future typing?
        // execCommand('fontSize') with values 1-7 could potentially do this,
        // but setting exact px size for future typing is complex.
        // For simplicity, we'll only apply to existing selection for now.
        console.warn("Font size applied only to selected text, not caret.");
        return;
      }

      // Check if the entire selection is already inside a span with the *same* font size
      // This is complex to check perfectly. A simpler approach is to just apply.
      // For removing size, one might need to find parent spans and remove/modify them.

      // Wrap selection in a span with the specified font size
      const span = document.createElement("span");
      span.style.fontSize = size;

      // SurroundContents is often better than extract+insert for preserving structure
      try {
        // Check if the selection spans across multiple block elements
        // If so, surrounding might not work as expected or might be invalid HTML.
        // A more robust solution would involve iterating through nodes in the range.
        span.appendChild(range.extractContents());
        range.insertNode(span);

        // Restore selection around the new span if possible
        range.selectNodeContents(span);
        selection.removeAllRanges();
        selection.addRange(range);
      } catch (e) {
        console.error("Could not apply font size:", e);
        // Fallback or alternative approach might be needed for complex selections
        // Maybe use insertHTML with styled content (less ideal)
      }

      handleContentChange();
    }
  };

  // --- Text Color Handling ---
  const handleTextColorChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const color = event.target.value;
    setCurrentColor(color); // Update the color picker's displayed color
    executeCommand("foreColor", color);
  };

  const handleOrderedList = () => executeCommand("insertOrderedList");
  const handleUnorderedList = () => executeCommand("insertUnorderedList");

  const handleAlignLeft = () => executeCommand("justifyLeft");
  const handleAlignCenter = () => executeCommand("justifyCenter");
  const handleAlignRight = () => executeCommand("justifyRight");
  const handleAlignJustify = () => executeCommand("justifyFull");

  const handleLink = () => {
    editorRef.current?.focus(); // Ensure focus before prompt
    const url = prompt("Enter URL:");
    if (url) {
      // Check if there is a selection, if not, insert the URL as text and then link it.
      const selection = window.getSelection();
      if (selection && selection.toString().length === 0) {
        document.execCommand("insertText", false, url);
        // Re-select the inserted text - this is tricky, might need range manipulation
        // Simpler: just execute createLink, browser might handle it or link the prompt value
      }
      executeCommand("createLink", url);
    }
  };

  const handleImage = () => {
    editorRef.current?.focus(); // Ensure focus before prompt
    const imageUrl = prompt("Enter image URL:");
    if (imageUrl) {
      executeCommand("insertImage", imageUrl);
    }
  };

  const handleUndo = () => executeCommand("undo");
  const handleRedo = () => executeCommand("redo");

  // --- Block Formatting ---
  // Using formatBlock is generally preferred for block-level elements
  const handleCodeBlock = () => {
    executeCommand("formatBlock", "<pre>");
    // Optionally wrap in <code> inside <pre> for semantic correctness
    // This requires more complex DOM manipulation after formatBlock
  };

  const handleQuote = () => executeCommand("formatBlock", "<blockquote>");

  // Handle pasted content (Improved slightly)
  const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {
    event.preventDefault();
    const clipboardData = event.clipboardData;
    let pastedContent = "";

    // Prefer HTML content if available
    const htmlData = clipboardData.getData("text/html");
    if (htmlData) {
      // Basic Sanitization (highly recommended to use a library like DOMPurify for security)
      // This is a VERY basic example, not production-safe against XSS
      const sanitizedHtml = htmlData; // .replace(/<script.*?>.*?<\/script>/gi, ''); // Remove script tags
      // Add more rules or use a library
      pastedContent = sanitizedHtml;
    } else {
      // Fallback to plain text
      const text = clipboardData.getData("text/plain");
      // Convert newlines to <br> for HTML representation
      pastedContent = text.replace(/\n/g, "<br>");
    }

    if (pastedContent) {
      editorRef.current?.focus(); // Ensure focus
      document.execCommand("insertHTML", false, pastedContent);
    }

    handleContentChange(); // Update state after paste
  };

  // Keyboard Shortcuts Effect
  useEffect(() => {
    const editor = editorRef.current; // Capture ref value
    if (!editor) return; // Exit if editor not mounted yet

    const handleKeyDown = (event: KeyboardEvent) => {
      // Use event.key for modern browsers, fallbacks might be needed for older ones
      const key = event.key.toLowerCase();
      const isCtrlOrMeta = event.ctrlKey || event.metaKey; // Meta for macOS

      if (isCtrlOrMeta) {
        switch (key) {
          case "b":
            event.preventDefault();
            handleBold();
            break;
          case "i":
            event.preventDefault();
            handleItalic();
            break;
          case "u":
            event.preventDefault();
            handleUnderline();
            break;
          case "z":
            event.preventDefault();
            // Handle redo (Shift+Ctrl+Z or Ctrl+Y)
            if (event.shiftKey) {
              handleRedo();
            } else {
              handleUndo();
            }
            break;
          case "y": // Typically Ctrl+Y for Redo
            event.preventDefault();
            handleRedo();
            break;
          // Add more shortcuts if needed
        }
      }
    };

    editor.addEventListener("keydown", handleKeyDown);

    // Cleanup function
    return () => {
      editor.removeEventListener("keydown", handleKeyDown);
    };
    // Re-run effect if handlers change (though they are memoized with useCallback)
  }, [handleBold, handleItalic, handleUnderline, handleUndo, handleRedo]);

  const isFeatureEnabled = (featureName: string) =>
    toolbarFeatures.includes(featureName);

  // Determine which features go behind the "More" button
  // You can customize this list as needed
  const commonToolbarFeatures = [
    "bold",
    "italic",
    "underline",
    "strikethrough",
    "fontSize",
    "textColor", // Added textColor here
    "orderedList",
    "unorderedList",
    "alignLeft",
    "alignCenter",
    "alignRight",
    "alignJustify",
  ];

  const otherAvailableFeatures = toolbarFeatures.filter(
    (feature) => !commonToolbarFeatures.includes(feature)
  );

  return (
    <div className="rich-text-editor border border-gray-300 rounded-lg overflow-hidden shadow-sm">
      {/* Toolbar */}
      <div className="toolbar bg-gray-50 p-2 border-b border-gray-300 flex flex-wrap items-center gap-1 md:gap-2">
        {/* Always Visible Features */}
        {isFeatureEnabled("bold") && (
          <button
            onClick={handleBold}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Bold (Ctrl+B)"
          >
            <FaBold />
          </button>
        )}
        {isFeatureEnabled("italic") && (
          <button
            onClick={handleItalic}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Italic (Ctrl+I)"
          >
            <FaItalic />
          </button>
        )}
        {isFeatureEnabled("underline") && (
          <button
            onClick={handleUnderline}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Underline (Ctrl+U)"
          >
            <FaUnderline />
          </button>
        )}
        {isFeatureEnabled("strikethrough") && (
          <button
            onClick={handleStrikethrough}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Strikethrough"
          >
            <FaStrikethrough />
          </button>
        )}
        {isFeatureEnabled("fontSize") && (
          <select
            onChange={(e) => handleFontSize(e.target.value)}
            defaultValue=""
            className="toolbar-select p-1.5 border border-gray-300 rounded hover:bg-gray-100 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            title="Font Size"
          >
            <option value="" disabled>
              Size
            </option>
            <option value="8px">8px</option>
            <option value="10px">10px</option>
            <option value="12px">12px</option>
            <option value="14px">14px</option>
            <option value="16px">16px</option>
            <option value="18px">18px</option>
            <option value="20px">20px</option>
            <option value="24px">24px</option>
            <option value="30px">30px</option>
            <option value="36px">36px</option>
            {/* Add a way to clear font size? Maybe an option or separate button */}
          </select>
        )}
        {/* --- Text Color Input --- */}
        {isFeatureEnabled("textColor") && (
          <input
            type="color"
            onChange={handleTextColorChange}
            value={currentColor} // Controlled component for the color input visual
            className="toolbar-color-picker w-7 h-7 md:w-8 md:h-8 p-0 border border-gray-300 rounded cursor-pointer hover:opacity-90 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            title="Text Color"
          />
        )}
        {isFeatureEnabled("orderedList") && (
          <button
            onClick={handleOrderedList}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Ordered List"
          >
            <FaListOl />
          </button>
        )}
        {isFeatureEnabled("unorderedList") && (
          <button
            onClick={handleUnorderedList}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Unordered List"
          >
            <FaListUl />
          </button>
        )}
        {isFeatureEnabled("alignLeft") && (
          <button
            onClick={handleAlignLeft}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Align Left"
          >
            <FaAlignLeft />
          </button>
        )}
        {isFeatureEnabled("alignCenter") && (
          <button
            onClick={handleAlignCenter}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Align Center"
          >
            <FaAlignCenter />
          </button>
        )}
        {isFeatureEnabled("alignRight") && (
          <button
            onClick={handleAlignRight}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Align Right"
          >
            <FaAlignRight />
          </button>
        )}
        {isFeatureEnabled("alignJustify") && (
          <button
            onClick={handleAlignJustify}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Align Justify"
          >
            <FaAlignJustify />
          </button>
        )}

        {/* Separator and More Options Button if applicable */}
        {otherAvailableFeatures.length > 0 && (
          <>
            {/* Optional: Visual separator */}
            {/* <span className="border-l border-gray-300 h-6 mx-1"></span> */}
            <button
              onClick={() => setShowMoreOptions(!showMoreOptions)}
              className={`toolbar-button p-2 rounded ${
                showMoreOptions ? "bg-gray-200" : "hover:bg-gray-200"
              }`}
              title="More Options"
            >
              <FaEllipsisH />
            </button>
          </>
        )}

        {/* Undo/Redo are often placed separately or at the end */}
        {isFeatureEnabled("undo") && (
          <button
            onClick={handleUndo}
            className="toolbar-button p-2 hover:bg-gray-200 rounded ml-auto md:ml-2"
            title="Undo (Ctrl+Z)"
          >
            <FaUndo />
          </button>
        )}
        {isFeatureEnabled("redo") && (
          <button
            onClick={handleRedo}
            className="toolbar-button p-2 hover:bg-gray-200 rounded"
            title="Redo (Ctrl+Y / Ctrl+Shift+Z)"
          >
            <FaRedo />
          </button>
        )}
      </div>

      {/* Collapsible Section for Other Features */}
      {showMoreOptions && otherAvailableFeatures.length > 0 && (
        <div className="toolbar-more bg-gray-50 p-2 border-b border-gray-300 flex flex-wrap items-center gap-1 md:gap-2">
          {otherAvailableFeatures.includes("link") &&
            isFeatureEnabled("link") && (
              <button
                onClick={handleLink}
                className="toolbar-button p-2 hover:bg-gray-200 rounded"
                title="Insert Link"
              >
                <FaLink />
              </button>
            )}
          {otherAvailableFeatures.includes("image") &&
            isFeatureEnabled("image") && (
              <button
                onClick={handleImage}
                className="toolbar-button p-2 hover:bg-gray-200 rounded"
                title="Insert Image"
              >
                <FaImage />
              </button>
            )}
          {otherAvailableFeatures.includes("codeBlock") &&
            isFeatureEnabled("codeBlock") && (
              <button
                onClick={handleCodeBlock}
                className="toolbar-button p-2 hover:bg-gray-200 rounded"
                title="Code Block"
              >
                <FaCode />
              </button>
            )}
          {otherAvailableFeatures.includes("quote") &&
            isFeatureEnabled("quote") && (
              <button
                onClick={handleQuote}
                className="toolbar-button p-2 hover:bg-gray-200 rounded"
                title="Blockquote"
              >
                <FaQuoteRight />
              </button>
            )}
          {/* Add any other features designated for the 'more' section here */}
        </div>
      )}

      {/* Editable Content Area */}
      <div
        ref={editorRef}
        className="editor-content prose prose-sm sm:prose lg:prose-lg xl:prose-xl max-w-none min-h-[300px] p-4 bg-white focus:outline-none overflow-y-auto" // Added max-w-none to override prose width limits if needed, overflow-y-auto
        contentEditable={true}
        onInput={handleContentChange} // Fires on any input change
        onBlur={handleContentChange} // Handle cases where content changes without direct input (like commands)
        onPaste={handlePaste}
        suppressContentEditableWarning={true}
        // Optionally add ARIA attributes for accessibility
        role="textbox"
        aria-multiline="true"
        aria-label="Rich text editor content"
      >
        {/* Initial content is set via innerHTML in useEffect */}
      </div>
    </div>
  );
};

export default CustomEditor;
