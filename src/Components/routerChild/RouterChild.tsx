import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { Navigate, Route, useLocation } from "react-router-dom";
import Article from "../../Components/Articles/Article";
import Articles from "../../Components/Articles/Articles";
import Chat from "../../Components/Chat/Chat";
import Company from "../../Components/Company/Company";
import Connections from "../../Components/Connections/Connections";
import Contact from "../../Components/Contact/Contact";
import Event from "../../Components/Events/Event";
import Events from "../../Components/Events/Events";
import Helpdesk from "../../Components/HelpDesk/Helpdesk";
import Homepage from "../../Components/Home/Homepage";
import LearnMore from "../../Components/LearnMore/LearnMore";
import NotificationView from "../../Components/Notifications/NotificationView";
import CompanyOpportunites from "../../Components/Opportunity/CompanyOpportunites";
import Opportunites from "../../Components/Opportunity/Opportunites";
import Opportunity from "../../Components/Opportunity/Opportunity";
import YourOpportunites from "../../Components/Opportunity/YourOpportunites";
import YourOpportunity from "../../Components/Opportunity/YourOpportunity";
import Password from "../../Components/Password/Password";
import Personal from "../../Components/Peronal/Personal";
import CompanyProducts from "../../Components/Products/CompanyProducts";
import Product from "../../Components/Products/Product";
import Products from "../../Components/Products/Products";
import YourProduct from "../../Components/Products/YourProduct";
import YourProducts from "../../Components/Products/YourProducts";
import UserProfile from "../../Components/Profile/UserProfile";
import Signup from "../../Components/Signup/Signup";
import * as route from "../../Components/constants";
import Call from "../innovation/Call";
import CallView from "../innovation/CallView";
import Innovation from "../innovation/Innovation";
import Publications from "../Publications/Publications";
import Publication from "../Publications/Publication";
import News from "../News/News";

interface IRouterChild {
  handleLoginModal: () => void;
  handleWebsiteModal: () => void;
  handleSponsorModal: () => void;
}

const RouterChild: React.FC<IRouterChild> = ({
  handleLoginModal,
  handleWebsiteModal,
  handleSponsorModal,
}) => {
  //redux user
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  const location = useLocation();

  useEffect(() => {
    window.scrollTo({
      top: 0,
    });
  }, [location]);

  return (
    <>
      <Route
        path={route.HOME}
        element={
          <Homepage
            handleLogin={handleLoginModal}
            handleWebsiteModal={handleWebsiteModal}
            handleSponsorModal={handleSponsorModal}
          />
        }
      />
      {/* <PrivateRoute path='/' element={<Homepage handleLogin={handleLoginModal} />} /> */}
      <Route
        path="/signup"
        element={<Signup handleLoginModal={handleLoginModal} />}
      />
      <Route
        path={route.TECHNOLOGY}
        element={<Products handleLoginModal={handleLoginModal} />}
      />
      <Route
        path={route.PRODUCT}
        element={<Product handleLoginModal={handleLoginModal} />}
      />
      <Route path={route.YOUR_TECHNOLOGY} element={<YourProducts />} />
      <Route
        path={route.YOUR_PRODUCT}
        element={<YourProduct handleLoginModal={handleLoginModal} />}
      />
      <Route path={route.COMPANY_TECHNOLOGY} element={<CompanyProducts />} />
      <Route path={route.OPPORTUNITY} element={<Opportunites />} />
      <Route
        path={route.OPP}
        element={<Opportunity handleLoginModal={handleLoginModal} />}
      />
      <Route path={route.YOUR_OPPORTUNITY} element={<YourOpportunites />} />
      <Route
        path={route.YOUR_OPP}
        element={<YourOpportunity handleLoginModal={handleLoginModal} />}
      />
      <Route
        path={route.COMPANY_OPPORTUNITY}
        element={<CompanyOpportunites />}
      />
      <Route path={route.INNOVATION} element={<Innovation />} />
      <Route path={route.INNOVATION_CALL_ID} element={<Call />} />
      <Route
        path={route.INNOVATION_CALL_ID_VIEW}
        element={<CallView handleLoginModal={handleLoginModal} />}
      />
      <Route path={route.HELPDESK} element={<Helpdesk />} />
      <Route path={route.ABOUT} element={<LearnMore />}></Route>
      <Route path={route.ARTICLES} element={<Articles />}></Route>
      <Route path={`${route.ARTICLE}/:id`} element={<Article />}></Route>
      <Route path={route.PUBLICATIONS} element={<Publications />}></Route>
      <Route
        path={`${route.PUBLICATION}/:id`}
        element={<Publication handleLoginModal={handleLoginModal} />}
      ></Route>
      <Route path={route.NEWS} element={<News />}></Route>
      <Route path={`${route.NEWS}/:id`} element={<News />}></Route>
      <Route path={route.EVENTS} element={<Events />}></Route>
      <Route
        path={route.EVENT}
        element={<Event handleLoginModal={handleLoginModal} />}
      ></Route>
      <Route path={route.NOTIFICATIONS} element={<NotificationView />}></Route>
      <Route path={route.CHAT_ID} element={<Chat />}></Route>
      <Route path={route.CONTACTUS} element={<Contact />}></Route>

      {currentUser.admin !== -1 ? (
        <Route path="/profile" element={<UserProfile />}>
          <Route path="personal" element={<Personal />} />
          <Route path="company" element={<Company />} />
          <Route path="password" element={<Password />} />
          <Route path="network" element={<Connections />} />
        </Route>
      ) : (
        <Route path="*" element={<Navigate to="/" replace />} />
      )}
    </>
  );
};

export default RouterChild;
