/* Modern About Page Styles */

.about-page-main {
  @apply flex flex-col min-h-screen w-full;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Hero Section */
.about-hero-section {
  @apply relative min-h-screen flex items-center justify-center overflow-hidden;
  background: linear-gradient(135deg, #151e70 0%, #1e40af 50%, #3b82f6 100%);
}

.about-hero-background {
  @apply absolute inset-0 opacity-10;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(255, 255, 255, 0.2) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    );
}

.about-hero-content {
  @apply relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center;
}

.about-hero-badge {
  @apply inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20 mb-8;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.about-hero-title {
  @apply text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6;
  font-family: "Plus Jakarta Sans", sans-serif;
  line-height: 1.1;
}

.about-hero-gradient-text {
  background: linear-gradient(135deg, #60a5fa, #34d399, #fbbf24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-hero-subtitle {
  @apply text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto mb-12 leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

.about-hero-stats {
  @apply flex flex-wrap justify-center gap-8 md:gap-16;
}

.about-stat-item {
  @apply text-center;
}

.about-stat-number {
  @apply text-3xl md:text-4xl font-bold text-white mb-2;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.about-stat-label {
  @apply text-blue-200 text-sm md:text-base;
  font-family: "DM Sans", sans-serif;
}

/* Section Container */
.about-section-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Mission Section */
.about-mission-section {
  @apply py-20 bg-white;
}

.about-mission-content {
  @apply grid lg:grid-cols-2 gap-16 items-center;
}

.about-mission-text {
  @apply space-y-8;
}

.about-section-badge {
  @apply inline-flex items-center space-x-2 bg-blue-50 text-GTI-BLUE-default rounded-full px-4 py-2 text-sm font-medium;
}

.about-section-title {
  @apply text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900;
  font-family: "Plus Jakarta Sans", sans-serif;
  line-height: 1.2;
}

.about-section-description {
  @apply text-lg md:text-xl text-gray-600 leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

.about-mission-features {
  @apply space-y-4;
}

.about-feature-item {
  @apply flex items-start space-x-3;
}

.about-feature-item span {
  @apply text-gray-700 font-medium;
  font-family: "DM Sans", sans-serif;
}

.about-mission-visual {
  @apply flex justify-center;
}

.about-visual-container {
  @apply relative rounded-2xl overflow-hidden shadow-2xl;
  max-width: 500px;
}

.about-visual-image {
  @apply w-full h-auto;
}

.about-visual-overlay {
  @apply absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300;
}

.about-play-button {
  @apply w-16 h-16 bg-white/90 rounded-full flex items-center justify-center text-GTI-BLUE-default hover:bg-white hover:scale-110 transition-all duration-300 cursor-pointer;
}

/* Features Section */
.about-features-section {
  @apply py-20 bg-gray-50;
}

.about-features-header {
  @apply text-center mb-16;
}

.about-features-grid {
  @apply grid md:grid-cols-2 lg:grid-cols-3 gap-8;
}

.about-feature-card {
  @apply bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.about-feature-icon {
  @apply w-16 h-16 bg-gradient-to-br from-GTI-BLUE-default to-blue-600 rounded-2xl flex items-center justify-center text-white mb-6;
}

.about-feature-title {
  @apply text-xl font-bold text-gray-900 mb-4;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.about-feature-description {
  @apply text-gray-600 leading-relaxed mb-6;
  font-family: "DM Sans", sans-serif;
}

.about-feature-benefits {
  @apply flex flex-wrap gap-2;
}

.about-benefit-tag {
  @apply bg-blue-50 text-GTI-BLUE-default px-3 py-1 rounded-full text-sm font-medium;
}

/* Responsive Design */
@media (max-width: 640px) {
  .about-hero-title {
    @apply text-3xl;
  }

  .about-hero-subtitle {
    @apply text-lg;
  }

  .about-section-title {
    @apply text-2xl;
  }

  .about-section-description {
    @apply text-base;
  }

  .about-features-grid {
    @apply grid-cols-1;
  }

  .about-mission-content {
    @apply grid-cols-1 gap-8;
  }
}

/* Animation Classes */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stagger-animation {
  animation-delay: var(--delay, 0s);
}

/* Target Audience Section */
.about-audience-section {
  @apply py-20 bg-white;
}

.about-audience-grid {
  @apply grid md:grid-cols-2 lg:grid-cols-3 gap-8;
}

.about-audience-card {
  @apply bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.about-audience-card:hover {
  border-color: rgba(59, 130, 246, 0.2);
}

.about-audience-icon {
  @apply w-16 h-16 bg-gradient-to-br from-GTI-BLUE-default to-blue-600 rounded-2xl flex items-center justify-center text-white mb-6;
}

.about-audience-title {
  @apply text-2xl font-bold text-gray-900 mb-3;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.about-audience-subtitle {
  @apply text-GTI-BLUE-default font-semibold mb-6;
  font-family: "DM Sans", sans-serif;
}

.about-audience-benefits {
  @apply space-y-3 mb-8;
}

.about-audience-benefits li {
  @apply flex items-start space-x-3 text-gray-600;
  font-family: "DM Sans", sans-serif;
}

.about-audience-benefits li::before {
  content: "✓";
  @apply text-green-500 font-bold flex-shrink-0;
}

.about-audience-cta {
  @apply pt-4 border-t border-gray-200;
}

.about-cta-button {
  @apply inline-flex items-center space-x-2 bg-GTI-BLUE-default text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-all duration-300 transform hover:scale-105;
}

/* CTA Section */
.about-cta-section {
  @apply py-20 bg-gradient-to-r from-GTI-BLUE-default to-blue-600;
}

.about-cta-content {
  @apply text-center text-white;
}

.about-cta-title {
  @apply text-3xl md:text-4xl font-bold mb-6;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.about-cta-description {
  @apply text-xl text-blue-100 mb-8 max-w-3xl mx-auto;
  font-family: "DM Sans", sans-serif;
}

.about-cta-buttons {
  @apply flex flex-col sm:flex-row gap-4 justify-center;
}

.about-primary-button {
  @apply bg-white text-GTI-BLUE-default px-8 py-4 rounded-xl font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105;
}

.about-secondary-button {
  @apply border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-GTI-BLUE-default transition-all duration-300 transform hover:scale-105;
}

/* Hover Effects */
.about-feature-card:hover .about-feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.about-feature-card:hover .about-benefit-tag {
  @apply bg-GTI-BLUE-default text-white;
}

.about-audience-card:hover .about-audience-icon {
  transform: scale(1.1) rotate(-5deg);
}

/* Services Section */
.about-services-section {
  @apply py-20 bg-gray-50;
}

.about-services-grid {
  @apply grid md:grid-cols-2 gap-8;
}

.about-service-card {
  @apply bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.about-service-icon {
  @apply w-16 h-16 bg-gradient-to-br from-GTI-BLUE-default to-blue-600 rounded-2xl flex items-center justify-center text-white mb-6;
}

.about-service-title {
  @apply text-2xl font-bold text-gray-900 mb-6;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.about-service-description {
  @apply text-gray-600 leading-relaxed mb-6;
  font-family: "DM Sans", sans-serif;
}

.about-service-list {
  @apply space-y-3;
}

.about-service-list li {
  @apply flex items-start space-x-3 text-gray-600;
  font-family: "DM Sans", sans-serif;
}

.about-service-list li::before {
  content: "•";
  @apply text-GTI-BLUE-default font-bold flex-shrink-0;
}

.about-gbi-stats {
  @apply flex gap-6 mt-6;
}

.about-gbi-stat {
  @apply text-center;
}

.about-gbi-stat .about-stat-number {
  @apply text-2xl font-bold text-GTI-BLUE-default mb-1;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.about-gbi-stat .about-stat-label {
  @apply text-gray-500 text-sm;
  font-family: "DM Sans", sans-serif;
}
