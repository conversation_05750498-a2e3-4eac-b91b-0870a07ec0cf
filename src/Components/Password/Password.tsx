import React from "react";
import { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { AiFillEye, AiFillEyeInvisible } from "react-icons/ai";
import axios from "axios";
import { resetUser } from "../../store/actioncreators/actionCreators";
import { Dispatch } from "redux";
import { redirect } from "react-router-dom";

const Password = () => {
  let [oldpass, setOldPass] = useState("");
  let [sOP, setSOP] = useState(false);

  let [newpass, setNewPass] = useState("");
  let [sNP, setSNP] = useState(false);

  let [rnewpass, rsetNewPass] = useState("");
  let [sRNP, setSRNP] = useState(false);

  let regexpPass = new RegExp(
    "^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{6,16}$"
  );

  const dispatch: Dispatch<any> = useDispatch();
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  const resetUserDetails = React.useCallback(
    () => dispatch(resetUser(currentUser)),
    [dispatch, currentUser]
  );

  let check = () => {
    let opass = document.getElementById("oPass") as HTMLInputElement;
    let nPass = document.getElementById("nPass") as HTMLInputElement;
    let rnPass = document.getElementById("rnPass") as HTMLInputElement;

    if (!oldpass) {
      opass?.classList.remove("profile-content-head-2");
      opass?.classList.add("profile-content-head-2-error");
      opass.textContent = "Enter current password";
      return false;
    } else {
      opass?.classList.remove("profile-content-head-2-error");
      opass?.classList.add("profile-content-head-2");
    }

    if (!regexpPass.test(newpass)) {
      nPass?.classList.remove("profile-content-head-2");
      nPass?.classList.add("profile-content-head-2-error");
      nPass.textContent = "Enter strong Password";
      return false;
    } else if (newpass === oldpass) {
      nPass?.classList.remove("profile-content-head-2");
      nPass?.classList.add("profile-content-head-2-error");
      nPass.textContent = "New Password should not be same as Old Password";
    } else {
      nPass?.classList.remove("profile-content-head-2-error");
      nPass?.classList.add("profile-content-head-2");
    }

    if (newpass !== rnewpass) {
      rnPass?.classList.remove("profile-content-head-2");
      rnPass?.classList.add("profile-content-head-2-error");
      rnPass.textContent =
        "Re-entered Password does not matches the New Password";
      return false;
    } else {
      rnPass?.classList.remove("profile-content-head-2-error");
      rnPass?.classList.add("profile-content-head-2");
    }

    return true;
  };

  let reset = () => {
    var data = JSON.stringify({
      password: newpass,
      oldPassword: oldpass,
    });

    var config = {
      method: "patch",
      url: `${process.env.REACT_APP_BASE_API}/users/resetpassword`,
      headers: {
        Authorization: `Bearer ${currentUser.token}`,
        "Content-Type": "application/json",
      },
      data: data,
    };

    axios(config)
      .then(function (response) {
        // console.log("password reset request send", response);
        resetUserDetails();
        redirect(process.env.REACT_APP_HOME!);
      })
      .catch(function (error) {
        // console.log(error);
      });
  };

  let handleSaveCompany = async () => {
    if (check()) {
      reset();
    }
  };

  return (
    <div className="profile-form-container">
      <div className="profile-form-section">
        <h2 className="profile-form-title">Change Password</h2>

        <div className="profile-form-grid">
          <div className="profile-form-group full-width">
            <label className="profile-form-label">Current Password</label>
            <div className="profile-password-container">
              <input
                id="oldpass"
                type={sOP ? "text" : "password"}
                value={oldpass}
                onChange={(e) => setOldPass(e.target.value)}
                className="profile-form-input"
                placeholder="Enter your current password"
              />
              {sOP ? (
                <AiFillEye
                  onClick={() => setSOP(!sOP)}
                  className="profile-password-toggle"
                />
              ) : (
                <AiFillEyeInvisible
                  onClick={() => setSOP(!sOP)}
                  className="profile-password-toggle"
                />
              )}
            </div>
          </div>

          <div className="profile-form-group full-width">
            <label className="profile-form-label">New Password</label>
            <div className="profile-password-container">
              <input
                id="newpass"
                type={sNP ? "text" : "password"}
                value={newpass}
                onChange={(e) => setNewPass(e.target.value)}
                className="profile-form-input"
                placeholder="Enter your new password"
              />
              {sNP ? (
                <AiFillEye
                  onClick={() => setSNP(!sNP)}
                  className="profile-password-toggle"
                />
              ) : (
                <AiFillEyeInvisible
                  onClick={() => setSNP(!sNP)}
                  className="profile-password-toggle"
                />
              )}
            </div>
          </div>

          <div className="profile-form-group full-width">
            <label className="profile-form-label">Confirm New Password</label>
            <div className="profile-password-container">
              <input
                id="rnewpass"
                type={sRNP ? "text" : "password"}
                value={rnewpass}
                onChange={(e) => rsetNewPass(e.target.value)}
                className="profile-form-input"
                placeholder="Re-enter your new password"
              />
              {sRNP ? (
                <AiFillEye
                  onClick={() => setSRNP(!sRNP)}
                  className="profile-password-toggle"
                />
              ) : (
                <AiFillEyeInvisible
                  onClick={() => setSRNP(!sRNP)}
                  className="profile-password-toggle"
                />
              )}
            </div>
          </div>
        </div>

        <div className="profile-button-group">
          <button
            type="button"
            onClick={handleSaveCompany}
            className="profile-btn-primary"
          >
            Update Password
          </button>
        </div>
      </div>
    </div>
  );
};

export default Password;
