import React, { Dispatch, useEffect, useRef, useState } from "react";
import { Helmet } from "react-helmet";

import {
  ConnectionStatus,
  NONE,
  sendchatItem,
  title,
  metaData,
} from "../constants";
import test_profile from "../../assests/test/profile.jpg";
import defaultPic from "../../assests/images/default-user.svg";
import { IoSend } from "react-icons/io5";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import {
  getChat,
  getMoreChat,
  sendChat,
} from "../../store/actioncreators/chatactions";
import { useNavigate, useParams } from "react-router-dom";
import { getConnections } from "../../store/actioncreators/connectionactions";

import { getUserData } from "../../api/user";
import { isSuccess, notify } from "../../utils";
import { useDebouncedCallback } from "../../shared/hooks/useDebounce";

const Sender = ({
  image,
  message,
  timestamp,
}: {
  image: string;
  message: string;
  timestamp?: string;
}) => {
  return (
    <div className="flex flex-row items-end justify-end mb-4 w-full group message-sender">
      <div className="flex flex-col items-end max-w-xs lg:max-w-md">
        <div className="bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white px-4 py-3 rounded-2xl rounded-br-md shadow-lg">
          <p className="text-sm leading-relaxed">
            {message || "Message not available"}
          </p>
        </div>
        <span className="text-xs text-gray-500 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          {timestamp || "Just now"}
        </span>
      </div>
      <img
        loading="lazy"
        src={image !== NONE ? image : test_profile}
        className="rounded-full w-8 h-8 object-cover ml-3 border-2 border-white shadow-md"
        alt="You"
      />
    </div>
  );
};
const Receiver = ({
  image,
  message,
  timestamp,
}: {
  image: string;
  message: string;
  timestamp?: string;
}) => {
  return (
    <div className="flex flex-row items-end justify-start mb-4 w-full group message-receiver">
      <img
        loading="lazy"
        src={image !== NONE ? image : test_profile}
        className="rounded-full w-8 h-8 object-cover mr-3 border-2 border-white shadow-md"
        alt="Contact"
      />
      <div className="flex flex-col items-start max-w-xs lg:max-w-md">
        <div className="bg-white border border-gray-200 px-4 py-3 rounded-2xl rounded-bl-md shadow-lg">
          <p className="text-sm text-gray-800 leading-relaxed">
            {message || "Error in displaying message"}
          </p>
        </div>
        <span className="text-xs text-gray-500 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          {timestamp || "Just now"}
        </span>
      </div>
    </div>
  );
};

interface IChatContainer {
  chatContainerRef: any;
  senderId: string;
  receiverId: string;
  page: {
    skip: number;
    limit: number;
  };
  setPage: any;
}

const ChatContainer: React.FC<IChatContainer> = ({
  chatContainerRef,
  senderId,
  receiverId,
  page,
  setPage,
}) => {
  const dispatch = useDispatch<any>();
  const chat_data: CHAT_STATE = useSelector((state: STATE) => state.CHAT);

  const { id } = useParams();
  useEffect(() => {
    if (chat_data.CHAT_LIST.CHAT.length <= 10)
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
  }, [chat_data, chatContainerRef]);

  useEffect(() => {
    dispatch(getChat(id ?? "", page.skip.toString(), page.limit.toString()));
    setPage((prev: any) => ({
      skip: prev.skip + 1,
      limit: prev.limit + 1,
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    dispatch(getChat(id ?? "", "0", "10"));
    setPage({
      skip: 1,
      limit: 10,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const handleScroll = () => {
    if (chatContainerRef?.current) {
      const container = chatContainerRef.current;
      const scrollPos = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      const offset = 200;

      if (
        scrollPos <= offset &&
        scrollHeight > clientHeight &&
        page.skip * page.limit < chat_data?.CHAT_TOTAL
      ) {
        dispatch(
          getMoreChat(receiverId, page.skip.toString(), page.limit.toString())
        );
        setPage((prev: any) => ({
          skip: prev.skip + 1,
          limit: prev.limit + 1,
        }));
      }
    }
  };

  const debounceScroll = useDebouncedCallback(handleScroll, 500);

  return (
    <div
      ref={chatContainerRef}
      onScroll={debounceScroll}
      className="flex-1 overflow-y-auto px-6 py-4 bg-gradient-to-b from-gray-50/50 to-white chat-scrollbar"
      style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
      }}
    >
      {chat_data && chat_data.CHAT_LIST.CHAT.length > 0 ? (
        chat_data.CHAT_LIST.CHAT.map((item: any, index: number) => {
          const timestamp = new Date(item.createdAt).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          });

          if (item.user._id === senderId) {
            return (
              <Sender
                key={index}
                message={item?.message}
                image={item.company?.logo ?? defaultPic}
                timestamp={timestamp}
              />
            );
          } else {
            return (
              <Receiver
                key={index}
                message={item?.message}
                image={item.company?.logo ?? defaultPic}
                timestamp={timestamp}
              />
            );
          }
        })
      ) : (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <svg
              className="w-10 h-10 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.471L3 21l2.471-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">
            Start a conversation
          </h3>
          <p className="text-gray-500">Send a message to begin chatting</p>
        </div>
      )}
    </div>
  );
};

const UserChat = ({
  receiver,
  sender,
  handleView,
}: {
  receiver?: any;
  sender: USER;
  handleView: (id: string) => void;
}) => {
  const { id } = useParams();
  const dispatch: Dispatch<any> = useDispatch();
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const [page, setPage] = useState({
    skip: 0,
    limit: 10,
  });
  const [message, setMessage] = useState<sendchatItem>({
    message: "",
    receiver: id ?? "",
  });

  const handleSend = async () => {
    setMessage({ ...message, message: "" });
    dispatch(
      sendChat(
        {
          receiver: id ?? "",
          message: message.message,
        },
        page.skip,
        page.limit
      )
    );
    if (chatContainerRef.current) {
      setTimeout(() => {
        const container = chatContainerRef.current;
        container!.scrollTop = container!.scrollHeight;
      }, 500);
    }
  };

  return (
    <div className="flex flex-col w-full h-full bg-white">
      {/* Chat Header */}
      <div className="flex items-center px-6 py-4 bg-white border-b border-gray-200 shadow-sm">
        <img
          src={receiver?.profileImage || receiver?.logo || defaultPic}
          alt={receiver?.fullName}
          className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-md mr-4 flex-shrink-0"
        />
        <div className="flex-1 min-w-0">
          <h2 className="text-lg font-semibold text-gray-900 truncate">
            {receiver?.fullName || "Unknown User"}
          </h2>
          <p className="text-sm text-gray-500 truncate">
            {receiver?.name || receiver?.companyId || ""}
          </p>
        </div>
      </div>

      {/* Chat Messages */}
      <ChatContainer
        chatContainerRef={chatContainerRef}
        senderId={sender.id}
        receiverId={receiver?._id ?? ""}
        page={page}
        setPage={setPage}
      />

      {/* Message Input */}
      <div className="px-6 py-4 bg-white border-t border-gray-200">
        <form
          className="flex items-center space-x-4"
          onSubmit={(e) => {
            e.preventDefault();
            if (message.message.trim()) {
              handleSend();
            }
          }}
        >
          <div className="flex-1 relative">
            <input
              value={message.message}
              onChange={(e) => {
                setMessage({ ...message, message: e.target.value });
              }}
              type="text"
              className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-colors duration-200"
              placeholder="Type your message..."
              required
            />
          </div>
          <button
            type="submit"
            disabled={!message.message.trim()}
            className="p-3 bg-GTI-BLUE-default text-white rounded-2xl hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <IoSend className="w-5 h-5" />
          </button>
        </form>
      </div>
    </div>
  );
};

const ConnectionsCard = ({
  item: connectionData,
  handleView,
  isActive = false,
}: {
  item: any;
  handleView: (id: string) => void;
  isActive?: boolean;
}) => {
  const item = connectionData?.sender
    ? {
        ...connectionData.sender,
        ...connectionData.senderCompany,
      }
    : {
        ...connectionData.receiver,
        ...connectionData.receiverCompany,
      };

  const handleOpen = () => {
    handleView(item._id);
  };

  return (
    <div
      onClick={handleOpen}
      className={`flex items-center p-4 cursor-pointer transition-all duration-200 hover:bg-gray-50 border-l-4 ${
        isActive
          ? "bg-GTI-BLUE-default/5 border-l-GTI-BLUE-default"
          : "border-l-transparent hover:border-l-gray-200"
      }`}
    >
      <div className="relative">
        <img
          src={item.logo || item.profileImage || defaultPic}
          className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-md"
          alt={item.fullName}
        />
      </div>

      <div className="ml-4 flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-gray-900 truncate">
            {item.fullName || "Unknown User"}
          </h3>
          <span className="text-xs text-gray-500">2m</span>
        </div>
        <p className="text-sm text-gray-600 truncate mt-1">
          {item.name || "No company"}
        </p>
      </div>
    </div>
  );
};

const Chat = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch: Dispatch<any> = useDispatch();

  const [receiver, setReceiver] = useState<any>(null);

  const connections: CONNECTION = useSelector(
    (state: STATE) => state.CONNECTION.CONNECTION
  );
  const [search, setSearch] = useState({
    key: "",
    process: false,
  });
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);

  const handleView = (id: string) => {
    navigate(`/chat/${id}`);
  };

  useEffect(() => {
    if (id) {
      fetchReceiver(id);
    }
  }, [id]);

  useEffect(() => {
    if (id && connections?.CONNECTION_LIST?.connections?.length) {
      let found = false;
      connections.CONNECTION_LIST.connections.forEach((connection) => {
        if (connection?.receiver?._id === id || connection?.sender?._id === id)
          found = true;
      });

      if (!found) {
        navigate("/chat", { replace: true });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [connections]);

  useEffect(() => {
    dispatch(getConnections(ConnectionStatus.CONNECTED));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchReceiver = async (id: string) => {
    const response = await getUserData(id);
    if (isSuccess(response)) {
      setReceiver({ ...response.data, ...response.data.company[0] });
    } else {
      notify("Unable to fetch data!", "error");
    }
  };

  return (
    <div className="flex flex-row w-full h-screen bg-gradient-to-br from-slate-50 to-blue-50/30">
      <Helmet>
        <title>{title.CHAT}</title>
        <meta name="description" key="description" content={metaData.CHAT} />
        <meta name="title" key="title" content="Chat" />
        <meta property="og:title" content="Chat" />
        <meta property="og:description" content="Chat" />
        <meta property="og:image" content={NONE} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/chat`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Chat" />
        <meta name="twitter:description" content="Chat" />
        <meta name="twitter:image" content={NONE} />
        <meta name="twitter:card" content="Chat" />
      </Helmet>

      {/* Sidebar - Connections List */}
      <div className="flex flex-col w-80 min-w-[320px] bg-white shadow-xl border-r border-gray-200 h-full">
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-900 mb-4">
            Conversations
          </h2>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="w-5 h-5 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <input
              type="text"
              id="search-connections"
              className="w-full pl-10 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-colors duration-200"
              placeholder="Search conversations..."
              value={search.key}
              onChange={(e) => {
                setSearch({ key: e.target.value, process: true });
              }}
            />
          </div>
        </div>
        {/* Connections List */}
        <div className="flex-1 overflow-y-auto chat-scrollbar">
          {connections.CONNECTION_LIST.connectionsCount ? (
            connections.CONNECTION_LIST.connections
              .filter((item: any) => {
                if (item.receiver)
                  return item?.receiver?.fullName?.match(
                    new RegExp(search.key, "i")
                  );
                else
                  return item?.sender?.fullName?.match(
                    new RegExp(search.key, "i")
                  );
              })
              .map((item: any, index: number) => {
                const itemId =
                  item.sender?._id === currentUser.id
                    ? item.receiver?._id
                    : item.sender?._id;
                return (
                  <ConnectionsCard
                    key={index}
                    item={item}
                    handleView={handleView}
                    isActive={itemId === id}
                  />
                );
              })
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-8 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="w-8 h-8 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.471L3 21l2.471-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No conversations yet
              </h3>
              <p className="text-gray-500 text-sm">
                Start a conversation with your connections
              </p>
            </div>
          )}
        </div>
      </div>
      {/* Main Chat Area */}
      <div className="flex-1 flex">
        {receiver && id ? (
          <UserChat
            handleView={handleView}
            sender={currentUser}
            receiver={receiver}
          />
        ) : (
          <div className="flex-1 flex flex-col items-center justify-center bg-gray-50 text-center p-8">
            <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center mb-6 shadow-lg">
              <svg
                className="w-12 h-12 text-GTI-BLUE-default"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.471L3 21l2.471-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-3">
              Welcome to Messages
            </h2>
            <p className="text-gray-600 mb-6 max-w-md">
              Select a conversation from the sidebar to start chatting with your
              connections.
            </p>
            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 max-w-sm">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Get Started
              </h3>
              <p className="text-gray-600 text-sm">
                Choose a connection from your list to begin a conversation and
                stay connected with your network.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Chat;
