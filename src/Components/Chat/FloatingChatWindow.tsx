import React, { useState, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  IoSend,
  IoClose,
  IoRemove,
  IoExpand,
  IoEllipsisVertical,
} from "react-icons/io5";
import { Bs<PERSON><PERSON><PERSON><PERSON>ill, BsEmojiSmile } from "react-icons/bs";
import { FaUser } from "react-icons/fa";
import { getChat, sendChat } from "../../store/actioncreators/chatactions";
import { sendchatItem } from "../constants";
import defaultPic from "../../assests/images/default-user.svg";
import "./FloatingChat.css";

interface FloatingChatWindowProps {
  connectionId: string;
  connectionData: any;
  onClose: () => void;
  onMinimize: () => void;
  isMinimized: boolean;
  position: { bottom: number; right: number };
}

const FloatingChatWindow: React.FC<FloatingChatWindowProps> = ({
  connectionId,
  connectionData,
  onClose,
  onMinimize,
  isMinimized,
  position,
}) => {
  const dispatch = useDispatch<any>();
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [message, setMessage] = useState("");
  const [page, setPage] = useState({ skip: 0, limit: 10 });
  const [isTyping, setIsTyping] = useState(false);
  const [showOptions, setShowOptions] = useState(false);

  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  const chat_data: CHAT_STATE = useSelector((state: STATE) => state.CHAT);

  useEffect(() => {
    if (connectionId) {
      dispatch(
        getChat(connectionId, page.skip.toString(), page.limit.toString())
      );
    }
  }, [dispatch, connectionId]);

  useEffect(() => {
    if (chatContainerRef.current && !isMinimized) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [chat_data.CHAT_LIST.CHAT, isMinimized]);

  const handleSend = async () => {
    if (!message.trim()) return;

    const chatMessage: sendchatItem = {
      receiver: connectionId,
      message: message.trim(),
    };

    setMessage("");
    setIsTyping(true);

    try {
      dispatch(sendChat(chatMessage, page.skip, page.limit));

      if (chatContainerRef.current) {
        setTimeout(() => {
          const container = chatContainerRef.current;
          if (container) {
            container.scrollTop = container.scrollHeight;
          }
          setIsTyping(false);
        }, 500);
      }
    } catch (error) {
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const Sender = ({
    message,
    timestamp,
  }: {
    message: string;
    timestamp: string;
  }) => (
    <div className="flex justify-end mb-4 message-enter">
      <div className="max-w-[75%]">
        <div className="bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white px-4 py-2 rounded-2xl rounded-br-md shadow-md">
          <p className="text-sm leading-relaxed">{message}</p>
        </div>
        <div className="flex items-center justify-end gap-1 mt-1">
          <p className="text-xs text-gray-500">{timestamp}</p>
          <div className="w-1 h-1 bg-green-500 rounded-full"></div>
        </div>
      </div>
    </div>
  );

  const Receiver = ({
    message,
    timestamp,
  }: {
    message: string;
    timestamp: string;
  }) => (
    <div className="flex justify-start mb-4 message-enter">
      <div className="flex items-start gap-2 max-w-[75%]">
        <img
          src={connectionData?.profileImage || defaultPic}
          alt={connectionData?.fullName || "User"}
          className="w-6 h-6 rounded-full object-cover mt-1 flex-shrink-0"
        />
        <div>
          <div className="bg-gray-100 text-gray-900 px-4 py-2 rounded-2xl rounded-bl-md shadow-sm">
            <p className="text-sm leading-relaxed">{message}</p>
          </div>
          <p className="text-xs text-gray-500 mt-1 ml-1">{timestamp}</p>
        </div>
      </div>
    </div>
  );

  if (isMinimized) {
    return (
      <div
        className="fixed bg-GTI-BLUE-default text-white rounded-t-lg shadow-lg cursor-pointer hover:bg-GTI-BLUE-default/90 transition-colors z-40 chat-minimize"
        style={{
          bottom: 0,
          right: window.innerWidth <= 768 ? 20 : position.right,
          left: window.innerWidth <= 768 ? 20 : "auto",
        }}
        onClick={onMinimize}
      >
        <div className="flex items-center justify-between p-3 w-64">
          <div className="flex items-center gap-2">
            <img
              src={connectionData?.profileImage || defaultPic}
              alt={connectionData?.fullName || "User"}
              className="w-6 h-6 rounded-full object-cover"
            />
            <span className="font-medium text-sm truncate">
              {connectionData?.fullName || "Unknown User"}
            </span>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="p-1 hover:bg-white/20 rounded transition-colors"
          >
            <IoClose className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className="fixed bg-white rounded-t-lg shadow-2xl border border-gray-200 z-40 chat-window chat-window-enter"
      style={{
        bottom: 0,
        right: window.innerWidth <= 768 ? 20 : position.right,
        left: window.innerWidth <= 768 ? 20 : "auto",
        width: window.innerWidth <= 768 ? "calc(100vw - 40px)" : "320px",
        height: "400px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white rounded-t-lg shadow-lg">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="relative">
            <img
              src={connectionData?.profileImage || defaultPic}
              alt={connectionData?.fullName || "User"}
              className="w-8 h-8 rounded-full object-cover border-2 border-white/30"
            />
            <div className="absolute -bottom-0.5 -right-0.5">
              <BsCircleFill className="w-3 h-3 text-white" />
              <BsCircleFill className="w-2 h-2 text-green-500 absolute top-0.5 left-0.5" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-sm truncate">
              {connectionData?.fullName || "Unknown User"}
            </h3>
            <p className="text-xs text-blue-100 truncate">
              {isTyping ? "You are typing..." : "Active now"}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <button
            onClick={() => setShowOptions(!showOptions)}
            className="p-1.5 hover:bg-white/20 rounded-full transition-all duration-200 relative"
          >
            <IoEllipsisVertical className="w-4 h-4" />
            {showOptions && (
              <div className="absolute top-8 right-0 bg-white text-gray-700 rounded-lg shadow-xl border py-2 z-50 min-w-[120px]">
                <button className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2">
                  <FaUser className="w-3 h-3" />
                  View Profile
                </button>
              </div>
            )}
          </button>
          <button
            onClick={onMinimize}
            className="p-1.5 hover:bg-white/20 rounded-full transition-all duration-200 hover:scale-110"
          >
            <IoRemove className="w-4 h-4" />
          </button>
          <button
            onClick={onClose}
            className="p-1.5 hover:bg-white/20 rounded-full transition-all duration-200 hover:scale-110"
          >
            <IoClose className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Chat Messages */}
      <div
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto p-3 bg-gray-50"
        style={{ maxHeight: "280px" }}
      >
        {chat_data && chat_data.CHAT_LIST.CHAT.length > 0 ? (
          chat_data.CHAT_LIST.CHAT.map((item: any, index: number) => {
            const timestamp = new Date(item.createdAt).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            });

            if (item.user._id === currentUser.id) {
              return (
                <Sender
                  key={index}
                  message={item?.message}
                  timestamp={timestamp}
                />
              );
            } else {
              return (
                <Receiver
                  key={index}
                  message={item?.message}
                  timestamp={timestamp}
                />
              );
            }
          })
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p className="text-sm">Start a conversation</p>
          </div>
        )}
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
        <div className="flex items-end gap-3">
          <div className="flex-1 relative">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="w-full px-4 py-3 pr-12 text-sm border border-gray-200 rounded-2xl bg-white focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-200 placeholder-gray-400"
            />
            <button
              onClick={() => {
                /* Add emoji picker functionality */
              }}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <BsEmojiSmile className="w-4 h-4" />
            </button>
          </div>
          <button
            onClick={handleSend}
            disabled={!message.trim() || isTyping}
            className={`p-3 rounded-full transition-all duration-200 ${
              message.trim() && !isTyping
                ? "bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 shadow-lg"
                : "bg-gray-200 text-gray-400 cursor-not-allowed"
            }`}
          >
            {isTyping ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <IoSend className="w-4 h-4" />
            )}
          </button>
        </div>

        {/* Typing Indicator */}
        {isTyping && (
          <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
            <div className="flex gap-1">
              <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"></div>
              <div
                className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "0.1s" }}
              ></div>
              <div
                className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: "0.2s" }}
              ></div>
            </div>
            <span>Sending...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default FloatingChatWindow;
