import React, { useState, useCallback } from "react";
import FloatingChatWidget from "./FloatingChatWidget";
import FloatingChatWindow from "./FloatingChatWindow";

interface ChatWindow {
  id: string;
  connectionData: any;
  isMinimized: boolean;
}

const ChatManager: React.FC = () => {
  const [openChats, setOpenChats] = useState<ChatWindow[]>([]);

  const handleChatOpen = useCallback(
    (connectionId: string, connectionData: any) => {
      setOpenChats((prev) => {
        // Check if chat is already open
        const existingChat = prev.find((chat) => chat.id === connectionId);
        if (existingChat) {
          // If minimized, restore it
          if (existingChat.isMinimized) {
            return prev.map((chat) =>
              chat.id === connectionId ? { ...chat, isMinimized: false } : chat
            );
          }
          return prev;
        }

        // Add new chat window
        const newChat: ChatWindow = {
          id: connectionId,
          connectionData,
          isMinimized: false,
        };

        return [...prev, newChat];
      });
    },
    []
  );

  const handleChatClose = useCallback((connectionId: string) => {
    setOpenChats((prev) => prev.filter((chat) => chat.id !== connectionId));
  }, []);

  const handleChatMinimize = useCallback((connectionId: string) => {
    setOpenChats((prev) =>
      prev.map((chat) =>
        chat.id === connectionId
          ? { ...chat, isMinimized: !chat.isMinimized }
          : chat
      )
    );
  }, []);

  const calculateChatPosition = (index: number, isMinimized: boolean) => {
    const baseRight = 100; // Base position from right edge
    const chatWidth = isMinimized ? 264 : 320; // Width of chat window
    const spacing = 10; // Spacing between windows

    // On mobile, stack chats vertically instead of horizontally
    if (window.innerWidth <= 768) {
      return 20; // Fixed position on mobile
    }

    return baseRight + index * (chatWidth + spacing);
  };

  return (
    <>
      {/* Floating Chat Widget */}
      <FloatingChatWidget
        onChatOpen={handleChatOpen}
        openChats={openChats.map((chat) => chat.id)}
      />

      {/* Open Chat Windows */}
      {openChats.map((chat, index) => (
        <FloatingChatWindow
          key={chat.id}
          connectionId={chat.id}
          connectionData={chat.connectionData}
          onClose={() => handleChatClose(chat.id)}
          onMinimize={() => handleChatMinimize(chat.id)}
          isMinimized={chat.isMinimized}
          position={{
            bottom: 0,
            right: calculateChatPosition(index, chat.isMinimized),
          }}
        />
      ))}
    </>
  );
};

export default ChatManager;
