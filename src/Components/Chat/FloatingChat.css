/* Floating Chat System Styles */

/* Chat Widget Animation */
.floating-chat-widget {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.floating-chat-widget::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
  pointer-events: none;
}

.floating-chat-widget:hover::before {
  left: 100%;
}

.floating-chat-widget:hover {
  transform: scale(1.05) rotate(2deg);
  box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.3),
    0 10px 10px -5px rgba(59, 130, 246, 0.1);
}

/* Chat List Animation */
.chat-list-enter {
  animation: slideUpFadeIn 0.3s ease-out forwards;
}

.chat-list-exit {
  animation: slideDownFadeOut 0.3s ease-in forwards;
}

@keyframes slideUpFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideDownFadeOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
}

/* Chat Window Animation */
.chat-window-enter {
  animation: chatWindowSlideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-window-exit {
  animation: chatWindowSlideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes chatWindowSlideUp {
  from {
    opacity: 0;
    transform: translateY(100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes chatWindowSlideDown {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(100%) scale(0.9);
  }
}

/* Minimize/Maximize Animation */
.chat-minimize {
  animation: minimizeChat 0.3s ease-out forwards;
}

.chat-maximize {
  animation: maximizeChat 0.3s ease-out forwards;
}

@keyframes minimizeChat {
  from {
    height: 400px;
    opacity: 1;
  }
  to {
    height: 48px;
    opacity: 0.9;
  }
}

@keyframes maximizeChat {
  from {
    height: 48px;
    opacity: 0.9;
  }
  to {
    height: 400px;
    opacity: 1;
  }
}

/* Message Animations */
.message-enter {
  animation: messageSlideIn 0.3s ease-out forwards;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover Effects */
.connection-item {
  position: relative;
  overflow: hidden;
}

.connection-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  transition: left 0.3s ease;
}

.connection-item:hover::before {
  left: 100%;
}

.connection-item:hover {
  background-color: rgba(59, 130, 246, 0.05);
  transform: translateX(4px) scale(1.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.chat-button:hover {
  background-color: rgba(59, 130, 246, 0.9);
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Interactive Message Bubbles */
.message-bubble {
  transition: all 0.2s ease;
  cursor: pointer;
}

.message-bubble:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Scrollbar Styling */
.chat-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Online Status Pulse */
.online-status {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .floating-chat-widget {
    bottom: 20px;
    right: 20px;
  }

  .chat-window {
    width: calc(100vw - 40px) !important;
    right: 20px !important;
    left: 20px !important;
  }

  .chat-list {
    width: calc(100vw - 40px) !important;
    right: 20px !important;
    left: 20px !important;
  }
}

/* Focus States */
.chat-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Loading States */
.chat-loading {
  opacity: 0.6;
  pointer-events: none;
}

.chat-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  animation: bounce 0.5s ease-out;
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Floating Animation for Chat Widget */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

/* Glow Effect for Active Chats */
.active-chat {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  to {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
  }
}

/* Ripple Effect for Buttons */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::after {
  width: 300px;
  height: 300px;
}

/* Enhanced Focus States */
.enhanced-focus:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Gradient Text Animation */
.gradient-text {
  background: linear-gradient(-45deg, #3b82f6, #1d4ed8, #2563eb, #1e40af);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
