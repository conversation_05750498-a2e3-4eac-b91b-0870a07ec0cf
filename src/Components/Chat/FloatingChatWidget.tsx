import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  IoChatbubbleEllipsesOutline,
  IoClose,
  IoRemove,
  IoSearch,
  IoPeople,
} from "react-icons/io5";
import { FaUsers, FaComments, FaUserFriends } from "react-icons/fa";
import { BsChatDots, BsCircleFill } from "react-icons/bs";
import { getConnections } from "../../store/actioncreators/connectionactions";
import { ConnectionStatus } from "../constants";
import defaultPic from "../../assests/images/default-user.svg";
import "./FloatingChat.css";

interface FloatingChatWidgetProps {
  onChatOpen: (connectionId: string, connectionData: any) => void;
  openChats: string[];
}

interface ConnectionUser {
  _id: string;
  fullName: string;
  profileImage?: string;
  email?: string;
  isOnline?: boolean;
}

const FloatingChatWidget: React.FC<FloatingChatWidgetProps> = ({
  onChatOpen,
  openChats,
}) => {
  const dispatch = useDispatch<any>();
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [hoveredConnection, setHoveredConnection] = useState<string | null>(
    null
  );

  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  const connections: CONNECTION = useSelector(
    (state: STATE) => state.CONNECTION.CONNECTION
  );

  useEffect(() => {
    if (currentUser.admin !== -1) {
      setIsLoading(true);
      dispatch(getConnections(ConnectionStatus.CONNECTED));
      // Simulate loading delay for better UX
      setTimeout(() => setIsLoading(false), 1000);
    }
  }, [dispatch, currentUser.admin]);

  const filteredConnections =
    connections?.CONNECTION_LIST?.connections?.filter((connection: any) => {
      // Determine which user is the "other" user (not the current user)
      const connectionUser =
        connection.sender?._id === currentUser.id
          ? connection.receiver
          : connection.sender;
      const name = connectionUser?.fullName || "";
      return name.toLowerCase().includes(searchTerm.toLowerCase());
    }) || [];

  const handleConnectionClick = (connection: any) => {
    // Determine which user is the "other" user (not the current user)
    const connectionUser =
      connection.sender?._id === currentUser.id
        ? connection.receiver
        : connection.sender;
    if (
      connectionUser &&
      connectionUser._id &&
      !openChats.includes(connectionUser._id)
    ) {
      onChatOpen(connectionUser._id, connectionUser);
    }
    setIsExpanded(false);
  };

  // Don't render if user is not logged in
  if (currentUser.admin === -1) {
    return null;
  }

  return (
    <div className="fixed bottom-0 right-6 z-50">
      {/* Expanded Chat List */}
      {isExpanded && (
        <div
          className="mb-4 bg-white rounded-t-lg shadow-2xl border border-gray-200 max-h-96 flex flex-col chat-list chat-list-enter"
          style={{
            width: window.innerWidth <= 768 ? "calc(100vw - 40px)" : "320px",
            right: window.innerWidth <= 768 ? 20 : "auto",
            left: window.innerWidth <= 768 ? 20 : "auto",
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white rounded-t-lg shadow-lg">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-full">
                <FaComments className="w-4 h-4" />
              </div>
              <div>
                <span className="font-semibold text-sm">Conversations</span>
                <p className="text-xs text-blue-100">
                  {filteredConnections.length} connection
                  {filteredConnections.length !== 1 ? "s" : ""}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {openChats.length > 0 && (
                <div className="flex items-center gap-1 px-2 py-1 bg-white/20 rounded-full">
                  <BsChatDots className="w-3 h-3" />
                  <span className="text-xs font-medium">
                    {openChats.length}
                  </span>
                </div>
              )}
              <button
                onClick={() => setIsExpanded(false)}
                className="p-1.5 hover:bg-white/20 rounded-full transition-all duration-200 hover:scale-110"
              >
                <IoRemove className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Search */}
          <div className="p-4 border-b border-gray-100 bg-gray-50">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <IoSearch className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 text-sm border border-gray-200 rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-200 placeholder-gray-400 enhanced-focus"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <IoClose className="h-4 w-4 text-gray-400 hover:text-gray-600 transition-colors" />
                </button>
              )}
            </div>
          </div>

          {/* Connections List */}
          <div className="flex-1 overflow-y-auto chat-scrollbar">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex flex-col items-center gap-3">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-GTI-BLUE-default"></div>
                  <p className="text-sm text-gray-500">
                    Loading connections...
                  </p>
                </div>
              </div>
            ) : filteredConnections.length > 0 ? (
              filteredConnections.map((connection: any, index: number) => {
                // Determine which user is the "other" user (not the current user)
                const connectionUser =
                  connection.sender?._id === currentUser.id
                    ? connection.receiver
                    : connection.sender;
                const isAlreadyOpen = connectionUser?._id
                  ? openChats.includes(connectionUser._id)
                  : false;
                const isHovered = hoveredConnection === connectionUser?._id;

                return (
                  <div
                    key={connectionUser?._id || index}
                    onClick={() => handleConnectionClick(connection)}
                    onMouseEnter={() =>
                      setHoveredConnection(connectionUser?._id || null)
                    }
                    onMouseLeave={() => setHoveredConnection(null)}
                    className={`flex items-center gap-3 p-4 cursor-pointer border-b border-gray-50 last:border-b-0 transition-all duration-200 connection-item ${
                      isAlreadyOpen
                        ? "bg-blue-50 border-l-4 border-l-GTI-BLUE-default"
                        : isHovered
                        ? "bg-gray-50 transform translate-x-1"
                        : "hover:bg-gray-50"
                    }`}
                  >
                    <div className="relative">
                      <img
                        src={connectionUser?.profileImage || defaultPic}
                        alt={connectionUser?.fullName || "User"}
                        className={`w-12 h-12 rounded-full object-cover border-2 transition-all duration-200 ${
                          isAlreadyOpen
                            ? "border-GTI-BLUE-default shadow-lg"
                            : "border-white shadow-md"
                        }`}
                      />
                      <div className="absolute -bottom-1 -right-1">
                        <div className="relative">
                          <BsCircleFill className="w-4 h-4 text-white" />
                          <BsCircleFill className="w-3 h-3 text-green-500 absolute top-0.5 left-0.5 online-status" />
                        </div>
                      </div>
                      {isAlreadyOpen && (
                        <div className="absolute -top-1 -left-1 w-4 h-4 bg-GTI-BLUE-default rounded-full flex items-center justify-center">
                          <BsChatDots className="w-2 h-2 text-white" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <p
                          className={`font-medium text-sm truncate ${
                            isAlreadyOpen
                              ? "text-GTI-BLUE-default"
                              : "text-gray-900"
                          }`}
                        >
                          {connectionUser?.fullName || "Unknown User"}
                        </p>
                        {isHovered && !isAlreadyOpen && (
                          <div className="flex items-center gap-1 text-GTI-BLUE-default">
                            <BsChatDots className="w-3 h-3" />
                          </div>
                        )}
                      </div>
                      <p
                        className={`text-xs truncate ${
                          isAlreadyOpen
                            ? "text-blue-600 font-medium"
                            : "text-gray-500"
                        }`}
                      >
                        {isAlreadyOpen
                          ? "💬 Chat active"
                          : isHovered
                          ? "Click to start chatting"
                          : "Available to chat"}
                      </p>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="p-8 text-center text-gray-500">
                <div className="mb-4">
                  {searchTerm ? (
                    <IoSearch className="w-12 h-12 mx-auto text-gray-300" />
                  ) : (
                    <FaUserFriends className="w-12 h-12 mx-auto text-gray-300" />
                  )}
                </div>
                <h3 className="font-medium text-gray-700 mb-2">
                  {searchTerm ? "No matches found" : "No connections yet"}
                </h3>
                <p className="text-sm text-gray-500">
                  {searchTerm
                    ? "Try searching with a different name"
                    : "Connect with people to start chatting"}
                </p>
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm("")}
                    className="mt-3 px-4 py-2 text-sm text-GTI-BLUE-default hover:bg-blue-50 rounded-lg transition-colors ripple"
                  >
                    Clear search
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Chat Widget Button */}
      <div className="relative">
        <div
          onClick={() => setIsExpanded(!isExpanded)}
          className="bg-gradient-to-r from-GTI-BLUE-default to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white p-4 rounded-full shadow-xl cursor-pointer floating-chat-widget chat-button transform transition-all duration-300 hover:scale-110"
        >
          {isExpanded ? (
            <IoClose className="w-6 h-6" />
          ) : (
            <IoChatbubbleEllipsesOutline className="w-6 h-6" />
          )}
        </div>

        {/* Notification Badge */}
        {!isExpanded && openChats.length > 0 && (
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg notification-badge">
            {openChats.length > 9 ? "9+" : openChats.length}
          </div>
        )}

        {/* Pulse Animation for New Messages */}
        {!isExpanded && (
          <div className="absolute inset-0 rounded-full bg-GTI-BLUE-default opacity-75 animate-ping pointer-events-none"></div>
        )}
      </div>
    </div>
  );
};

export default FloatingChatWidget;
