import React, { useState } from "react";
import EmployeeList from "./EmployeeList";
import RequestList from "./RequestList";

const EmployeeManagement = () => {

    const [emp_tabdrop, setEmpDrop] = useState(false);
    const [emp_tab, setEmpTab] = useState(1);
    const [currentTab, setTabValue] = useState('New Requests');
    const handleEmpTab = (value: any) => {

        if (value === 1) {
            setTabValue('New Requests');
        }
        else {
            setTabValue('Existing Employee');
        }
        setEmpTab(value);
        setEmpDrop(false);

    }

    return (



        <div className="flex flex-col w-full justify-center space-x-5 duration-200 ease-in-out">
            <div className="flex flex-row  w-full justify-end z-10 px-5 py-10">
                <button id="dropdownDefault" data-dropdown-toggle="dropdown" className="w-fit text-white border-2 bg-GTI-BLUE-default focus:outline-none font-medium font-roboto rounded-lg m-1 text-sm px-4 py-2.5  text-center inline-flex items-center border-slate-300 justify-center flex-shrink" type="button" onFocus={() => { setEmpDrop(!emp_tabdrop) }}>{currentTab}<svg className="ml-2 w-4 h-4" aria-hidden="true" fill="grey" stroke="grey" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button>
                <div id="dropdown" className={"relative z-10 rounded " + (!emp_tabdrop ? "hidden" : "")} data-popper-placement="bottom">
                    <ul className="absolute flex flex-col  -left-40  top-14 text-sm font-roboto bg-slate-100" aria-labelledby="dropdownDefault">
                        <li className="block z-10 py-2 px-4 rounded  text-GTI-BLUE-default  hover:text-slate-500 " onClick={() => { handleEmpTab(1) }}>New Requests
                        </li>
                        <li className="block py-2 px-4 rounded  text-GTI-BLUE-default  hover:text-slate-500 " onClick={() => { handleEmpTab(2) }}>Existing Employees
                        </li>
                    </ul>
                </div>
            </div>
            <div className="flex  w-full h-fit py-4 justify-center">
                {
                    emp_tab == 1 ?
                        <RequestList />
                        :
                        <EmployeeList />

                }
            </div>

        </div>
    );

}
export default EmployeeManagement;