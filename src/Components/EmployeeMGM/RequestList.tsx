import React, { useState, useEffect } from "react";
import Card from "../Pager/Card";
import pic from "../../assests/test/product/product3.jpg";
import nothing from "../../assests/response/nothing.gif";
import { useSelector } from "react-redux";
import axios from "axios";
import { empData, empItem } from "../Modules/getData";

const RequestList = () => {
  let res;
  let [req_list, setList] = useState<empData>([]);
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  let [userModalShow, userModalSet] = useState(false);
  let [selected_emp, Pick] = useState("");
  let handleSelection = () => {};
  let org_name = currentUser?.company[0]?.name ?? "";

  useEffect(() => {
    pullData();
  }, []);

  let pullData = async () => {
    var data = "";

    var config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/users/getcompanynewusers`,
      headers: {
        Authorization: `Bearer ${currentUser.token}`,
      },
      data: data,
    };

    await axios(config)
      .then(function (response) {
        res = response.data.map((item: any, key: number) => {
          // console.log(key,item);
          let temp = {
            id: key,
            title: item.fullName,
            email: item.email,
          };
          return temp;
        });
        setList(res);
      })
      .catch(function (error) {
        // console.log("error in getting new users", error);
      });
  };

  return (
    <div className="grid grid-cols-5">
      {req_list.map(({ id, title, email }: any) => {
        return (
          <Card key={id} title={title} org={org_name} Img={pic} email={email} />
        );
      })}
    </div>
  );
};
export default RequestList;
