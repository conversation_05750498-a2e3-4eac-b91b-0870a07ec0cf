import axios from "axios";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import testpic from "../../assests/test/product/product4.jpg";
import { empData } from "../Modules/getData";
import { ConnectionStatus } from "../constants";
import "./style.css";

const Card2 = ({
  Img,
  title,
  org,
  email,
  user,
  status,
}: {
  Img: string;
  title: string;
  org: string;
  email: string;
  user: any;
  status?: ConnectionStatus;
}) => {
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  const profile = false;
  const navigate = useNavigate();
  return (
    <div className="w-full max-w-sm px-5 py-7 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
      <div className="flex flex-col items-center ">
        <img
          className="w-24 h-24 mb-3 rounded-full shadow-lg text-GTI-BLUE-default"
          src={Img}
          alt={title}
        />
        <h5 className="mb-1 text-xl font-medium text-gray-900 dark:text-white">
          {title}
        </h5>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {email}
        </span>
        <div className="flex mt-4 space-x-3 md:mt-6">
          <a
            href="#"
            className="inline-flex w-full items-center px-4 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
          >
            View Profile
          </a>
        </div>
      </div>
    </div>
  );
};

const EmployeeList = () => {
  let res;
  let [req_list, setList] = useState<empData>([]);
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);

  useEffect(() => {
    pullData();
  }, []);

  let pullData = async () => {
    var data = "";

    var config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/users/getcompanyexistingusers`,
      headers: {
        Authorization: `Bearer ${currentUser.token}`,
      },
      data: data,
    };

    await axios(config)
      .then(function (response) {
        res = response.data.map((item: any, key: number) => {
          let temp = {
            id: key,
            title: item.fullName,
            email: item.email,
          };
          return temp;
        });
        setList(res);
      })
      .catch(function (error) {});
  };

  let org_name = currentUser?.company[0]?.name ?? "";

  return (
    <div className="grid grid-cols-5">
      {req_list.map((user: any) => {
        return (
          <Card2
            key={user.id}
            user={user}
            title={user.title}
            org={user.org_name}
            email={user.email}
            Img={testpic}
          />
        );
      })}
    </div>
  );
};
export default EmployeeList;
