import React, { useState, useRef, useEffect } from "react";
import { IoSend, IoClose, IoRemove } from "react-icons/io5";
import { ChatbotWindowProps } from "./types/chatbot.types";
import ChatbotMessage from "./ChatbotMessage";
import ChatbotTyping from "./ChatbotTyping";
import ChatbotSuggestions from "./ChatbotSuggestions";

const ChatbotWindow: React.FC<ChatbotWindowProps> = ({
  messages,
  onSendMessage,
  onClose,
  isTyping,
  suggestions,
  onSuggestionClick,
  onMinimize,
  isMinimized,
}) => {
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isTyping]);

  // Focus input when window opens
  useEffect(() => {
    if (!isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isMinimized]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      onSendMessage(inputValue.trim());
      setInputValue("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  if (isMinimized) {
    return (
      <div className="chatbot-window fixed bottom-24 right-24 z-[9998] w-64 bg-white rounded-t-lg shadow-xl border border-gray-200">
        <div className="flex items-center justify-between p-3 bg-GTI-BLUE-default text-white rounded-t-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span className="text-sm font-medium">GTI Support</span>
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={onMinimize}
              className="p-1 hover:bg-blue-600 rounded transition-colors"
              aria-label="Restore chat"
            >
              <IoRemove className="w-4 h-4" />
            </button>
            <button
              onClick={onClose}
              className="p-1 hover:bg-blue-600 rounded transition-colors"
              aria-label="Close chat"
            >
              <IoClose className="w-4 h-4" />
            </button>
          </div>
        </div>
        <div className="p-3 text-sm text-gray-600 text-center">
          Chat minimized. Click to restore.
        </div>
      </div>
    );
  }

  return (
    <div className="chatbot-window fixed bottom-24 right-24 z-[9998] w-80 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col animate-fade-in-up">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-GTI-BLUE-default text-white rounded-t-lg">
        <div className="flex items-center space-x-3">
          <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          <div>
            <h3 className="font-semibold text-sm">GTI Support</h3>
            <p className="text-xs opacity-90">We typically reply instantly</p>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={onMinimize}
            className="p-1 hover:bg-blue-600 rounded transition-colors"
            aria-label="Minimize chat"
          >
            <IoRemove className="w-4 h-4" />
          </button>
          <button
            onClick={onClose}
            className="p-1 hover:bg-blue-600 rounded transition-colors"
            aria-label="Close chat"
          >
            <IoClose className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2 bg-gray-50">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 text-sm py-8">
            <div className="w-12 h-12 bg-GTI-BLUE-default rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-white text-lg">👋</span>
            </div>
            <p>Welcome to GTI Support!</p>
            <p className="text-xs mt-1">How can we help you today?</p>
          </div>
        ) : (
          messages.map((message, index) => (
            <ChatbotMessage
              key={message.id}
              message={message}
              isLast={index === messages.length - 1}
            />
          ))
        )}

        {isTyping && <ChatbotTyping />}
        <div ref={messagesEndRef} />
      </div>

      {/* Suggestions */}
      <ChatbotSuggestions
        suggestions={suggestions}
        onSuggestionClick={onSuggestionClick}
      />

      {/* Input Area */}
      <div className="p-4 border-t border-gray-200 bg-white rounded-b-lg">
        <form onSubmit={handleSubmit} className="flex items-center space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            className="
              flex-1 px-4 py-3 border border-gray-300 rounded-full text-base
              focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default
              transition-colors duration-200 min-h-[48px]
            "
            disabled={isTyping}
          />
          <button
            type="submit"
            disabled={!inputValue.trim() || isTyping}
            className="
              p-3 bg-GTI-BLUE-default text-white rounded-full hover:bg-blue-600
              disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200
              transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20
              min-w-[48px] min-h-[48px] flex items-center justify-center
            "
            aria-label="Send message"
          >
            <IoSend className="w-5 h-5" />
          </button>
        </form>

        {/* Powered by text */}
        <div className="text-center mt-2">
          <span className="text-xs text-gray-400">Powered by GTI Support</span>
        </div>
      </div>
    </div>
  );
};

export default ChatbotWindow;
