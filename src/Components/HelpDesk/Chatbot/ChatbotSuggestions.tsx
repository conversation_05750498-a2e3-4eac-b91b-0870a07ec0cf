import React from 'react';

interface ChatbotSuggestionsProps {
  suggestions: string[];
  onSuggestionClick: (suggestion: string) => void;
}

const ChatbotSuggestions: React.FC<ChatbotSuggestionsProps> = ({
  suggestions,
  onSuggestionClick
}) => {
  if (suggestions.length === 0) return null;

  return (
    <div className="px-4 pb-4">
      <div className="flex flex-wrap gap-2">
        {suggestions.map((suggestion, index) => (
          <button
            key={index}
            onClick={() => onSuggestionClick(suggestion)}
            className="
              inline-flex items-center px-3 py-2 text-xs font-medium text-GTI-BLUE-default 
              bg-blue-50 border border-blue-200 rounded-full hover:bg-blue-100 
              hover:border-blue-300 transition-all duration-200 transform hover:scale-105
              focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20
            "
          >
            {suggestion}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ChatbotSuggestions;
