// Chatbot TypeScript interfaces and types

export interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  suggestions?: string[];
  category?: string;
}

export interface ChatbotState {
  messages: ChatMessage[];
  isTyping: boolean;
  currentContext: string;
  userSession: string;
  isOpen: boolean;
}

export interface ChatbotWidgetProps {
  onToggle: () => void;
  isOpen: boolean;
  unreadCount?: number;
  position?: 'bottom-right' | 'bottom-left';
}

export interface ChatbotWindowProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  onClose: () => void;
  isTyping: boolean;
  suggestions: string[];
  onSuggestionClick: (suggestion: string) => void;
  onMinimize: () => void;
  isMinimized: boolean;
}

export interface ChatbotMessageProps {
  message: ChatMessage;
  isLast: boolean;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  keywords: string[];
  category: 'general' | 'technical' | 'services' | 'contact' | 'pricing' | 'account';
  priority: number;
  followUpSuggestions?: string[];
}

export interface QuickReply {
  id: string;
  text: string;
  category: string;
  response: string;
  followUp?: string[];
}

export interface ChatbotResponse {
  message: string;
  suggestions?: string[];
  category?: string;
  confidence: number;
  requiresEscalation?: boolean;
}

export interface KeywordPattern {
  keywords: string[];
  response: string;
  category: string;
  priority: number;
  suggestions?: string[];
}

export interface ChatbotConfig {
  typingDelay: number;
  maxSuggestions: number;
  sessionTimeout: number;
  defaultResponses: {
    greeting: string;
    fallback: string;
    goodbye: string;
    escalation: string;
  };
}

export interface UserSession {
  id: string;
  startTime: Date;
  lastActivity: Date;
  messageCount: number;
  context: string[];
  escalated: boolean;
}
