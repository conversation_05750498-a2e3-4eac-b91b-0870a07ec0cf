import React from "react";
import { IoChatbubbleEllipsesOutline, IoClose } from "react-icons/io5";
import { ChatbotWidgetProps } from "./types/chatbot.types";

const ChatbotWidget: React.FC<ChatbotWidgetProps> = ({
  onToggle,
  isOpen,
  unreadCount = 0,
  position = "bottom-right",
}) => {
  const positionClasses = {
    "bottom-right": "bottom-6 right-6",
    "bottom-left": "bottom-6 left-6",
  };

  return (
    <div
      className={`chatbot-widget fixed ${positionClasses[position]} z-[9999]`}
    >
      {/* Chatbot Widget Button */}
      <div className="relative">
        <button
          onClick={onToggle}
          className={`
            chatbot-widget-button group relative w-16 h-16 rounded-full shadow-2xl cursor-pointer
            transform transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-GTI-BLUE-default/20
            ${
              isOpen
                ? "bg-gray-600 hover:bg-gray-700"
                : "bg-gradient-to-r from-GTI-BLUE-default to-blue-600 hover:from-blue-600 hover:to-blue-700"
            }
          `}
          aria-label={isOpen ? "Close chat" : "Open chat"}
        >
          {/* Icon */}
          <div className="flex items-center justify-center w-full h-full text-white">
            {isOpen ? (
              <IoClose className="w-7 h-7 transition-transform duration-200" />
            ) : (
              <IoChatbubbleEllipsesOutline className="w-7 h-7 transition-transform duration-200 group-hover:scale-110" />
            )}
          </div>

          {/* Pulse Animation Ring */}
          {!isOpen && (
            <div className="absolute inset-0 rounded-full bg-GTI-BLUE-default opacity-20 animate-ping"></div>
          )}

          {/* Notification Badge */}
          {!isOpen && unreadCount > 0 && (
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg animate-bounce">
              {unreadCount > 9 ? "9+" : unreadCount}
            </div>
          )}
        </button>

        {/* Tooltip */}
        {!isOpen && (
          <div className="absolute bottom-full right-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
            <div className="bg-gray-900 text-white text-sm px-3 py-2 rounded-lg whitespace-nowrap shadow-lg">
              Need help? Chat with us!
              <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
          </div>
        )}
      </div>

      {/* GTI Logo - Always Visible */}
      <div className="gti-logo-badge">
        <div className="w-8 h-8 bg-GTI-BLUE-default rounded-full flex items-center justify-center">
          <span className="text-white text-xs font-bold">GTI</span>
        </div>
      </div>
    </div>
  );
};

export default ChatbotWidget;
