/* Chatbot Animation Styles */

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@keyframes bounce-gentle {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fade-in-up 0.4s ease-out forwards;
}

.animate-fade-in-down {
  animation: fade-in-down 0.4s ease-out forwards;
}

.animate-fade-in-left {
  animation: fade-in-left 0.4s ease-out forwards;
}

.animate-fade-in-right {
  animation: fade-in-right 0.4s ease-out forwards;
}

.animate-pulse-ring {
  animation: pulse-ring 2s infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s infinite;
}

/* Chatbot Widget Styles */
.chatbot-widget {
  position: fixed !important;
  bottom: 24px !important;
  right: 24px !important;
  z-index: 9999 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
}

.chatbot-widget:hover {
  transform: scale(1.05);
}

/* GTI Logo - Always Visible */
.gti-logo-badge {
  position: absolute !important;
  top: -48px;
  right: 8px;
  background: white;
  border-radius: 50%;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 2px solid #f3f4f6;
  z-index: 10000 !important;
  animation: gentle-bounce 3s ease-in-out infinite;
  pointer-events: none;
}

@keyframes gentle-bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

.chatbot-widget-button {
  position: relative;
  overflow: hidden;
}

.chatbot-widget-button::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.chatbot-widget-button:active::before {
  width: 300px;
  height: 300px;
}

/* Chatbot Window Styles */
.chatbot-window {
  position: fixed !important;
  z-index: 9998 !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  pointer-events: auto;
}

.chatbot-messages {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.chatbot-messages::-webkit-scrollbar {
  width: 6px;
}

.chatbot-messages::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.chatbot-messages::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.chatbot-messages::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Message Bubble Styles */
.message-bubble {
  position: relative;
  word-wrap: break-word;
  max-width: 100%;
}

.message-bubble-user {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
}

.message-bubble-bot {
  background: #f3f4f6;
  color: #374151;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 12px 16px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #9ca3af;
  margin: 0 2px;
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Suggestion Buttons */
.suggestion-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.suggestion-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.suggestion-button:hover::before {
  left: 100%;
}

.suggestion-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .chatbot-window {
    width: calc(100vw - 32px) !important;
    height: calc(100vh - 100px) !important;
    right: 16px !important;
    bottom: 80px !important;
    left: 16px !important;
  }

  .chatbot-widget {
    position: fixed !important;
    right: 16px !important;
    bottom: 16px !important;
  }

  .gti-logo-badge {
    right: 4px;
    top: -40px;
    padding: 6px;
  }

  .gti-logo-badge .w-8 {
    width: 1.5rem;
    height: 1.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .chatbot-window {
    background: #1f2937;
    border-color: #374151;
  }

  .message-bubble-bot {
    background: #374151;
    color: #f9fafb;
  }

  .chatbot-messages {
    scrollbar-color: #4b5563 #1f2937;
  }
}

/* Accessibility */
.chatbot-widget:focus,
.suggestion-button:focus,
.chatbot-input:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .chatbot-widget {
    border: 2px solid #000;
  }

  .message-bubble-user {
    background: #000;
    color: #fff;
  }

  .message-bubble-bot {
    background: #fff;
    color: #000;
    border: 1px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .animate-fade-in-down,
  .animate-fade-in-left,
  .animate-fade-in-right,
  .animate-pulse-ring,
  .animate-bounce-gentle {
    animation: none;
  }

  .chatbot-widget,
  .suggestion-button {
    transition: none;
  }
}
