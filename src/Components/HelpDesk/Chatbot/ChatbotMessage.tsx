import React from 'react';
import { IoChatbubbleEllipsesOutline, IoPersonOutline } from 'react-icons/io5';
import { ChatbotMessageProps } from './types/chatbot.types';

const ChatbotMessage: React.FC<ChatbotMessageProps> = ({ message, isLast }) => {
  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (message.type === 'user') {
    return (
      <div className={`flex justify-end mb-4 ${isLast ? 'animate-fade-in-right' : ''}`}>
        <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
          <div className="bg-GTI-BLUE-default text-white px-4 py-2 rounded-2xl rounded-br-md shadow-sm">
            <p className="text-sm">{message.content}</p>
          </div>
          <div className="flex-shrink-0 w-8 h-8 bg-GTI-BLUE-default rounded-full flex items-center justify-center">
            <IoPersonOutline className="w-4 h-4 text-white" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex justify-start mb-4 ${isLast ? 'animate-fade-in-left' : ''}`}>
      <div className="flex items-end space-x-2 max-w-xs lg:max-w-md">
        <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
          <IoChatbubbleEllipsesOutline className="w-4 h-4 text-GTI-BLUE-default" />
        </div>
        <div className="bg-gray-100 text-gray-800 px-4 py-2 rounded-2xl rounded-bl-md shadow-sm">
          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
          <div className="text-xs text-gray-500 mt-1">
            {formatTime(message.timestamp)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatbotMessage;
