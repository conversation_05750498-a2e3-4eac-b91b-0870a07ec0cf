import React, { useState, useEffect } from 'react';
import { ChatMessage, ChatbotState } from './types/chatbot.types';
import { ChatbotService } from './services/chatbotService';
import ChatbotWidget from './ChatbotWidget';
import ChatbotWindow from './ChatbotWindow';

interface HelpdeskChatbotProps {
  position?: 'bottom-right' | 'bottom-left';
}

const HelpdeskChatbot: React.FC<HelpdeskChatbotProps> = ({
  position = 'bottom-right'
}) => {
  const [chatbotState, setChatbotState] = useState<ChatbotState>({
    messages: [],
    isTyping: false,
    currentContext: '',
    userSession: '',
    isOpen: false
  });

  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isMinimized, setIsMinimized] = useState(false);
  const [chatbotService] = useState(() => ChatbotService.getInstance());

  // Initialize session on component mount
  useEffect(() => {
    const sessionId = chatbotService.generateSessionId();
    setChatbotState(prev => ({
      ...prev,
      userSession: sessionId
    }));
  }, [chatbotService]);

  // Load initial greeting when chat opens
  useEffect(() => {
    if (chatbotState.isOpen && chatbotState.messages.length === 0 && chatbotState.userSession) {
      const { botMessage, suggestions: initialSuggestions } = chatbotService.getInitialGreeting(chatbotState.userSession);
      
      setChatbotState(prev => ({
        ...prev,
        messages: [botMessage]
      }));
      setSuggestions(initialSuggestions);
    }
  }, [chatbotState.isOpen, chatbotState.messages.length, chatbotState.userSession, chatbotService]);

  const handleToggleChat = () => {
    setChatbotState(prev => ({
      ...prev,
      isOpen: !prev.isOpen
    }));
    setIsMinimized(false);
  };

  const handleCloseChat = () => {
    setChatbotState(prev => ({
      ...prev,
      isOpen: false
    }));
    setIsMinimized(false);
  };

  const handleMinimizeChat = () => {
    setIsMinimized(!isMinimized);
  };

  const handleSendMessage = async (messageContent: string) => {
    if (!messageContent.trim() || chatbotState.isTyping) return;

    // Create user message
    const userMessage = chatbotService.createUserMessage(messageContent);
    
    // Add user message to chat
    setChatbotState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      isTyping: true
    }));

    // Clear suggestions temporarily
    setSuggestions([]);

    try {
      // Simulate typing delay
      await chatbotService.simulateTyping();

      // Process message and get bot response
      const { botMessage, suggestions: newSuggestions } = await chatbotService.processMessage(
        messageContent,
        chatbotState.userSession
      );

      // Add bot message to chat
      setChatbotState(prev => ({
        ...prev,
        messages: [...prev.messages, botMessage],
        isTyping: false
      }));

      // Update suggestions
      setSuggestions(newSuggestions);

    } catch (error) {
      console.error('Error processing message:', error);
      
      // Add error message
      const errorMessage = chatbotService.createUserMessage(
        'Sorry, I encountered an error. Please try again or contact our support team directly.'
      );
      
      setChatbotState(prev => ({
        ...prev,
        messages: [...prev.messages, errorMessage],
        isTyping: false
      }));
    }
  };

  const handleSuggestionClick = async (suggestionText: string) => {
    if (chatbotState.isTyping) return;

    // Create user message for the suggestion
    const userMessage = chatbotService.createUserMessage(suggestionText);
    
    // Add user message to chat
    setChatbotState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      isTyping: true
    }));

    // Clear suggestions temporarily
    setSuggestions([]);

    try {
      // Simulate typing delay
      await chatbotService.simulateTyping();

      // Process quick reply
      const { botMessage, suggestions: newSuggestions } = await chatbotService.processQuickReply(
        suggestionText,
        chatbotState.userSession
      );

      // Add bot message to chat
      setChatbotState(prev => ({
        ...prev,
        messages: [...prev.messages, botMessage],
        isTyping: false
      }));

      // Update suggestions
      setSuggestions(newSuggestions);

    } catch (error) {
      console.error('Error processing suggestion:', error);
      
      setChatbotState(prev => ({
        ...prev,
        isTyping: false
      }));
    }
  };

  return (
    <>
      {/* Chatbot Widget */}
      <ChatbotWidget
        onToggle={handleToggleChat}
        isOpen={chatbotState.isOpen}
        position={position}
      />

      {/* Chatbot Window */}
      {chatbotState.isOpen && (
        <ChatbotWindow
          messages={chatbotState.messages}
          onSendMessage={handleSendMessage}
          onClose={handleCloseChat}
          isTyping={chatbotState.isTyping}
          suggestions={suggestions}
          onSuggestionClick={handleSuggestionClick}
          onMinimize={handleMinimizeChat}
          isMinimized={isMinimized}
        />
      )}
    </>
  );
};

export default HelpdeskChatbot;
