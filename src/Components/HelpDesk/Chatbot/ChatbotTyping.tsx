import React from 'react';
import { IoChatbubbleEllipsesOutline } from 'react-icons/io5';

const ChatbotTyping: React.FC = () => {
  return (
    <div className="flex justify-start mb-4 animate-fade-in-left">
      <div className="flex items-end space-x-2">
        <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
          <IoChatbubbleEllipsesOutline className="w-4 h-4 text-GTI-BLUE-default" />
        </div>
        <div className="bg-gray-100 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatbotTyping;
