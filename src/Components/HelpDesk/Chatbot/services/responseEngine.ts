import { 
  FAQ_DATABASE, 
  QUICK_REPLIES, 
  KEYWORD_PATTERNS, 
  DEFAULT_RESPONSES 
} from './knowledgeBase';
import { ChatbotResponse, FAQItem, KeywordPattern } from '../types/chatbot.types';

export class ResponseEngine {
  private static instance: ResponseEngine;
  
  public static getInstance(): ResponseEngine {
    if (!ResponseEngine.instance) {
      ResponseEngine.instance = new ResponseEngine();
    }
    return ResponseEngine.instance;
  }

  /**
   * Main method to process user input and generate response
   */
  public processMessage(userInput: string, context: string[] = []): ChatbotResponse {
    const normalizedInput = this.normalizeInput(userInput);
    
    // Check for greeting patterns first
    const greetingResponse = this.checkGreetingPatterns(normalizedInput);
    if (greetingResponse) return greetingResponse;

    // Check for goodbye patterns
    const goodbyeResponse = this.checkGoodbyePatterns(normalizedInput);
    if (goodbyeResponse) return goodbyeResponse;

    // Check exact FAQ matches
    const faqResponse = this.findFAQMatch(normalizedInput);
    if (faqResponse) return faqResponse;

    // Check keyword patterns
    const keywordResponse = this.findKeywordMatch(normalizedInput);
    if (keywordResponse) return keywordResponse;

    // Check partial matches
    const partialResponse = this.findPartialMatch(normalizedInput);
    if (partialResponse) return partialResponse;

    // Return fallback response
    return this.getFallbackResponse();
  }

  /**
   * Normalize user input for better matching
   */
  private normalizeInput(input: string): string {
    return input
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, ' ') // Remove punctuation
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  /**
   * Check for greeting patterns
   */
  private checkGreetingPatterns(input: string): ChatbotResponse | null {
    const greetingPattern = KEYWORD_PATTERNS.find(pattern => 
      pattern.category === 'greeting' && 
      pattern.keywords.some(keyword => input.includes(keyword))
    );

    if (greetingPattern) {
      return {
        message: greetingPattern.response,
        suggestions: greetingPattern.suggestions || [],
        category: 'greeting',
        confidence: 1.0
      };
    }
    return null;
  }

  /**
   * Check for goodbye patterns
   */
  private checkGoodbyePatterns(input: string): ChatbotResponse | null {
    const goodbyePattern = KEYWORD_PATTERNS.find(pattern => 
      pattern.category === 'goodbye' && 
      pattern.keywords.some(keyword => input.includes(keyword))
    );

    if (goodbyePattern) {
      return {
        message: goodbyePattern.response,
        category: 'goodbye',
        confidence: 1.0
      };
    }
    return null;
  }

  /**
   * Find exact FAQ matches
   */
  private findFAQMatch(input: string): ChatbotResponse | null {
    // Sort FAQs by priority
    const sortedFAQs = FAQ_DATABASE.sort((a, b) => b.priority - a.priority);

    for (const faq of sortedFAQs) {
      // Check if input matches any keywords
      const keywordMatch = faq.keywords.some(keyword => 
        input.includes(keyword.toLowerCase())
      );

      // Check if input is similar to the question
      const questionMatch = this.calculateSimilarity(input, faq.question.toLowerCase()) > 0.6;

      if (keywordMatch || questionMatch) {
        return {
          message: faq.answer,
          suggestions: faq.followUpSuggestions || [],
          category: faq.category,
          confidence: keywordMatch ? 0.9 : 0.7
        };
      }
    }
    return null;
  }

  /**
   * Find keyword pattern matches
   */
  private findKeywordMatch(input: string): ChatbotResponse | null {
    const sortedPatterns = KEYWORD_PATTERNS
      .filter(pattern => pattern.category !== 'greeting' && pattern.category !== 'goodbye')
      .sort((a, b) => b.priority - a.priority);

    for (const pattern of sortedPatterns) {
      const matchCount = pattern.keywords.filter(keyword => 
        input.includes(keyword.toLowerCase())
      ).length;

      if (matchCount > 0) {
        const confidence = Math.min(matchCount / pattern.keywords.length, 1.0);
        return {
          message: pattern.response,
          suggestions: pattern.suggestions || [],
          category: pattern.category,
          confidence: confidence * 0.8
        };
      }
    }
    return null;
  }

  /**
   * Find partial matches using fuzzy matching
   */
  private findPartialMatch(input: string): ChatbotResponse | null {
    let bestMatch: FAQItem | null = null;
    let bestScore = 0;

    for (const faq of FAQ_DATABASE) {
      // Check similarity with question
      const questionScore = this.calculateSimilarity(input, faq.question.toLowerCase());
      
      // Check keyword overlap
      const keywordScore = this.calculateKeywordOverlap(input, faq.keywords);
      
      const totalScore = (questionScore * 0.6) + (keywordScore * 0.4);

      if (totalScore > bestScore && totalScore > 0.3) {
        bestScore = totalScore;
        bestMatch = faq;
      }
    }

    if (bestMatch && bestScore > 0.3) {
      return {
        message: bestMatch.answer,
        suggestions: bestMatch.followUpSuggestions || [],
        category: bestMatch.category,
        confidence: bestScore
      };
    }
    return null;
  }

  /**
   * Calculate similarity between two strings
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const words1 = str1.split(' ');
    const words2 = str2.split(' ');
    
    const commonWords = words1.filter(word => 
      words2.includes(word) && word.length > 2
    );
    
    const totalWords = Math.max(words1.length, words2.length);
    return commonWords.length / totalWords;
  }

  /**
   * Calculate keyword overlap score
   */
  private calculateKeywordOverlap(input: string, keywords: string[]): number {
    const inputWords = input.split(' ');
    let matchCount = 0;

    for (const keyword of keywords) {
      const keywordWords = keyword.toLowerCase().split(' ');
      const hasMatch = keywordWords.some(word => 
        inputWords.some(inputWord => 
          inputWord.includes(word) || word.includes(inputWord)
        )
      );
      if (hasMatch) matchCount++;
    }

    return matchCount / keywords.length;
  }

  /**
   * Get fallback response with suggestions
   */
  private getFallbackResponse(): ChatbotResponse {
    const suggestions = [
      'What is GTI?',
      'How can I get started?',
      'What services do you offer?',
      'How do I contact support?',
      'Tell me about pricing'
    ];

    return {
      message: DEFAULT_RESPONSES.fallback,
      suggestions,
      category: 'fallback',
      confidence: 0.1
    };
  }

  /**
   * Get quick reply response
   */
  public getQuickReplyResponse(quickReplyText: string): ChatbotResponse {
    const quickReply = QUICK_REPLIES.find(qr => qr.text === quickReplyText);
    
    if (quickReply) {
      return {
        message: quickReply.response,
        suggestions: quickReply.followUp || [],
        category: quickReply.category,
        confidence: 1.0
      };
    }

    return this.getFallbackResponse();
  }

  /**
   * Get initial suggestions for new conversations
   */
  public getInitialSuggestions(): string[] {
    return [
      'What is GTI?',
      'How can I get started?',
      'What services do you offer?',
      'How do I contact support?',
      'Tell me about pricing',
      'Schedule a consultation'
    ];
  }

  /**
   * Check if message requires escalation to human support
   */
  public requiresEscalation(input: string): boolean {
    const escalationKeywords = [
      'speak to human',
      'talk to person',
      'human agent',
      'representative',
      'escalate',
      'manager',
      'complaint',
      'urgent',
      'emergency'
    ];

    const normalizedInput = this.normalizeInput(input);
    return escalationKeywords.some(keyword => normalizedInput.includes(keyword));
  }

  /**
   * Get escalation response
   */
  public getEscalationResponse(): ChatbotResponse {
    return {
      message: DEFAULT_RESPONSES.escalation,
      category: 'escalation',
      confidence: 1.0,
      requiresEscalation: true
    };
  }
}
