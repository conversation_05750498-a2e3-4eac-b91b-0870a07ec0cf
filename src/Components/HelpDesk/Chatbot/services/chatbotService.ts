import {
  ChatMessage,
  ChatbotResponse,
  UserSession,
  ChatbotConfig,
} from "../types/chatbot.types";
import { ResponseEngine } from "./responseEngine";

export class ChatbotService {
  private static instance: ChatbotService;
  private responseEngine: ResponseEngine;
  private sessions: Map<string, UserSession> = new Map();
  private config: ChatbotConfig;

  private constructor() {
    this.responseEngine = ResponseEngine.getInstance();
    this.config = {
      typingDelay: 1500,
      maxSuggestions: 4,
      sessionTimeout: 30 * 60 * 1000, // 30 minutes
      defaultResponses: {
        greeting: "Hello! Welcome to GTI Support. How can I help you today?",
        fallback: "I am not sure I understand. Could you please rephrase that?",
        goodbye: "Thank you for contacting GTI Support! Have a great day!",
        escalation: "Let me connect you with a human support agent.",
      },
    };
  }

  public static getInstance(): ChatbotService {
    if (!ChatbotService.instance) {
      ChatbotService.instance = new ChatbotService();
    }
    return ChatbotService.instance;
  }

  /**
   * Process user message and generate bot response
   */
  public async processMessage(
    userMessage: string,
    sessionId: string
  ): Promise<{ botMessage: ChatMessage; suggestions: string[] }> {
    // Get or create session
    const session = this.getOrCreateSession(sessionId);

    // Update session activity
    this.updateSessionActivity(session, userMessage);

    // Check for escalation request
    if (this.responseEngine.requiresEscalation(userMessage)) {
      const escalationResponse = this.responseEngine.getEscalationResponse();
      session.escalated = true;

      const botMessage = this.createBotMessage(escalationResponse.message);
      return {
        botMessage,
        suggestions: [],
      };
    }

    // Process message through response engine
    const response = this.responseEngine.processMessage(
      userMessage,
      session.context
    );

    // Update session context
    this.updateSessionContext(
      session,
      userMessage,
      response.category || "general"
    );

    // Create bot message
    const botMessage = this.createBotMessage(response.message);

    // Limit suggestions
    const suggestions = (response.suggestions || []).slice(
      0,
      this.config.maxSuggestions
    );

    return {
      botMessage,
      suggestions,
    };
  }

  /**
   * Process quick reply selection
   */
  public async processQuickReply(
    quickReplyText: string,
    sessionId: string
  ): Promise<{ botMessage: ChatMessage; suggestions: string[] }> {
    const session = this.getOrCreateSession(sessionId);
    this.updateSessionActivity(session, quickReplyText);

    const response = this.responseEngine.getQuickReplyResponse(quickReplyText);

    this.updateSessionContext(
      session,
      quickReplyText,
      response.category || "general"
    );

    const botMessage = this.createBotMessage(response.message);
    const suggestions = (response.suggestions || []).slice(
      0,
      this.config.maxSuggestions
    );

    return {
      botMessage,
      suggestions,
    };
  }

  /**
   * Get initial greeting and suggestions for new conversation
   */
  public getInitialGreeting(sessionId: string): {
    botMessage: ChatMessage;
    suggestions: string[];
  } {
    const session = this.getOrCreateSession(sessionId);

    const greetingMessage = this.config.defaultResponses.greeting;
    const botMessage = this.createBotMessage(greetingMessage);
    const suggestions = this.responseEngine.getInitialSuggestions();

    return {
      botMessage,
      suggestions: suggestions.slice(0, this.config.maxSuggestions),
    };
  }

  /**
   * Create user message object
   */
  public createUserMessage(content: string): ChatMessage {
    return {
      id: this.generateMessageId(),
      type: "user",
      content,
      timestamp: new Date(),
    };
  }

  /**
   * Create bot message object
   */
  private createBotMessage(content: string): ChatMessage {
    return {
      id: this.generateMessageId(),
      type: "bot",
      content,
      timestamp: new Date(),
    };
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get or create user session
   */
  private getOrCreateSession(sessionId: string): UserSession {
    let session = this.sessions.get(sessionId);

    if (!session) {
      session = {
        id: sessionId,
        startTime: new Date(),
        lastActivity: new Date(),
        messageCount: 0,
        context: [],
        escalated: false,
      };
      this.sessions.set(sessionId, session);
    }

    return session;
  }

  /**
   * Update session activity
   */
  private updateSessionActivity(session: UserSession, message: string): void {
    session.lastActivity = new Date();
    session.messageCount++;

    // Clean up old sessions periodically
    this.cleanupOldSessions();
  }

  /**
   * Update session context for better responses
   */
  private updateSessionContext(
    session: UserSession,
    userMessage: string,
    category: string
  ): void {
    session.context.push(category);

    // Keep only last 5 context items
    if (session.context.length > 5) {
      session.context = session.context.slice(-5);
    }
  }

  /**
   * Clean up old sessions
   */
  private cleanupOldSessions(): void {
    const now = new Date().getTime();

    this.sessions.forEach((session, sessionId) => {
      const timeSinceLastActivity = now - session.lastActivity.getTime();

      if (timeSinceLastActivity > this.config.sessionTimeout) {
        this.sessions.delete(sessionId);
      }
    });
  }

  /**
   * Generate unique session ID
   */
  public generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get session info
   */
  public getSessionInfo(sessionId: string): UserSession | null {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Check if session is escalated
   */
  public isSessionEscalated(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    return session?.escalated || false;
  }

  /**
   * Get typing delay configuration
   */
  public getTypingDelay(): number {
    return this.config.typingDelay;
  }

  /**
   * Simulate typing delay
   */
  public async simulateTyping(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(resolve, this.config.typingDelay);
    });
  }

  /**
   * Get chatbot statistics
   */
  public getStatistics(): {
    activeSessions: number;
    totalSessions: number;
    averageMessagesPerSession: number;
    escalatedSessions: number;
  } {
    const activeSessions = this.sessions.size;
    const totalSessions = this.sessions.size;

    let totalMessages = 0;
    let escalatedSessions = 0;

    this.sessions.forEach((session) => {
      totalMessages += session.messageCount;
      if (session.escalated) {
        escalatedSessions++;
      }
    });

    return {
      activeSessions,
      totalSessions,
      averageMessagesPerSession:
        totalSessions > 0 ? totalMessages / totalSessions : 0,
      escalatedSessions,
    };
  }

  /**
   * Reset session (for testing purposes)
   */
  public resetSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }

  /**
   * Clear all sessions (for testing purposes)
   */
  public clearAllSessions(): void {
    this.sessions.clear();
  }
}
