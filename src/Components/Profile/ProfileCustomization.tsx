import React, { useState, useEffect } from "react";
import {
  <PERSON>a<PERSON>alet<PERSON>,
  FaColumns,
  FaEye,
  FaSave,
  FaUndo,
  FaMoon,
  FaSun,
  FaDesktop,
  FaFont,
  FaImage,
  FaBell,
  FaLock,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaChartBar,
} from "react-icons/fa";

interface Theme {
  id: string;
  name: string;
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  preview: string;
}

interface LayoutOption {
  id: string;
  name: string;
  description: string;
  preview: string;
  columns: number;
}

interface CustomizationSettings {
  theme: string;
  layout: string;
  colorMode: "light" | "dark" | "auto";
  showProfileViews: boolean;
  showActivityStatus: boolean;
  compactMode: boolean;
  animationsEnabled: boolean;
  // Typography & Display
  fontSize?: string;
  density?: string;
  // Accessibility
  highContrast?: boolean;
  reducedMotion?: boolean;
  largeClickTargets?: boolean;
  screenReaderOptimized?: boolean;
  // Privacy & Notifications
  emailNotifications?: boolean;
  pushNotifications?: boolean;
  profileVisibility?: boolean;
  activityTracking?: boolean;
  dataAnalytics?: boolean;
  // Advanced Settings
  backgroundStyle?: string;
  sidebarPosition?: string;
  [key: string]: any;
}

const ProfileCustomization: React.FC = () => {
  const [settings, setSettings] = useState<CustomizationSettings>({
    theme: "default",
    layout: "standard",
    colorMode: "light",
    showProfileViews: true,
    showActivityStatus: true,
    compactMode: false,
    animationsEnabled: true,
    // Typography & Display
    fontSize: "medium",
    density: "comfortable",
    // Accessibility
    highContrast: false,
    reducedMotion: false,
    largeClickTargets: false,
    screenReaderOptimized: false,
    // Privacy & Notifications
    emailNotifications: true,
    pushNotifications: true,
    profileVisibility: true,
    activityTracking: true,
    dataAnalytics: true,
    // Advanced Settings
    backgroundStyle: "none",
    sidebarPosition: "left",
  });

  const [previewMode, setPreviewMode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const themes: Theme[] = [
    {
      id: "default",
      name: "GTI Blue",
      primary: "#3B82F6",
      secondary: "#1E40AF",
      accent: "#60A5FA",
      background: "#F8FAFC",
      surface: "#FFFFFF",
      text: "#1F2937",
      preview: "linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%)",
    },
    {
      id: "emerald",
      name: "Emerald",
      primary: "#10B981",
      secondary: "#047857",
      accent: "#34D399",
      background: "#F0FDF4",
      surface: "#FFFFFF",
      text: "#1F2937",
      preview: "linear-gradient(135deg, #10B981 0%, #047857 100%)",
    },
    {
      id: "purple",
      name: "Purple",
      primary: "#8B5CF6",
      secondary: "#7C3AED",
      accent: "#A78BFA",
      background: "#FAF5FF",
      surface: "#FFFFFF",
      text: "#1F2937",
      preview: "linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)",
    },
    {
      id: "rose",
      name: "Rose",
      primary: "#F43F5E",
      secondary: "#E11D48",
      accent: "#FB7185",
      background: "#FFF1F2",
      surface: "#FFFFFF",
      text: "#1F2937",
      preview: "linear-gradient(135deg, #F43F5E 0%, #E11D48 100%)",
    },
    {
      id: "dark",
      name: "Dark Mode",
      primary: "#3B82F6",
      secondary: "#1E40AF",
      accent: "#60A5FA",
      background: "#0F172A",
      surface: "#1E293B",
      text: "#F1F5F9",
      preview: "linear-gradient(135deg, #0F172A 0%, #1E293B 100%)",
    },
    {
      id: "purple",
      name: "Royal Purple",
      primary: "#7C3AED",
      secondary: "#5B21B6",
      accent: "#A78BFA",
      background: "#FAF5FF",
      surface: "#FFFFFF",
      text: "#1F2937",
      preview: "linear-gradient(135deg, #7C3AED 0%, #5B21B6 100%)",
    },
    {
      id: "rose",
      name: "Rose Gold",
      primary: "#E11D48",
      secondary: "#BE185D",
      accent: "#FB7185",
      background: "#FFF1F2",
      surface: "#FFFFFF",
      text: "#1F2937",
      preview: "linear-gradient(135deg, #E11D48 0%, #BE185D 100%)",
    },
    {
      id: "orange",
      name: "Sunset Orange",
      primary: "#EA580C",
      secondary: "#C2410C",
      accent: "#FB923C",
      background: "#FFF7ED",
      surface: "#FFFFFF",
      text: "#1F2937",
      preview: "linear-gradient(135deg, #EA580C 0%, #C2410C 100%)",
    },
    {
      id: "teal",
      name: "Ocean Teal",
      primary: "#0D9488",
      secondary: "#0F766E",
      accent: "#2DD4BF",
      background: "#F0FDFA",
      surface: "#FFFFFF",
      text: "#1F2937",
      preview: "linear-gradient(135deg, #0D9488 0%, #0F766E 100%)",
    },
    {
      id: "slate",
      name: "Professional Slate",
      primary: "#475569",
      secondary: "#334155",
      accent: "#94A3B8",
      background: "#F8FAFC",
      surface: "#FFFFFF",
      text: "#1F2937",
      preview: "linear-gradient(135deg, #475569 0%, #334155 100%)",
    },
  ];

  const layouts: LayoutOption[] = [
    {
      id: "standard",
      name: "Standard",
      description: "Classic layout with sidebar navigation",
      preview: "grid-cols-4",
      columns: 4,
    },
    {
      id: "wide",
      name: "Wide",
      description: "Full-width layout for maximum content space",
      preview: "grid-cols-1",
      columns: 1,
    },
    {
      id: "compact",
      name: "Compact",
      description: "Condensed layout for smaller screens",
      preview: "grid-cols-3",
      columns: 3,
    },
    {
      id: "dashboard",
      name: "Dashboard",
      description: "Card-based dashboard layout",
      preview: "grid-cols-6",
      columns: 6,
    },
  ];

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem("profileCustomization");
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }
  }, []);

  // Track changes
  useEffect(() => {
    const savedSettings = localStorage.getItem("profileCustomization");
    const currentSettings = JSON.stringify(settings);
    setHasChanges(savedSettings !== currentSettings);
  }, [settings]);

  const handleSettingChange = (
    key: keyof CustomizationSettings,
    value: any
  ) => {
    setSettings((prev) => ({ ...prev, [key]: value }));
  };

  const saveSettings = () => {
    localStorage.setItem("profileCustomization", JSON.stringify(settings));
    setHasChanges(false);

    // Apply theme to document
    const selectedTheme = themes.find((t) => t.id === settings.theme);
    if (selectedTheme) {
      document.documentElement.style.setProperty(
        "--primary-color",
        selectedTheme.primary
      );
      document.documentElement.style.setProperty(
        "--secondary-color",
        selectedTheme.secondary
      );
      document.documentElement.style.setProperty(
        "--accent-color",
        selectedTheme.accent
      );
      document.documentElement.style.setProperty(
        "--background-color",
        selectedTheme.background
      );
      document.documentElement.style.setProperty(
        "--surface-color",
        selectedTheme.surface
      );
      document.documentElement.style.setProperty(
        "--text-color",
        selectedTheme.text
      );
    }
  };

  const resetSettings = () => {
    const defaultSettings: CustomizationSettings = {
      theme: "default",
      layout: "standard",
      colorMode: "light",
      showProfileViews: true,
      showActivityStatus: true,
      compactMode: false,
      animationsEnabled: true,
    };
    setSettings(defaultSettings);
  };

  const selectedTheme = themes.find((t) => t.id === settings.theme);
  const selectedLayout = layouts.find((l) => l.id === settings.layout);

  return (
    <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <FaPalette className="w-5 h-5 text-GTI-BLUE-default" />
          <h2 className="text-xl font-semibold text-gray-900">
            Profile Customization
          </h2>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
              previewMode
                ? "bg-GTI-BLUE-default text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            <FaEye className="w-4 h-4" />
            {previewMode ? "Exit Preview" : "Preview"}
          </button>
        </div>
      </div>

      {/* Theme Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
          <FaPalette className="w-4 h-4" />
          Color Theme
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {themes.map((theme) => (
            <button
              key={theme.id}
              onClick={() => handleSettingChange("theme", theme.id)}
              className={`relative p-4 rounded-xl border-2 transition-all duration-200 ${
                settings.theme === theme.id
                  ? "border-GTI-BLUE-default shadow-lg"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div
                className="w-full h-16 rounded-lg mb-3"
                style={{ background: theme.preview }}
              ></div>
              <p className="text-sm font-medium text-gray-900">{theme.name}</p>
              {settings.theme === theme.id && (
                <div className="absolute top-2 right-2 w-5 h-5 bg-GTI-BLUE-default rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Layout Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
          <FaColumns className="w-4 h-4" />
          Layout Style
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {layouts.map((layout) => (
            <button
              key={layout.id}
              onClick={() => handleSettingChange("layout", layout.id)}
              className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                settings.layout === layout.id
                  ? "border-GTI-BLUE-default bg-GTI-BLUE-default/5"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{layout.name}</h4>
                {settings.layout === layout.id && (
                  <div className="w-5 h-5 bg-GTI-BLUE-default rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-600">{layout.description}</p>
              <div className={`mt-3 grid ${layout.preview} gap-1 h-8`}>
                {Array.from({ length: layout.columns }, (_, i) => (
                  <div key={i} className="bg-gray-200 rounded"></div>
                ))}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Display Preferences */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">
          Display Preferences
        </h3>
        <div className="space-y-4">
          {/* Color Mode */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">
                Color Mode
              </label>
              <p className="text-sm text-gray-600">
                Choose your preferred color scheme
              </p>
            </div>
            <div className="flex bg-gray-100 rounded-lg p-1">
              {[
                { value: "light", icon: FaSun, label: "Light" },
                { value: "dark", icon: FaMoon, label: "Dark" },
                { value: "auto", icon: FaDesktop, label: "Auto" },
              ].map(({ value, icon: Icon, label }) => (
                <button
                  key={value}
                  onClick={() => handleSettingChange("colorMode", value)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    settings.colorMode === value
                      ? "bg-white text-GTI-BLUE-default shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {label}
                </button>
              ))}
            </div>
          </div>

          {/* Toggle Options */}
          {[
            {
              key: "showProfileViews",
              label: "Show Profile Views",
              description: "Display who viewed your profile",
            },
            {
              key: "showActivityStatus",
              label: "Show Activity Status",
              description: "Display your online/offline status",
            },
            {
              key: "compactMode",
              label: "Compact Mode",
              description: "Reduce spacing for more content",
            },
            {
              key: "animationsEnabled",
              label: "Enable Animations",
              description: "Show smooth transitions and effects",
            },
          ].map(({ key, label, description }) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900">
                  {label}
                </label>
                <p className="text-sm text-gray-600">{description}</p>
              </div>
              <button
                onClick={() =>
                  handleSettingChange(
                    key as keyof CustomizationSettings,
                    !settings[key as keyof CustomizationSettings]
                  )
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                  settings[key as keyof CustomizationSettings]
                    ? "bg-GTI-BLUE-default"
                    : "bg-gray-200"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                    settings[key as keyof CustomizationSettings]
                      ? "translate-x-6"
                      : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Typography Settings */}
      <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4">
          <FaFont className="w-4 h-4" />
          Typography & Display
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Font Size
            </label>
            <select
              value={settings.fontSize || "medium"}
              onChange={(e) => handleSettingChange("fontSize", e.target.value)}
              className="w-full border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default"
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
              <option value="extra-large">Extra Large</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Content Density
            </label>
            <select
              value={settings.density || "comfortable"}
              onChange={(e) => handleSettingChange("density", e.target.value)}
              className="w-full border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default"
            >
              <option value="compact">Compact</option>
              <option value="comfortable">Comfortable</option>
              <option value="spacious">Spacious</option>
            </select>
          </div>
        </div>
      </div>

      {/* Accessibility Settings */}
      <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4">
          <FaEye className="w-4 h-4" />
          Accessibility
        </h3>
        <div className="space-y-4">
          {[
            {
              key: "highContrast",
              label: "High Contrast Mode",
              description: "Increase contrast for better visibility",
            },
            {
              key: "reducedMotion",
              label: "Reduce Motion",
              description: "Minimize animations and transitions",
            },
            {
              key: "largeClickTargets",
              label: "Large Click Targets",
              description: "Increase button and link sizes",
            },
            {
              key: "screenReaderOptimized",
              label: "Screen Reader Optimized",
              description: "Enhanced screen reader support",
            },
          ].map(({ key, label, description }) => (
            <div key={key} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-900">{label}</div>
                <div className="text-xs text-gray-500">{description}</div>
              </div>
              <button
                onClick={() =>
                  handleSettingChange(
                    key as keyof CustomizationSettings,
                    !settings[key as keyof CustomizationSettings]
                  )
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                  settings[key as keyof CustomizationSettings]
                    ? "bg-GTI-BLUE-default"
                    : "bg-gray-200"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                    settings[key as keyof CustomizationSettings]
                      ? "translate-x-6"
                      : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Privacy & Notifications */}
      <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4">
          <FaBell className="w-4 h-4" />
          Privacy & Notifications
        </h3>
        <div className="space-y-4">
          {[
            {
              key: "emailNotifications",
              label: "Email Notifications",
              description: "Receive updates via email",
            },
            {
              key: "pushNotifications",
              label: "Push Notifications",
              description: "Browser push notifications",
            },
            {
              key: "profileVisibility",
              label: "Public Profile",
              description: "Make profile visible to others",
            },
            {
              key: "activityTracking",
              label: "Activity Tracking",
              description: "Track profile views and interactions",
            },
            {
              key: "dataAnalytics",
              label: "Analytics",
              description: "Allow usage analytics collection",
            },
          ].map(({ key, label, description }) => (
            <div key={key} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-900">{label}</div>
                <div className="text-xs text-gray-500">{description}</div>
              </div>
              <button
                onClick={() =>
                  handleSettingChange(
                    key as keyof CustomizationSettings,
                    !settings[key as keyof CustomizationSettings]
                  )
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                  settings[key as keyof CustomizationSettings]
                    ? "bg-GTI-BLUE-default"
                    : "bg-gray-200"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                    settings[key as keyof CustomizationSettings]
                      ? "translate-x-6"
                      : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Advanced Settings */}
      <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
        <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2 mb-4">
          <FaCog className="w-4 h-4" />
          Advanced Settings
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Profile Background
            </label>
            <div className="grid grid-cols-3 gap-3">
              {["none", "gradient", "pattern"].map((bg) => (
                <button
                  key={bg}
                  onClick={() => handleSettingChange("backgroundStyle", bg)}
                  className={`p-3 rounded-lg border-2 transition-colors duration-200 ${
                    settings.backgroundStyle === bg
                      ? "border-GTI-BLUE-default bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="text-sm font-medium capitalize">{bg}</div>
                </button>
              ))}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sidebar Position
            </label>
            <div className="grid grid-cols-2 gap-3">
              {["left", "right"].map((position) => (
                <button
                  key={position}
                  onClick={() =>
                    handleSettingChange("sidebarPosition", position)
                  }
                  className={`p-3 rounded-lg border-2 transition-colors duration-200 ${
                    settings.sidebarPosition === position
                      ? "border-GTI-BLUE-default bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="text-sm font-medium capitalize">
                    {position}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Preview Section */}
      {previewMode && selectedTheme && (
        <div className="border-t border-gray-100 pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Live Preview
          </h3>
          <div
            className="p-6 rounded-xl border-2 border-dashed border-gray-200"
            style={{
              backgroundColor: selectedTheme.background,
              color: selectedTheme.text,
            }}
          >
            <div
              className="p-4 rounded-lg mb-4"
              style={{ backgroundColor: selectedTheme.surface }}
            >
              <h4
                className="font-semibold mb-2"
                style={{ color: selectedTheme.primary }}
              >
                Sample Profile Card
              </h4>
              <p className="text-sm opacity-75">
                This is how your profile will look with the selected theme and
                layout.
              </p>
              <button
                className="mt-3 px-4 py-2 rounded-lg text-white text-sm font-medium"
                style={{ backgroundColor: selectedTheme.primary }}
              >
                Sample Button
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-100">
        <button
          onClick={resetSettings}
          className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200"
        >
          <FaUndo className="w-4 h-4" />
          Reset to Default
        </button>

        <div className="flex gap-3">
          <button
            onClick={saveSettings}
            disabled={!hasChanges}
            className={`flex items-center gap-2 px-6 py-2 rounded-lg font-medium transition-colors duration-200 ${
              hasChanges
                ? "bg-GTI-BLUE-default text-white hover:bg-blue-600"
                : "bg-gray-100 text-gray-400 cursor-not-allowed"
            }`}
          >
            <FaSave className="w-4 h-4" />
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfileCustomization;
