import React, { useState } from 'react';
import { Fa<PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaGithub, FaGlobe, FaPlus, FaEdit, FaTrash, FaSave, FaTimes } from 'react-icons/fa';

interface SocialLink {
  id: string;
  platform: string;
  url: string;
  icon: React.ReactNode;
  color: string;
}

interface SocialMediaLinksProps {
  links: SocialLink[];
  onLinksUpdate: (links: SocialLink[]) => void;
  editable?: boolean;
}

const SocialMediaLinks: React.FC<SocialMediaLinksProps> = ({
  links,
  onLinksUpdate,
  editable = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingLinks, setEditingLinks] = useState<SocialLink[]>(links);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newLink, setNewLink] = useState({ platform: '', url: '' });

  const platformOptions = [
    { value: 'linkedin', label: 'LinkedIn', icon: <FaLinkedin />, color: '#0077B5' },
    { value: 'twitter', label: 'Twitter', icon: <FaTwitter />, color: '#1DA1F2' },
    { value: 'github', label: 'GitHub', icon: <FaGithub />, color: '#333' },
    { value: 'website', label: 'Website', icon: <FaGlobe />, color: '#6B7280' },
  ];

  const handleEdit = () => {
    setIsEditing(true);
    setEditingLinks([...links]);
  };

  const handleSave = () => {
    onLinksUpdate(editingLinks);
    setIsEditing(false);
    setShowAddForm(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditingLinks([...links]);
    setShowAddForm(false);
    setNewLink({ platform: '', url: '' });
  };

  const handleLinkUpdate = (id: string, url: string) => {
    setEditingLinks(prev => 
      prev.map(link => link.id === id ? { ...link, url } : link)
    );
  };

  const handleLinkDelete = (id: string) => {
    setEditingLinks(prev => prev.filter(link => link.id !== id));
  };

  const handleAddLink = () => {
    if (!newLink.platform || !newLink.url) return;

    const platform = platformOptions.find(p => p.value === newLink.platform);
    if (!platform) return;

    const newSocialLink: SocialLink = {
      id: Date.now().toString(),
      platform: platform.label,
      url: newLink.url,
      icon: platform.icon,
      color: platform.color
    };

    setEditingLinks(prev => [...prev, newSocialLink]);
    setNewLink({ platform: '', url: '' });
    setShowAddForm(false);
  };

  const validateUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  return (
    <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Social Links</h3>
        {editable && !isEditing && (
          <button
            onClick={handleEdit}
            className="text-GTI-BLUE-default hover:text-blue-600 transition-colors duration-200"
          >
            <FaEdit className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Display Mode */}
      {!isEditing && (
        <div className="space-y-3">
          {links.length === 0 ? (
            <p className="text-gray-500 text-sm">No social links added yet</p>
          ) : (
            links.map((link) => (
              <a
                key={link.id}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group"
              >
                <div 
                  className="w-10 h-10 rounded-full flex items-center justify-center text-white"
                  style={{ backgroundColor: link.color }}
                >
                  {link.icon}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{link.platform}</p>
                  <p className="text-sm text-gray-500 group-hover:text-GTI-BLUE-default transition-colors duration-200">
                    {link.url}
                  </p>
                </div>
              </a>
            ))
          )}
        </div>
      )}

      {/* Edit Mode */}
      {isEditing && (
        <div className="space-y-4">
          {editingLinks.map((link) => (
            <div key={link.id} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
              <div 
                className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                style={{ backgroundColor: link.color }}
              >
                {link.icon}
              </div>
              <div className="flex-1">
                <input
                  type="url"
                  value={link.url}
                  onChange={(e) => handleLinkUpdate(link.id, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-colors duration-200"
                  placeholder={`Enter ${link.platform} URL`}
                />
              </div>
              <button
                onClick={() => handleLinkDelete(link.id)}
                className="text-red-500 hover:text-red-600 transition-colors duration-200"
              >
                <FaTrash className="w-4 h-4" />
              </button>
            </div>
          ))}

          {/* Add New Link Form */}
          {showAddForm && (
            <div className="p-4 border-2 border-dashed border-gray-200 rounded-lg">
              <div className="space-y-3">
                <select
                  value={newLink.platform}
                  onChange={(e) => setNewLink(prev => ({ ...prev, platform: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default"
                >
                  <option value="">Select Platform</option>
                  {platformOptions.map((platform) => (
                    <option key={platform.value} value={platform.value}>
                      {platform.label}
                    </option>
                  ))}
                </select>
                <input
                  type="url"
                  value={newLink.url}
                  onChange={(e) => setNewLink(prev => ({ ...prev, url: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default"
                  placeholder="Enter URL"
                />
                <div className="flex gap-2">
                  <button
                    onClick={handleAddLink}
                    disabled={!newLink.platform || !newLink.url || !validateUrl(newLink.url)}
                    className="px-4 py-2 bg-GTI-BLUE-default text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                  >
                    Add Link
                  </button>
                  <button
                    onClick={() => {
                      setShowAddForm(false);
                      setNewLink({ platform: '', url: '' });
                    }}
                    className="px-4 py-2 border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Add Button */}
          {!showAddForm && (
            <button
              onClick={() => setShowAddForm(true)}
              className="w-full p-3 border-2 border-dashed border-gray-200 rounded-lg text-gray-500 hover:text-GTI-BLUE-default hover:border-GTI-BLUE-default transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <FaPlus className="w-4 h-4" />
              Add Social Link
            </button>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t border-gray-100">
            <button
              onClick={handleSave}
              className="flex-1 px-4 py-2 bg-GTI-BLUE-default text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <FaSave className="w-4 h-4" />
              Save Changes
            </button>
            <button
              onClick={handleCancel}
              className="flex-1 px-4 py-2 border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <FaTimes className="w-4 h-4" />
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SocialMediaLinks;
