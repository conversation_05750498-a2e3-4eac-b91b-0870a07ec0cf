import React, { useState, useEffect } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import ReactCountryFlag from "react-country-flag";
import { FaConnectdevelop } from "react-icons/fa";
import { useSelector, useDispatch } from "react-redux";
import { Dispatch } from "redux";
import { resetUser } from "../../store/actioncreators/actionCreators";
import ProfilePhotoUpload from "./ProfilePhotoUpload";

import { NotificationProvider, useNotifications } from "./NotificationSystem";
import { PerformanceMonitor } from "./PerformanceOptimizations";
import "./style.css";

const UserProfile = () => {
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  const dispatch: Dispatch<any> = useDispatch();
  const { addNotification } = useNotifications();

  let emp_name = currentUser.user.name;
  let org_name = currentUser?.company[0]?.name ?? "";
  let org_country = currentUser.user.country; //later fetch from user backend
  let navigate = useNavigate();
  const location = useLocation();

  const [profileImage, setProfileImage] = useState(
    currentUser?.company.length
      ? currentUser?.company[0].logo || currentUser?.profileImage
      : currentUser?.profileImage
  );
  const [activeTab, setActiveTab] = useState("personal");

  // Sync activeTab with current route
  useEffect(() => {
    const pathname = location.pathname;
    if (pathname.includes("/personal")) {
      setActiveTab("personal");
    } else if (pathname.includes("/company")) {
      setActiveTab("company");
    } else if (pathname.includes("/password")) {
      setActiveTab("security");
    }
  }, [location.pathname]);

  const resetUserDetails = () => dispatch(resetUser(currentUser));

  const handleLogOut = () => {
    resetUserDetails();
    localStorage.removeItem("GTI_data");
    navigate(process.env.REACT_APP_HOME!);
  };

  const handleProfileImageUpdate = (newImageUrl: string) => {
    setProfileImage(newImageUrl);
    addNotification({
      type: "success",
      title: "Profile Photo Updated",
      message: "Your profile photo has been successfully updated.",
      duration: 3000,
    });
  };

  return (
    <PerformanceMonitor>
      <div
        className="min-h-screen bg-gray-50"
        style={{ width: "100vw", maxWidth: "none" }}
      >
        <div className="flex h-screen">
          {/* Left Sidebar Navigation */}
          <div className="w-80 bg-white shadow-lg border-r border-gray-200 flex flex-col">
            {/* Profile Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <ProfilePhotoUpload
                    currentImage={profileImage}
                    onImageUpdate={handleProfileImageUpdate}
                    userName={emp_name}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h2 className="text-lg font-semibold text-gray-900 truncate">
                      {emp_name}
                    </h2>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    </div>
                  </div>
                  {org_name && (
                    <p className="text-sm text-gray-600 flex items-center gap-1 mb-2">
                      <svg
                        className="w-3 h-3"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-6a1 1 0 00-1-1H9a1 1 0 00-1 1v6a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 8a1 1 0 011-1h4a1 1 0 011 1v4H7v-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span className="truncate">{org_name}</span>
                    </p>
                  )}
                  <div className="flex items-center gap-3 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <ReactCountryFlag
                        countryCode={org_country ? org_country : "IN"}
                        svg
                        style={{ width: "12px", height: "12px" }}
                      />
                      <span>{org_country ? org_country : "India"}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Menu */}
            <div className="flex-1 p-6">
              <nav className="space-y-2">
                <button
                  className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all duration-200 ${
                    activeTab === "personal"
                      ? "bg-blue-50 text-blue-700 border-l-4 border-blue-600"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                  onClick={() => {
                    setActiveTab("personal");
                    navigate("personal");
                  }}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      activeTab === "personal" ? "bg-blue-100" : "bg-gray-100"
                    }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                  <span className="font-medium">Personal Details</span>
                </button>

                {currentUser.admin === 1 &&
                  currentUser?.company &&
                  currentUser?.userType !== "GENERAL_SUBSCRIBER" && (
                    <button
                      className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all duration-200 ${
                        activeTab === "company"
                          ? "bg-blue-50 text-blue-700 border-l-4 border-blue-600"
                          : "text-gray-700 hover:bg-gray-50"
                      }`}
                      onClick={() => {
                        setActiveTab("company");
                        navigate("company");
                      }}
                    >
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          activeTab === "company"
                            ? "bg-green-100"
                            : "bg-gray-100"
                        }`}
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                          />
                        </svg>
                      </div>
                      <span className="font-medium">Company Details</span>
                    </button>
                  )}

                <button
                  className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all duration-200 ${
                    activeTab === "security"
                      ? "bg-blue-50 text-blue-700 border-l-4 border-blue-600"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                  onClick={() => {
                    setActiveTab("security");
                    navigate("password");
                  }}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      activeTab === "security" ? "bg-red-100" : "bg-gray-100"
                    }`}
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                  </div>
                  <span className="font-medium">Security</span>
                </button>
              </nav>

              {/* Quick Navigation Cards */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-4">
                  Quick Access
                </h3>
                <div className="space-y-3">
                  <button
                    onClick={() => navigate("/your-technology")}
                    className="w-full flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg hover:from-blue-100 hover:to-blue-200 transition-all duration-200 group"
                  >
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
                      <svg
                        className="w-4 h-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900">My Products</p>
                      <p className="text-xs text-gray-600">
                        Manage your technologies
                      </p>
                    </div>
                  </button>

                  <button
                    onClick={() => navigate("/your-opportunities")}
                    className="w-full flex items-center gap-3 p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg hover:from-green-100 hover:to-green-200 transition-all duration-200 group"
                  >
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white">
                      <svg
                        className="w-4 h-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900">
                        My Opportunities
                      </p>
                      <p className="text-xs text-gray-600">
                        View your opportunities
                      </p>
                    </div>
                  </button>

                  <button
                    onClick={() => navigate("/connections")}
                    className="w-full flex items-center gap-3 p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg hover:from-purple-100 hover:to-purple-200 transition-all duration-200 group"
                  >
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white">
                      <FaConnectdevelop className="w-4 h-4" />
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900">
                        My Connections
                      </p>
                      <p className="text-xs text-gray-600">
                        Manage your network
                      </p>
                    </div>
                  </button>
                </div>
              </div>
            </div>
            {/* Logout Button */}
            <div className="p-6 border-t border-gray-200">
              <button
                className="w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all duration-200 text-red-600 hover:bg-red-50"
                onClick={handleLogOut}
              >
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75"
                    />
                  </svg>
                </div>
                <span className="font-medium">Log Out</span>
              </button>
            </div>
          </div>

          {/* Right Content Area */}
          <div className="flex-1 overflow-auto">
            <div className="p-8">
              <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
                <Outlet />
              </div>
            </div>
          </div>
        </div>
      </div>
    </PerformanceMonitor>
  );
};

// Wrapper component with NotificationProvider
const UserProfileWithNotifications = () => {
  return (
    <NotificationProvider>
      <UserProfile />
    </NotificationProvider>
  );
};

export default UserProfileWithNotifications;
