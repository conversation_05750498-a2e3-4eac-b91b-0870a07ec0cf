import React, { useState, useEffect } from "react";
import {
  FaArrowUp,
  FaArrowDown,
  FaEye,
  FaUsers,
  FaCog,
  FaBriefcase,
} from "react-icons/fa";

// Simple Chart Components (without external dependencies)
interface ChartData {
  label: string;
  value: number;
  color?: string;
}

interface LineChartProps {
  data: ChartData[];
  height?: number;
  color?: string;
}

const LineChart: React.FC<LineChartProps> = ({
  data,
  height = 200,
  color = "#3B82F6",
}) => {
  const maxValue = Math.max(...data.map((d) => d.value));
  const minValue = Math.min(...data.map((d) => d.value));
  const range = maxValue - minValue || 1;

  const points = data
    .map((item, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = 100 - ((item.value - minValue) / range) * 100;
      return `${x},${y}`;
    })
    .join(" ");

  return (
    <div className="relative" style={{ height }}>
      <svg
        className="w-full h-full"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
      >
        {/* Grid lines */}
        <defs>
          <pattern
            id="grid"
            width="20"
            height="20"
            patternUnits="userSpaceOnUse"
          >
            <path
              d="M 20 0 L 0 0 0 20"
              fill="none"
              stroke="#f3f4f6"
              strokeWidth="0.5"
            />
          </pattern>
        </defs>
        <rect width="100" height="100" fill="url(#grid)" />

        {/* Area under curve */}
        <path
          d={`M 0,100 L ${points} L 100,100 Z`}
          fill={`${color}20`}
          className="transition-all duration-500"
        />

        {/* Line */}
        <polyline
          points={points}
          fill="none"
          stroke={color}
          strokeWidth="2"
          className="transition-all duration-500"
        />

        {/* Data points */}
        {data.map((item, index) => {
          const x = (index / (data.length - 1)) * 100;
          const y = 100 - ((item.value - minValue) / range) * 100;
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r="2"
              fill={color}
              className="hover:r-3 transition-all duration-200 cursor-pointer"
            >
              <title>{`${item.label}: ${item.value}`}</title>
            </circle>
          );
        })}
      </svg>
    </div>
  );
};

interface BarChartProps {
  data: ChartData[];
  height?: number;
}

const BarChart: React.FC<BarChartProps> = ({ data, height = 200 }) => {
  const maxValue = Math.max(...data.map((d) => d.value));

  return (
    <div className="flex items-end justify-between gap-2" style={{ height }}>
      {data.map((item, index) => {
        const barHeight = (item.value / maxValue) * 100;
        return (
          <div key={index} className="flex flex-col items-center flex-1 group">
            <div className="relative flex-1 flex items-end w-full">
              <div
                className="w-full rounded-t-lg transition-all duration-500 hover:opacity-80 cursor-pointer"
                style={{
                  height: `${barHeight}%`,
                  backgroundColor: item.color || "#3B82F6",
                }}
                title={`${item.label}: ${item.value}`}
              >
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                  {item.value}
                </div>
              </div>
            </div>
            <span className="text-xs text-gray-600 mt-2 text-center">
              {item.label}
            </span>
          </div>
        );
      })}
    </div>
  );
};

interface DonutChartProps {
  data: ChartData[];
  size?: number;
}

const DonutChart: React.FC<DonutChartProps> = ({ data, size = 120 }) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let cumulativePercentage = 0;

  const radius = 45;
  const strokeWidth = 10;
  const normalizedRadius = radius - strokeWidth * 2;
  const circumference = normalizedRadius * 2 * Math.PI;

  return (
    <div className="relative" style={{ width: size, height: size }}>
      <svg width={size} height={size} className="transform -rotate-90">
        <circle
          cx={size / 2}
          cy={size / 2}
          r={normalizedRadius}
          stroke="#f3f4f6"
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        {data.map((item, index) => {
          const percentage = (item.value / total) * 100;
          const strokeDasharray = `${
            (percentage / 100) * circumference
          } ${circumference}`;
          const strokeDashoffset = -(
            (cumulativePercentage / 100) *
            circumference
          );
          cumulativePercentage += percentage;

          return (
            <circle
              key={index}
              cx={size / 2}
              cy={size / 2}
              r={normalizedRadius}
              stroke={item.color || "#3B82F6"}
              strokeWidth={strokeWidth}
              fill="transparent"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className="transition-all duration-500 hover:stroke-opacity-80 cursor-pointer"
            >
              <title>{`${item.label}: ${item.value} (${percentage.toFixed(
                1
              )}%)`}</title>
            </circle>
          );
        })}
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg font-bold text-gray-900">{total}</div>
          <div className="text-xs text-gray-500">Total</div>
        </div>
      </div>
    </div>
  );
};

// Activity Timeline Component
interface ActivityItem {
  id: string;
  type: "profile" | "connection" | "technology" | "opportunity";
  title: string;
  description: string;
  timestamp: Date;
  icon: React.ReactNode;
  color: string;
}

interface ActivityTimelineProps {
  activities: ActivityItem[];
}

const ActivityTimeline: React.FC<ActivityTimelineProps> = ({ activities }) => {
  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  return (
    <div className="space-y-4">
      {activities.map((activity, index) => (
        <div key={activity.id} className="flex items-start gap-4 group">
          <div className="relative">
            <div
              className="w-10 h-10 rounded-full flex items-center justify-center text-white shadow-lg"
              style={{ backgroundColor: activity.color }}
            >
              {activity.icon}
            </div>
            {index < activities.length - 1 && (
              <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-0.5 h-8 bg-gray-200"></div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900 group-hover:text-GTI-BLUE-default transition-colors duration-200">
                {activity.title}
              </h4>
              <span className="text-xs text-gray-500">
                {getTimeAgo(activity.timestamp)}
              </span>
            </div>
            <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
          </div>
        </div>
      ))}
    </div>
  );
};

// Main Analytics Dashboard Component
const AnalyticsDashboard: React.FC = () => {
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d">("30d");

  // Sample data
  const profileViewsData = [
    { label: "Week 1", value: 45 },
    { label: "Week 2", value: 52 },
    { label: "Week 3", value: 38 },
    { label: "Week 4", value: 61 },
    { label: "Week 5", value: 55 },
    { label: "Week 6", value: 67 },
  ];

  const skillsData = [
    { label: "React", value: 85, color: "#61DAFB" },
    { label: "TypeScript", value: 78, color: "#3178C6" },
    { label: "Node.js", value: 72, color: "#339933" },
    { label: "Python", value: 65, color: "#3776AB" },
  ];

  const engagementData = [
    { label: "Profile Views", value: 234, color: "#3B82F6" },
    { label: "Connections", value: 89, color: "#10B981" },
    { label: "Messages", value: 45, color: "#F59E0B" },
    { label: "Endorsements", value: 23, color: "#8B5CF6" },
  ];

  const recentActivities: ActivityItem[] = [
    {
      id: "1",
      type: "profile",
      title: "Profile Updated",
      description: "Added new skills and experience",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      icon: <FaEye className="w-4 h-4" />,
      color: "#3B82F6",
    },
    {
      id: "2",
      type: "connection",
      title: "New Connection",
      description: "Connected with Sarah Johnson",
      timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
      icon: <FaUsers className="w-4 h-4" />,
      color: "#10B981",
    },
    {
      id: "3",
      type: "technology",
      title: "Technology Added",
      description: "Added React Native to your skills",
      timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      icon: <FaCog className="w-4 h-4" />,
      color: "#8B5CF6",
    },
    {
      id: "4",
      type: "opportunity",
      title: "Opportunity Applied",
      description: "Applied for Senior Developer position",
      timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      icon: <FaBriefcase className="w-4 h-4" />,
      color: "#F59E0B",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          Analytics Overview
        </h2>
        <div className="flex bg-gray-100 rounded-lg p-1">
          {(["7d", "30d", "90d"] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors duration-200 ${
                timeRange === range
                  ? "bg-white text-GTI-BLUE-default shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              {range === "7d"
                ? "7 Days"
                : range === "30d"
                ? "30 Days"
                : "90 Days"}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Profile Views Chart */}
        <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Profile Views
            </h3>
            <div className="flex items-center gap-2 text-green-600">
              <FaArrowUp className="w-4 h-4" />
              <span className="text-sm font-medium">+12%</span>
            </div>
          </div>
          <LineChart data={profileViewsData} />
        </div>

        {/* Skills Rating */}
        <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Top Skills
          </h3>
          <BarChart data={skillsData} />
        </div>

        {/* Engagement Breakdown */}
        <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Engagement
          </h3>
          <div className="flex items-center justify-center">
            <DonutChart data={engagementData} />
          </div>
          <div className="grid grid-cols-2 gap-4 mt-4">
            {engagementData.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm text-gray-600">{item.label}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Activity Timeline */}
        <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Recent Activity
          </h3>
          <ActivityTimeline activities={recentActivities} />
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
