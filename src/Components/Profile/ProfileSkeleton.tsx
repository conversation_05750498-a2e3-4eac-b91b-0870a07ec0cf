import React from 'react';

const ProfileSkeleton: React.FC = () => {
  return (
    <div className="profile-main-1">
      <div className="profile-container">
        {/* Header Skeleton */}
        <div className="profile-header-card">
          <div className="profile-cover">
            <div className="profile-skeleton h-full w-full"></div>
          </div>
          
          <div className="profile-info-section">
            <div className="profile-avatar-container">
              <div className="profile-skeleton-avatar"></div>
            </div>
            
            <div className="profile-info-content">
              <div className="profile-user-info">
                <div className="profile-skeleton-text w-48 h-8 mb-3"></div>
                <div className="profile-skeleton-text w-32 h-5 mb-3"></div>
                <div className="profile-skeleton-text w-24 h-4"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Skeleton */}
        <div className="profile-navigation-card">
          <div className="profile-skeleton-text w-32 h-6 mb-6"></div>
          <div className="space-y-2">
            {[1, 2, 3, 4, 5].map((item) => (
              <div key={item} className="flex items-center gap-3 p-4">
                <div className="profile-skeleton w-5 h-5 rounded"></div>
                <div className="profile-skeleton-text w-24 h-4"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Content Skeleton */}
        <div className="profile-content-card">
          <div className="profile-form-container">
            <div className="profile-skeleton-text w-48 h-6 mb-6"></div>
            
            <div className="profile-form-grid">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="profile-form-group">
                  <div className="profile-skeleton-text w-24 h-4 mb-2"></div>
                  <div className="profile-skeleton h-12 w-full rounded-xl"></div>
                </div>
              ))}
            </div>
            
            <div className="profile-button-group mt-6">
              <div className="profile-skeleton w-32 h-12 rounded-xl"></div>
              <div className="profile-skeleton w-24 h-12 rounded-xl"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSkeleton;
