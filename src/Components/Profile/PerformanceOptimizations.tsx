import React, {
  useState,
  useEffect,
  useRef,
  Suspense,
  lazy,
  useCallback,
  useMemo,
} from "react";

// Lazy Loading Component
interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  threshold?: number;
}

export const LazyLoad: React.FC<LazyComponentProps> = ({
  children,
  fallback = <div className="animate-pulse bg-gray-200 rounded-lg h-32"></div>,
  threshold = 0.1,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold, hasLoaded]);

  return <div ref={ref}>{isVisible ? children : fallback}</div>;
};

// Optimized Image Component
interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  placeholder?: string;
  quality?: number;
  loading?: "lazy" | "eager";
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = "",
  width,
  height,
  placeholder = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+",
  quality = 75,
  loading = "lazy",
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder);

  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setCurrentSrc(src);
      setIsLoaded(true);
    };
    img.onerror = () => {
      setHasError(true);
    };
    img.src = src;
  }, [src]);

  if (hasError) {
    return (
      <div
        className={`bg-gray-200 flex items-center justify-center ${className}`}
      >
        <span className="text-gray-500 text-sm">Failed to load image</span>
      </div>
    );
  }

  return (
    <div className="relative overflow-hidden">
      <img
        src={currentSrc}
        alt={alt}
        className={`transition-opacity duration-300 ${
          isLoaded ? "opacity-100" : "opacity-0"
        } ${className}`}
        width={width}
        height={height}
        loading={loading}
        onLoad={() => setIsLoaded(true)}
      />
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>
      )}
    </div>
  );
};

// Skeleton Loading Components
export const SkeletonCard: React.FC<{ className?: string }> = ({
  className = "",
}) => (
  <div className={`animate-pulse ${className}`}>
    <div className="bg-gray-200 rounded-lg h-48 mb-4"></div>
    <div className="space-y-2">
      <div className="bg-gray-200 rounded h-4 w-3/4"></div>
      <div className="bg-gray-200 rounded h-4 w-1/2"></div>
    </div>
  </div>
);

export const SkeletonProfile: React.FC = () => (
  <div className="animate-pulse">
    <div className="bg-gray-200 rounded-2xl h-64 mb-6"></div>
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      {Array.from({ length: 4 }, (_, i) => (
        <div key={i} className="bg-gray-200 rounded-xl h-24"></div>
      ))}
    </div>
    <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
      <div className="bg-gray-200 rounded-xl h-96"></div>
      <div className="lg:col-span-4 bg-gray-200 rounded-xl h-96"></div>
    </div>
  </div>
);

export const SkeletonList: React.FC<{ items?: number }> = ({ items = 5 }) => (
  <div className="space-y-4">
    {Array.from({ length: items }, (_, i) => (
      <div key={i} className="animate-pulse flex items-center space-x-4">
        <div className="bg-gray-200 rounded-full h-12 w-12"></div>
        <div className="flex-1 space-y-2">
          <div className="bg-gray-200 rounded h-4 w-3/4"></div>
          <div className="bg-gray-200 rounded h-4 w-1/2"></div>
        </div>
      </div>
    ))}
  </div>
);

// Virtual Scrolling Component for Large Lists
interface VirtualScrollProps {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
  overscan?: number;
}

export const VirtualScroll: React.FC<VirtualScrollProps> = ({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  };

  return (
    <div
      ref={scrollElementRef}
      style={{ height: containerHeight, overflow: "auto" }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * itemHeight, position: "relative" }}>
        <div
          style={{
            transform: `translateY(${startIndex * itemHeight}px)`,
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map((item, index) =>
            renderItem(item, startIndex + index)
          )}
        </div>
      </div>
    </div>
  );
};

// Debounced Search Hook
export const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Memoized Component Wrapper
export const MemoizedComponent = React.memo(
  ({
    children,
    ...props
  }: {
    children: React.ReactNode;
    [key: string]: any;
  }) => {
    return <div {...props}>{children}</div>;
  },
  (prevProps, nextProps) => {
    // Custom comparison logic
    return JSON.stringify(prevProps) === JSON.stringify(nextProps);
  }
);

// Performance Monitor Component
export const PerformanceMonitor: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === "measure") {
            console.log(`Performance: ${entry.name} took ${entry.duration}ms`);
          }
        });
      });

      observer.observe({ entryTypes: ["measure"] });

      return () => observer.disconnect();
    }
  }, []);

  return <>{children}</>;
};

// Code Splitting Utilities
export const createLazyComponent = (
  importFunc: () => Promise<{ default: React.ComponentType<any> }>
) => {
  return lazy(importFunc);
};

// Error Boundary for Lazy Components
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class LazyErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  ErrorBoundaryState
> {
  constructor(props: {
    children: React.ReactNode;
    fallback?: React.ReactNode;
  }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Lazy component error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <h3 className="text-red-800 font-medium">Something went wrong</h3>
            <p className="text-red-600 text-sm mt-1">
              Failed to load component. Please try refreshing the page.
            </p>
          </div>
        )
      );
    }

    return this.props.children;
  }
}

// Memoized Component Wrapper
interface MemoWrapperProps {
  children: React.ReactNode;
  dependencies?: any[];
}

export const MemoWrapper: React.FC<MemoWrapperProps> = React.memo(
  ({ children }) => <>{children}</>,
  (prevProps, nextProps) => {
    if (prevProps.dependencies && nextProps.dependencies) {
      return prevProps.dependencies.every(
        (dep, index) => dep === nextProps.dependencies![index]
      );
    }
    return false;
  }
);

// Resource Preloader
export const useResourcePreloader = (resources: string[]) => {
  const [loadedResources, setLoadedResources] = useState<Set<string>>(
    new Set()
  );

  useEffect(() => {
    const preloadResource = (url: string) => {
      return new Promise((resolve, reject) => {
        if (url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
          const img = new Image();
          img.onload = () => resolve(url);
          img.onerror = reject;
          img.src = url;
        } else if (url.match(/\.(js|css)$/i)) {
          const link = document.createElement("link");
          link.rel = url.endsWith(".css") ? "stylesheet" : "preload";
          if (url.endsWith(".js")) {
            link.as = "script";
          }
          link.href = url;
          link.onload = () => resolve(url);
          link.onerror = reject;
          document.head.appendChild(link);
        } else {
          fetch(url)
            .then(() => resolve(url))
            .catch(reject);
        }
      });
    };

    const loadResources = async () => {
      for (const resource of resources) {
        try {
          await preloadResource(resource);
          setLoadedResources((prev) => {
            const newSet = new Set(prev);
            newSet.add(resource);
            return newSet;
          });
        } catch (error) {
          console.warn(`Failed to preload resource: ${resource}`, error);
        }
      }
    };

    loadResources();
  }, [resources]);

  return loadedResources;
};

// Bundle Size Analyzer (Development only)
export const BundleAnalyzer: React.FC = () => {
  const [bundleInfo, setBundleInfo] = useState<any>(null);

  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      // Simulate bundle analysis
      const analyzeBundle = () => {
        const scripts = Array.from(document.querySelectorAll("script[src]"));
        const styles = Array.from(
          document.querySelectorAll('link[rel="stylesheet"]')
        );

        const info = {
          scripts: scripts.length,
          styles: styles.length,
          totalSize: "Estimated 1.2MB",
          loadTime: performance.now(),
          recommendations: [
            "Consider code splitting for large components",
            "Optimize images with WebP format",
            "Enable gzip compression",
            "Use tree shaking for unused code",
          ],
        };

        setBundleInfo(info);
      };

      setTimeout(analyzeBundle, 1000);
    }
  }, []);

  if (process.env.NODE_ENV !== "development" || !bundleInfo) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
      <h4 className="font-bold mb-2">Bundle Analysis</h4>
      <div className="text-sm space-y-1">
        <p>Scripts: {bundleInfo.scripts}</p>
        <p>Styles: {bundleInfo.styles}</p>
        <p>Size: {bundleInfo.totalSize}</p>
        <p>Load Time: {Math.round(bundleInfo.loadTime)}ms</p>
      </div>
      <details className="mt-2">
        <summary className="cursor-pointer text-xs">Recommendations</summary>
        <ul className="text-xs mt-1 space-y-1">
          {bundleInfo.recommendations.map((rec: string, index: number) => (
            <li key={index}>• {rec}</li>
          ))}
        </ul>
      </details>
    </div>
  );
};

// Performance Metrics Hook
export const usePerformanceMetrics = () => {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    componentCount: 0,
    memoryUsage: 0,
    fps: 0,
  });

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        setMetrics((prev) => ({
          ...prev,
          fps: Math.round((frameCount * 1000) / (currentTime - lastTime)),
        }));
        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(measureFPS);
    };

    const measureMemory = () => {
      if ("memory" in performance) {
        const memory = (performance as any).memory;
        setMetrics((prev) => ({
          ...prev,
          memoryUsage: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        }));
      }
    };

    measureFPS();
    measureMemory();

    const interval = setInterval(measureMemory, 5000);

    return () => {
      cancelAnimationFrame(animationId);
      clearInterval(interval);
    };
  }, []);

  return metrics;
};
