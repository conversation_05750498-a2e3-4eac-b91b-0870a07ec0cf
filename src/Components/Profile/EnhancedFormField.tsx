import React, { useState, useRef, useEffect } from 'react';
import { FaEye, FaEyeSlash, FaCheck, FaExclamationTriangle } from 'react-icons/fa';

interface EnhancedFormFieldProps {
  label: string;
  type?: 'text' | 'email' | 'password' | 'tel' | 'textarea' | 'select';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  icon?: React.ReactNode;
  validation?: {
    isValid: boolean;
    message: string;
  };
  options?: { value: string; label: string }[];
  rows?: number;
  helpText?: string;
  showCharCount?: boolean;
  maxLength?: number;
}

const EnhancedFormField: React.FC<EnhancedFormFieldProps> = ({
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  disabled = false,
  required = false,
  icon,
  validation,
  options,
  rows = 4,
  helpText,
  showCharCount = false,
  maxLength,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>(null);

  const hasValue = value && value.length > 0;
  const shouldFloatLabel = isFocused || hasValue;

  useEffect(() => {
    if (isFocused && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isFocused]);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  const getValidationIcon = () => {
    if (!validation) return null;
    
    if (validation.isValid && hasValue) {
      return <FaCheck className="w-4 h-4 text-green-500" />;
    } else if (!validation.isValid && hasValue) {
      return <FaExclamationTriangle className="w-4 h-4 text-red-500" />;
    }
    return null;
  };

  const getInputClasses = () => {
    let baseClasses = "w-full px-4 py-4 border rounded-xl transition-all duration-200 bg-white focus:outline-none";
    
    if (icon) {
      baseClasses += " pl-12";
    }
    
    if (type === 'password') {
      baseClasses += " pr-12";
    } else if (validation) {
      baseClasses += " pr-10";
    }
    
    if (disabled) {
      baseClasses += " bg-gray-50 text-gray-500 cursor-not-allowed";
    } else if (validation && !validation.isValid && hasValue) {
      baseClasses += " border-red-300 focus:border-red-500 focus:ring-2 focus:ring-red-100";
    } else if (validation && validation.isValid && hasValue) {
      baseClasses += " border-green-300 focus:border-green-500 focus:ring-2 focus:ring-green-100";
    } else if (isFocused) {
      baseClasses += " border-GTI-BLUE-default focus:ring-2 focus:ring-GTI-BLUE-default/20";
    } else {
      baseClasses += " border-gray-200 hover:border-gray-300";
    }
    
    return baseClasses;
  };

  const renderInput = () => {
    const commonProps = {
      ref: inputRef as any,
      value,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => 
        onChange(e.target.value),
      onFocus: handleFocus,
      onBlur: handleBlur,
      disabled,
      className: getInputClasses(),
      maxLength,
      placeholder: shouldFloatLabel ? placeholder : '',
    };

    switch (type) {
      case 'textarea':
        return (
          <textarea
            {...commonProps}
            rows={rows}
            style={{ resize: 'none' }}
          />
        );
      
      case 'select':
        return (
          <select {...commonProps}>
            <option value="">Select an option</option>
            {options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      
      case 'password':
        return (
          <input
            {...commonProps}
            type={showPassword ? 'text' : 'password'}
          />
        );
      
      default:
        return (
          <input
            {...commonProps}
            type={type}
          />
        );
    }
  };

  return (
    <div className="relative">
      {/* Input Container */}
      <div className="relative">
        {/* Icon */}
        {icon && (
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 z-10">
            {icon}
          </div>
        )}

        {/* Input Field */}
        {renderInput()}

        {/* Floating Label */}
        <label
          className={`absolute left-4 transition-all duration-200 pointer-events-none z-10 ${
            icon ? 'left-12' : 'left-4'
          } ${
            shouldFloatLabel
              ? 'top-2 text-xs font-medium text-GTI-BLUE-default'
              : 'top-1/2 transform -translate-y-1/2 text-gray-500'
          }`}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>

        {/* Password Toggle */}
        {type === 'password' && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
          >
            {showPassword ? <FaEyeSlash className="w-4 h-4" /> : <FaEye className="w-4 h-4" />}
          </button>
        )}

        {/* Validation Icon */}
        {type !== 'password' && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            {getValidationIcon()}
          </div>
        )}
      </div>

      {/* Help Text and Validation */}
      <div className="mt-2 flex justify-between items-start">
        <div className="flex-1">
          {validation && !validation.isValid && hasValue && (
            <p className="text-sm text-red-600 flex items-center gap-1">
              <FaExclamationTriangle className="w-3 h-3" />
              {validation.message}
            </p>
          )}
          {validation && validation.isValid && hasValue && (
            <p className="text-sm text-green-600 flex items-center gap-1">
              <FaCheck className="w-3 h-3" />
              Looks good!
            </p>
          )}
          {helpText && !validation && (
            <p className="text-sm text-gray-500">{helpText}</p>
          )}
        </div>
        
        {/* Character Count */}
        {showCharCount && maxLength && (
          <div className="text-xs text-gray-400 ml-2">
            {value.length}/{maxLength}
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedFormField;
