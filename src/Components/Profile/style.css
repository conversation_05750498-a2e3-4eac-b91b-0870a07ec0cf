/* Enhanced Typography */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");

/* Full Width Breakout */
.profile-main-1 {
  @apply min-h-screen py-8;
  width: 100vw;
  max-width: none !important;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw !important;
  margin-right: -50vw !important;
  padding-left: max(2rem, env(safe-area-inset-left));
  padding-right: max(2rem, env(safe-area-inset-right));
  box-sizing: border-box;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
}

.profile-container {
  @apply w-full grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8;
  max-width: none;
  width: 100%;
}

/* Enhanced Card Styling */
.profile-header-card {
  @apply lg:col-span-5 bg-white rounded-2xl overflow-hidden relative w-full;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.profile-header-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.profile-cover {
  @apply h-48 lg:h-64 bg-gradient-to-r from-GTI-BLUE-default via-blue-600 to-indigo-600 relative;
}

.profile-cover::before {
  content: "";
  @apply absolute inset-0 bg-black/10;
}

.profile-cover-pattern {
  @apply absolute inset-0 opacity-20;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.profile-info-section {
  @apply relative px-6 lg:px-8 pb-8;
}

.profile-avatar-container {
  @apply absolute -top-16 left-6 lg:left-8;
}

.profile-avatar {
  @apply w-32 h-32 rounded-2xl border-4 border-white shadow-2xl object-cover bg-white;
}

.profile-info-content {
  @apply pt-20 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4;
}

.profile-user-info {
  @apply flex-1;
}

.profile-user-name {
  @apply text-2xl lg:text-3xl font-bold text-gray-900 mb-2;
  font-family: "Inter", sans-serif;
}

.profile-user-company {
  @apply text-lg text-GTI-BLUE-default font-medium mb-3;
}

.profile-user-location {
  @apply flex items-center gap-2 text-gray-600;
}
/* Enhanced Navigation Sidebar */
.profile-navigation-card {
  @apply bg-white rounded-2xl p-6 h-fit sticky top-8 w-full;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.profile-navigation-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.profile-nav-title {
  @apply text-lg font-semibold text-gray-900 mb-6;
  font-family: "Inter", sans-serif;
}

.profile-nav-list {
  @apply space-y-2;
}

.profile-nav-item {
  @apply flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200 cursor-pointer;
}

.profile-nav-item:hover {
  @apply bg-gray-50 transform scale-[1.02];
}

.profile-nav-item.active {
  @apply bg-GTI-BLUE-default text-white shadow-lg;
}

.profile-nav-item.active:hover {
  @apply bg-blue-600;
}

.profile-nav-icon {
  @apply w-5 h-5 transition-colors duration-200;
}

.profile-nav-text {
  @apply font-medium text-sm;
  font-family: "Inter", sans-serif;
}

.profile-nav-item:not(.active) .profile-nav-text {
  @apply text-gray-700 group-hover:text-gray-900;
}

.profile-nav-item.logout {
  @apply text-red-500 hover:bg-red-50 hover:text-red-600 mt-6 border-t border-gray-100 pt-6;
}

/* Enhanced Content Area */
.profile-content-card {
  @apply lg:col-span-4 bg-white rounded-2xl overflow-hidden w-full;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.profile-content-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Modern Form Styling */
.profile-form-container {
  @apply p-8 w-full;
}

.profile-form-section {
  @apply mb-8;
}

.profile-form-title {
  @apply text-xl font-semibold text-gray-900 mb-6 pb-3 border-b border-gray-100;
  font-family: "Inter", sans-serif;
}

.profile-form-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.profile-form-group {
  @apply space-y-2;
}

.profile-form-group.full-width {
  @apply lg:col-span-2;
}

.profile-form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
  font-family: "Inter", sans-serif;
}

.profile-form-input {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-200 bg-white;
  font-family: "Inter", sans-serif;
}

.profile-form-input:disabled {
  @apply bg-gray-50 text-gray-500 cursor-not-allowed;
}

.profile-form-input:focus {
  @apply outline-none shadow-lg;
}

.profile-form-textarea {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-200 bg-white resize-none;
  font-family: "Inter", sans-serif;
  min-height: 120px;
}

.profile-form-select {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-200 bg-white;
  font-family: "Inter", sans-serif;
}

/* Modern Button Styling */
.profile-button-group {
  @apply flex flex-wrap gap-3 pt-6 border-t border-gray-100;
}

.profile-btn-primary {
  @apply px-6 py-3 bg-GTI-BLUE-default text-white rounded-xl font-medium hover:bg-blue-600 focus:ring-4 focus:ring-GTI-BLUE-default/20 transition-all duration-200 transform hover:scale-105 focus:outline-none;
  font-family: "Inter", sans-serif;
}

.profile-btn-secondary {
  @apply px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-100 transition-all duration-200 transform hover:scale-105 focus:outline-none;
  font-family: "Inter", sans-serif;
}

.profile-btn-danger {
  @apply px-6 py-3 bg-red-500 text-white rounded-xl font-medium hover:bg-red-600 focus:ring-4 focus:ring-red-100 transition-all duration-200 transform hover:scale-105 focus:outline-none;
  font-family: "Inter", sans-serif;
}

/* Input with Icon Styling */
.profile-input-with-icon {
  @apply relative;
}

.profile-input-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5;
}

.profile-input-with-icon .profile-form-input {
  @apply pl-10;
}

/* Password Input Styling */
.profile-password-container {
  @apply relative;
}

.profile-password-toggle {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 cursor-pointer w-5 h-5 transition-colors duration-200;
}

/* Loading States & Animations */
.profile-skeleton {
  @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 rounded-xl;
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.profile-skeleton-text {
  @apply h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 rounded;
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.profile-skeleton-avatar {
  @apply w-32 h-32 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 rounded-2xl;
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Fade In Animation */
.profile-fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide In Animation */
.profile-slide-in {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Scale In Animation */
.profile-scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Advanced Micro-Interactions */
.profile-nav-item {
  position: relative;
  overflow: hidden;
}

.profile-nav-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.profile-nav-item:hover::before {
  left: 100%;
}

/* Button Hover Effects */
.profile-btn-primary {
  position: relative;
  overflow: hidden;
}

.profile-btn-primary::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.profile-btn-primary:hover::before {
  width: 300px;
  height: 300px;
}

/* Statistics Card Animations */
.stats-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card:hover {
  transform: translateY(-8px) scale(1.02);
}

.stats-card:hover .stats-icon {
  transform: rotate(10deg) scale(1.1);
}

.stats-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Pulse Animation for Online Status */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.online-indicator {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Form Field Focus Effects */
.enhanced-form-field {
  position: relative;
}

.enhanced-form-field::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.enhanced-form-field:focus-within::after {
  width: 100%;
}

/* Connection Cards */
.connections-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6;
}

.connection-card {
  @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-GTI-BLUE-default/20;
}

.connection-card:hover {
  @apply transform -translate-y-1;
}

.connection-avatar {
  @apply w-16 h-16 rounded-xl object-cover border-2 border-white shadow-lg;
}

.connection-info {
  @apply p-6;
}

.connection-name {
  @apply text-lg font-semibold text-gray-900 mb-1;
  font-family: "Inter", sans-serif;
}

.connection-company {
  @apply text-sm text-GTI-BLUE-default font-medium mb-3;
}

.connection-status {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
}

.connection-status.connected {
  @apply bg-green-100 text-green-800;
}

.connection-status.pending {
  @apply bg-yellow-100 text-yellow-800;
}

.connection-actions {
  @apply flex gap-2 mt-4;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .profile-container {
    @apply grid-cols-1 gap-6;
  }

  .profile-header-card {
    @apply col-span-1;
  }

  .profile-navigation-card {
    @apply sticky top-4;
  }

  .profile-content-card {
    @apply col-span-1;
  }

  .profile-nav-list {
    @apply flex overflow-x-auto space-y-0 space-x-2 pb-2;
  }

  .profile-nav-item {
    @apply flex-shrink-0 min-w-max;
  }

  .profile-form-grid {
    @apply grid-cols-1;
  }

  .connections-grid {
    @apply grid-cols-1 md:grid-cols-2;
  }
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
  .profile-main-1 {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .profile-header-card {
    margin: 0;
    border-radius: 1rem;
  }

  .profile-cover {
    @apply h-32;
  }

  .profile-avatar {
    @apply w-20 h-20;
  }

  .profile-avatar-container {
    @apply -top-10 left-4;
  }

  .profile-info-content {
    @apply pt-14 px-4;
  }

  .profile-user-name {
    @apply text-lg;
  }

  .profile-form-container {
    @apply p-4;
  }

  .connections-grid {
    @apply grid-cols-1;
  }

  /* Mobile Statistics Cards */
  .lg\\:col-span-5 {
    @apply grid-cols-2 gap-3;
  }

  .stats-card {
    @apply p-4;
  }

  .stats-card p {
    @apply text-xs;
  }

  .stats-card .text-2xl {
    @apply text-xl;
  }

  /* Mobile Navigation */
  .profile-navigation-card {
    @apply p-4 rounded-xl;
  }

  .profile-nav-list {
    @apply space-y-1;
  }

  .profile-nav-item {
    @apply p-3 text-sm;
  }

  /* Mobile Form Fields */
  .profile-form-grid {
    @apply grid-cols-1 gap-4;
  }

  .enhanced-form-field input,
  .enhanced-form-field textarea,
  .enhanced-form-field select {
    @apply py-3 text-base;
  }

  /* Touch-friendly buttons */
  .profile-btn-primary,
  .profile-btn-secondary {
    @apply py-4 px-6 text-base;
    min-height: 44px;
  }

  /* Mobile Header Actions */
  .profile-info-content .mobile-actions {
    @apply flex-row gap-2;
  }

  .profile-info-content button {
    @apply text-xs px-3 py-2;
  }
}

/* Tablet Responsive */
@media (min-width: 769px) and (max-width: 1024px) {
  .profile-container {
    @apply grid-cols-1 gap-6;
  }

  .profile-header-card {
    @apply col-span-1;
  }

  .profile-navigation-card {
    @apply col-span-1;
  }

  .profile-content-card {
    @apply col-span-1;
  }

  .lg\\:col-span-5 {
    @apply grid-cols-3;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .profile-nav-item {
    @apply py-4;
  }

  .stats-card {
    @apply active:scale-95;
  }

  .profile-btn-primary:active,
  .profile-btn-secondary:active {
    @apply scale-95;
  }

  /* Remove hover effects on touch devices */
  .profile-nav-item:hover::before {
    display: none;
  }

  .profile-btn-primary:hover::before {
    display: none;
  }
}

/* Accessibility Enhancements */
.profile-nav-item:focus {
  @apply outline-none ring-2 ring-GTI-BLUE-default ring-offset-2;
}

.profile-btn-primary:focus,
.profile-btn-secondary:focus {
  @apply outline-none ring-2 ring-GTI-BLUE-default ring-offset-2;
}

.enhanced-form-field input:focus,
.enhanced-form-field textarea:focus,
.enhanced-form-field select:focus {
  @apply outline-none ring-2 ring-GTI-BLUE-default ring-offset-2;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .profile-header-card,
  .profile-navigation-card,
  .profile-content-card,
  .stats-card {
    border: 2px solid #000;
  }

  .profile-nav-item.active {
    @apply bg-black text-white;
  }

  .profile-btn-primary {
    @apply bg-black text-white border-2 border-black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .profile-fade-in,
  .profile-slide-in,
  .profile-scale-in {
    animation: none;
  }

  .stats-card,
  .profile-nav-item,
  .profile-btn-primary {
    transition: none;
  }

  .online-indicator {
    animation: none;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible for keyboard navigation */
.profile-nav-item:focus-visible,
.profile-btn-primary:focus-visible,
.profile-btn-secondary:focus-visible {
  @apply outline-none ring-2 ring-GTI-BLUE-default ring-offset-2;
}

/* Skip link for keyboard navigation */
.skip-link {
  @apply absolute -top-10 left-4 bg-GTI-BLUE-default text-white px-4 py-2 rounded-md z-50 transition-all duration-200;
}

.skip-link:focus {
  @apply top-4;
}
