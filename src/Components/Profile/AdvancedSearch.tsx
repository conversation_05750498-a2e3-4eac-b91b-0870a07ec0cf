import React, { useState, useEffect, useMemo } from 'react';
import { Fa<PERSON><PERSON>ch, Fa<PERSON><PERSON><PERSON>, FaSortAmountDown, FaSortAmountUp, FaTimes, FaChevronDown } from 'react-icons/fa';

interface SearchFilters {
  query: string;
  category: string;
  dateRange: string;
  status: string;
  tags: string[];
}

interface SortOption {
  value: string;
  label: string;
  direction: 'asc' | 'desc';
}

interface SearchableItem {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  date: Date;
  tags: string[];
  [key: string]: any;
}

interface AdvancedSearchProps {
  items: SearchableItem[];
  onResultsChange: (results: SearchableItem[]) => void;
  placeholder?: string;
  categories?: { value: string; label: string }[];
  statuses?: { value: string; label: string }[];
  availableTags?: string[];
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  items,
  onResultsChange,
  placeholder = "Search...",
  categories = [],
  statuses = [],
  availableTags = []
}) => {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    category: '',
    dateRange: '',
    status: '',
    tags: []
  });

  const [sortBy, setSortBy] = useState<SortOption>({
    value: 'date',
    label: 'Date',
    direction: 'desc'
  });

  const [showFilters, setShowFilters] = useState(false);
  const [showTagDropdown, setShowTagDropdown] = useState(false);

  const sortOptions: SortOption[] = [
    { value: 'date', label: 'Date', direction: 'desc' },
    { value: 'title', label: 'Title', direction: 'asc' },
    { value: 'category', label: 'Category', direction: 'asc' },
    { value: 'status', label: 'Status', direction: 'asc' }
  ];

  // Filter and sort items
  const filteredAndSortedItems = useMemo(() => {
    let filtered = items.filter(item => {
      // Text search
      if (filters.query) {
        const searchText = filters.query.toLowerCase();
        const searchableText = `${item.title} ${item.description}`.toLowerCase();
        if (!searchableText.includes(searchText)) return false;
      }

      // Category filter
      if (filters.category && item.category !== filters.category) return false;

      // Status filter
      if (filters.status && item.status !== filters.status) return false;

      // Tags filter
      if (filters.tags.length > 0) {
        const hasAllTags = filters.tags.every(tag => item.tags.includes(tag));
        if (!hasAllTags) return false;
      }

      // Date range filter
      if (filters.dateRange) {
        const now = new Date();
        const itemDate = new Date(item.date);
        let cutoffDate: Date;

        switch (filters.dateRange) {
          case '7d':
            cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case '30d':
            cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case '90d':
            cutoffDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          default:
            cutoffDate = new Date(0);
        }

        if (itemDate < cutoffDate) return false;
      }

      return true;
    });

    // Sort items
    filtered.sort((a, b) => {
      let aValue = a[sortBy.value];
      let bValue = b[sortBy.value];

      if (sortBy.value === 'date') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortBy.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [items, filters, sortBy]);

  // Update results when filtered items change
  useEffect(() => {
    onResultsChange(filteredAndSortedItems);
  }, [filteredAndSortedItems, onResultsChange]);

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleTagToggle = (tag: string) => {
    setFilters(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag]
    }));
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      category: '',
      dateRange: '',
      status: '',
      tags: []
    });
  };

  const hasActiveFilters = filters.query || filters.category || filters.dateRange || filters.status || filters.tags.length > 0;

  return (
    <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <FaSearch className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={filters.query}
          onChange={(e) => handleFilterChange('query', e.target.value)}
          className="block w-full pl-10 pr-12 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-colors duration-200"
          placeholder={placeholder}
        />
        {filters.query && (
          <button
            onClick={() => handleFilterChange('query', '')}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
          >
            <FaTimes className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Filter Toggle and Sort */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors duration-200 ${
              showFilters || hasActiveFilters
                ? 'bg-GTI-BLUE-default text-white border-GTI-BLUE-default'
                : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-50'
            }`}
          >
            <FaFilter className="w-4 h-4" />
            <span>Filters</span>
            {hasActiveFilters && (
              <span className="bg-white text-GTI-BLUE-default text-xs px-2 py-0.5 rounded-full">
                {[filters.category, filters.status, filters.dateRange, ...filters.tags].filter(Boolean).length}
              </span>
            )}
          </button>

          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200"
            >
              Clear all
            </button>
          )}
        </div>

        {/* Sort Options */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Sort by:</span>
          <select
            value={`${sortBy.value}-${sortBy.direction}`}
            onChange={(e) => {
              const [value, direction] = e.target.value.split('-');
              const option = sortOptions.find(opt => opt.value === value);
              if (option) {
                setSortBy({ ...option, direction: direction as 'asc' | 'desc' });
              }
            }}
            className="text-sm border border-gray-200 rounded-lg px-3 py-1 focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default"
          >
            {sortOptions.map(option => (
              <React.Fragment key={option.value}>
                <option value={`${option.value}-asc`}>
                  {option.label} (A-Z)
                </option>
                <option value={`${option.value}-desc`}>
                  {option.label} (Z-A)
                </option>
              </React.Fragment>
            ))}
          </select>
          <button
            onClick={() => setSortBy(prev => ({ 
              ...prev, 
              direction: prev.direction === 'asc' ? 'desc' : 'asc' 
            }))}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
          >
            {sortBy.direction === 'asc' ? <FaSortAmountUp /> : <FaSortAmountDown />}
          </button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <div className="border-t border-gray-100 pt-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Category Filter */}
            {categories.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default"
                >
                  <option value="">All Categories</option>
                  {categories.map(cat => (
                    <option key={cat.value} value={cat.value}>{cat.label}</option>
                  ))}
                </select>
              </div>
            )}

            {/* Status Filter */}
            {statuses.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default"
                >
                  <option value="">All Statuses</option>
                  {statuses.map(status => (
                    <option key={status.value} value={status.value}>{status.label}</option>
                  ))}
                </select>
              </div>
            )}

            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
              <select
                value={filters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                className="w-full border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default"
              >
                <option value="">All Time</option>
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
            </div>
          </div>

          {/* Tags Filter */}
          {availableTags.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
              <div className="relative">
                <button
                  onClick={() => setShowTagDropdown(!showTagDropdown)}
                  className="w-full flex items-center justify-between px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  <span className="text-gray-700">
                    {filters.tags.length === 0 ? 'Select tags...' : `${filters.tags.length} selected`}
                  </span>
                  <FaChevronDown className={`w-4 h-4 transition-transform duration-200 ${showTagDropdown ? 'rotate-180' : ''}`} />
                </button>

                {showTagDropdown && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-48 overflow-y-auto">
                    {availableTags.map(tag => (
                      <label key={tag} className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={filters.tags.includes(tag)}
                          onChange={() => handleTagToggle(tag)}
                          className="mr-3 text-GTI-BLUE-default focus:ring-GTI-BLUE-default"
                        />
                        <span className="text-sm text-gray-700">{tag}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>

              {/* Selected Tags */}
              {filters.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {filters.tags.map(tag => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-GTI-BLUE-default/10 text-GTI-BLUE-default text-sm rounded-full"
                    >
                      {tag}
                      <button
                        onClick={() => handleTagToggle(tag)}
                        className="hover:text-GTI-BLUE-default/70"
                      >
                        <FaTimes className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Results Count */}
      <div className="text-sm text-gray-600">
        Showing {filteredAndSortedItems.length} of {items.length} results
      </div>
    </div>
  );
};

export default AdvancedSearch;
