import { useNavigate } from "react-router-dom";
import gbi_home_logo from "../../assests/home/<USER>";

const H_left = ({ handleShow, show }: { handleShow: any; show: boolean }) => {
  const navigate = useNavigate();

  return (
    <div className="modern-logo-container">
      <div className="logo-wrapper" onClick={() => navigate("/")}>
        <img src={gbi_home_logo} alt="GTI® logo" className="modern-logo" />
      </div>
      <button
        className="modern-mobile-toggle"
        onClick={handleShow}
        aria-label="Toggle navigation menu"
      >
        <div
          className={`hamburger-line ${show ? "rotate-45 translate-y-2" : ""}`}
        ></div>
        <div className={`hamburger-line ${show ? "opacity-0" : ""}`}></div>
        <div
          className={`hamburger-line ${
            show ? "-rotate-45 -translate-y-2" : ""
          }`}
        ></div>
      </button>
    </div>
  );
};

export default H_left;
