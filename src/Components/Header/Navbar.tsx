import React, { useState, useEffect } from "react";
import H_left from "./H_left";
import H_mid from "./H_mid";
import H_right from "./H_right";

// interfaces
import LoginInterface from "../Modules/LoginModalInterface/LoginModalInterface";
import { useLocation } from "react-router-dom";
import {
  getAllNotification,
  getNotificationsCount,
} from "../../store/actioncreators/notificationactions";
import { useDispatch } from "react-redux";

interface NavbarMidRightInterface {
  handleLoginModal: () => void;
  handleNotificationModal: () => void;
  show: boolean;
  handleShow: () => void;
}

const NavbarMidRight: React.FC<NavbarMidRightInterface> = ({
  handleLoginModal,
  handleNotificationModal,
  handleShow,
  show,
}) => {
  const location = useLocation();
  return (
    <div
      className={`modern-navbar-content ${
        show ? "mobile-open" : "mobile-closed"
      }`}
    >
      <H_mid handleShow={handleShow} show={show} />
      <H_right
        handleLoginModal={handleLoginModal}
        handleNotificationModal={handleNotificationModal}
        show={show}
        handleShow={handleShow}
      />
    </div>
  );
};

const Navbar: React.FC<LoginInterface> = ({
  handleLoginModal,
  handleNotificationModal,
}) => {
  const location = useLocation();
  const dispatch: any = useDispatch();
  // navbar start
  const [show, setshow] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    dispatch(getAllNotification("", ""));
    dispatch(getNotificationsCount());
  }, [dispatch]);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      setScrolled(isScrolled);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleShow = () => {
    setshow(!show);
  };
  // navbar end

  return (
    <div
      className={`modern-navbar-container ${scrolled ? "navbar-scrolled" : ""}`}
    >
      <H_left handleShow={handleShow} show={show} />
      <NavbarMidRight
        handleLoginModal={handleLoginModal}
        handleNotificationModal={handleNotificationModal}
        handleShow={handleShow}
        show={show}
      />
      {show && (
        <div
          className="modern-navbar-overlay"
          onClick={(e: React.MouseEvent<HTMLElement>) => {
            const targetElement = e.target as HTMLElement;
            if (targetElement?.tagName === "DIV") handleShow();
          }}
        />
      )}
    </div>
  );
};

export default Navbar;
