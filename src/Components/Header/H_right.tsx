import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { IoIosNotifications } from "react-icons/io";
import { useSelector, useDispatch } from "react-redux";

import { Dispatch } from "redux";
import * as ROUTE from "../Constants/routes";
import { notificationCount } from "../../api/user";
import { HOME, NOTIFICATIONS } from "../constants";
import { resetUser } from "../../store/actioncreators/actionCreators";

interface NavbarInterface {
  handleLoginModal: () => void;
  handleNotificationModal: () => void;
  show?: boolean;
  handleShow: () => void;
}

const H_Right: React.FC<NavbarInterface> = ({
  handleLoginModal,
  handleNotificationModal,
  show,
  handleShow,
}) => {
  const [sign_modal, setSignModal] = useState(1);
  const dispatch: Dispatch<any> = useDispatch();
  const location = useLocation();

  const resetUserDetails = React.useCallback(
    () => dispatch(resetUser(currentUser)),
    [dispatch]
  );

  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);

  const [notificationCnt, setNotificationCnt] = useState(0);
  let navigate = useNavigate();
  const handleLogOut = () => {
    resetUserDetails();
    localStorage.removeItem("GTI_data");
    navigate(process.env.REACT_APP_HOME!);
  };

  const handleProfileClick = () => {
    navigate(ROUTE.PROFILE);
  };
  const handleSignSelectModal = (index: any) => {
    if (index === 1) {
      setSignModal(1);
      handleLoginModal();
    } else {
      setSignModal(2);
    }
  };
  const handleView = () => {
    navigate(NOTIFICATIONS);
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      const res = await notificationCount();
      setNotificationCnt(res?.data);
    } catch (err) {}
  };

  return (
    <div className="modern-user-actions">
      {currentUser.admin !== -1 ? (
        <div className="modern-user-menu">
          <button
            className="modern-notification-button"
            onClick={() => handleView()}
            aria-label="View notifications"
          >
            <IoIosNotifications className="modern-notification-icon" />
            {notificationCnt > 0 && (
              <span className="modern-notification-badge">
                {notificationCnt > 99 ? "99+" : notificationCnt}
              </span>
            )}
          </button>
          <button
            className="modern-profile-button"
            onClick={handleProfileClick}
            aria-label="Go to user profile"
          >
            <img
              src={currentUser?.company[0]?.logo || currentUser.user.profileImg}
              className="modern-profile-avatar"
              alt={currentUser.user.name}
            />
            <span className="modern-profile-name">{currentUser.user.name}</span>
          </button>
        </div>
      ) : (
        <div className="flex items-center justify-center">
          <button
            onClick={() => {
              handleSignSelectModal(1);
              handleShow();
            }}
            className="modern-get-started-button"
          >
            Get Started
          </button>
        </div>
      )}
    </div>
  );
};

export default H_Right;
