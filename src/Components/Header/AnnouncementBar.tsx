import React, { useState } from "react";
import { Link } from "react-router-dom";

const AnnouncementBar: React.FC = () => {
  const [show, setshow] = useState(true);

  const handleShow = () => {
    setshow(!show);
  };

  if (show) {
    return (
      <div className="bg-GTI-BLUE-default text-white text-center py-2 text-sm flex justify-center items-center relative w-full">
        <span>
          CHECK OUT OUR &nbsp;
          <Link to="/displayer-services" className="underline font-semibold">
            PREMIUM SERVICES
          </Link>
          &nbsp;FOR ALL DISPLAYERS OUT THERE
        </span>
        <button
          className="absolute right-4 text-white"
          aria-label="Close"
          onClick={handleShow}
        >
          ✕
        </button>
      </div>
    );
  }

  return <div></div>;
};

export default AnnouncementBar;
