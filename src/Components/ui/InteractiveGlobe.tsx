import React, { Suspense } from "react";
import { World } from "./globe";

interface InteractiveGlobeProps {
  className?: string;
  height?: string;
}

export default function InteractiveGlobe({
  className = "",
  height = "h-96",
}: InteractiveGlobeProps) {
  const globeConfig = {
    pointSize: 4,
    globeColor: "#151E70", // GTI-BLUE-default
    showAtmosphere: true,
    atmosphereColor: "#FFFFFF",
    atmosphereAltitude: 0.1,
    emissive: "#151E70",
    emissiveIntensity: 0.1,
    shininess: 0.9,
    polygonColor: "rgba(255,255,255,0.7)",
    ambientLight: "#38bdf8",
    directionalLeftLight: "#ffffff",
    directionalTopLight: "#ffffff",
    pointLight: "#ffffff",
    arcTime: 1500,
    arcLength: 0.9,
    rings: 1,
    maxRings: 3,
    initialPosition: { lat: 22.3193, lng: 114.1694 },
    autoRotate: true,
    autoRotateSpeed: 0.3,
  };

  const colors = ["#151E70", "#3b82f6", "#06b6d4"]; // GTI colors

  // Sample connection arcs representing global technology partnerships
  const sampleArcs = [
    // North America to Asia
    {
      order: 1,
      startLat: 37.7749, // San Francisco
      startLng: -122.4194,
      endLat: 35.6762, // Tokyo
      endLng: 139.6503,
      arcAlt: 0.3,
      color: colors[0],
    },
    // Europe to India
    {
      order: 2,
      startLat: 51.5072, // London
      startLng: -0.1276,
      endLat: 28.6139, // New Delhi
      endLng: 77.209,
      arcAlt: 0.3,
      color: colors[1],
    },
    // US to Brazil
    {
      order: 3,
      startLat: 40.7128, // New York
      startLng: -74.006,
      endLat: -23.5505, // São Paulo
      endLng: -46.6333,
      arcAlt: 0.4,
      color: colors[2],
    },
    // Singapore to Australia
    {
      order: 4,
      startLat: 1.3521, // Singapore
      startLng: 103.8198,
      endLat: -33.8688, // Sydney
      endLng: 151.2093,
      arcAlt: 0.2,
      color: colors[0],
    },
    // Germany to China
    {
      order: 5,
      startLat: 52.52, // Berlin
      startLng: 13.405,
      endLat: 39.9042, // Beijing
      endLng: 116.4074,
      arcAlt: 0.3,
      color: colors[1],
    },
    // Canada to UK
    {
      order: 6,
      startLat: 43.6532, // Toronto
      startLng: -79.3832,
      endLat: 51.5072, // London
      endLng: -0.1276,
      arcAlt: 0.2,
      color: colors[2],
    },
    // India to Singapore
    {
      order: 7,
      startLat: 19.076, // Mumbai
      startLng: 72.8777,
      endLat: 1.3521, // Singapore
      endLng: 103.8198,
      arcAlt: 0.2,
      color: colors[0],
    },
    // Japan to South Korea
    {
      order: 8,
      startLat: 35.6762, // Tokyo
      startLng: 139.6503,
      endLat: 37.5665, // Seoul
      endLng: 126.978,
      arcAlt: 0.1,
      color: colors[1],
    },
  ];

  return (
    <div className={`relative w-full ${height} ${className}`}>
      <Suspense
        fallback={
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-GTI-BLUE-default"></div>
          </div>
        }
      >
        <World data={sampleArcs} globeConfig={globeConfig} />
      </Suspense>
    </div>
  );
}
