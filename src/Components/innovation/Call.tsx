import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import axios from "axios";
import { AiOutlineDownload } from "react-icons/ai";
import { Helmet } from "react-helmet";

import { InnovationItemFetched, NONE } from "../constants";
import innovation_banner from "../../assests/banners/innovation_banner.png";
import { downloadFile } from "../utils/fileDownloader";

const Call = () => {
  const { id } = useParams();
  const state = {
    id,
  };
  let [innovation, setInnovation] = useState<InnovationItemFetched>({
    _id: NONE,
    title: NONE,
    description: NONE,
    companyName: NONE,
    companyLogo: NONE,
    companyDocument: NONE,
    imageUrl: NONE,
    startDate: NONE,
    endDate: NONE,
    displayOnHomePage: false,
    isDeleted: false,
    createdAt: NONE,
    formLink: "",
    referenceCode: NONE,
    __v: -1,
  });
  const loadCall = () => {
    var config = {
      method: "put",
      url: `${process.env.REACT_APP_BASE_API}/innovation-calls/${state.id}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setInnovation(response.data);
      })
      .catch(function (error) {});
  };
  useEffect(() => {
    loadCall();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const DOS = new Date(innovation.startDate);
  const DOE = new Date(innovation.endDate);

  const handleDownload = async () => {
    // eslint-disable-next-line array-callback-return
    innovation.companyDocument.split(" ").map((item, id) => {
      downloadFile(item);
    });
  };

  return (
    <React.Fragment>
      <div id="main" className="innovation-main">
        <Helmet>
          <title>{innovation.title}</title>
          <meta
            name="description"
            key="description"
            content={innovation.description}
          />
          <meta name="title" key="title" content={innovation.title} />
          <meta property="og:title" content={innovation.title} />
          <meta
            property="og:description"
            content={`${innovation.description}`}
          />
          <meta property="og:image" content={innovation.companyLogo} />
          <meta
            property="og:url"
            content={`${process.env.REACT_APP_BASE_URL}/innovation-call-view/${innovation._id}`}
          />
          <meta property="og:type" content="website" />
          <meta name="twitter:title" content={innovation.title} />
          <meta name="twitter:description" content={innovation.description} />
          <meta name="twitter:image" content={innovation.companyLogo} />
          <meta name="twitter:card" content={innovation.title} />
        </Helmet>
        <img
          src={
            innovation.imageUrl === NONE
              ? innovation_banner
              : innovation.imageUrl
          }
          className="innovation-banner"
          alt={innovation.companyName}
          loading="lazy"
        />
        <div className="innovation-parent-details">
          <div className="innovation-details">
            <div className="innovation-group">
              <h4 className="text-gray-500 font-roboto">
                {DOS.toLocaleString("default", {
                  month: "long",
                  day: "2-digit",
                  year: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                  hourCycle: "h12",
                })}
              </h4>
            </div>
            <div className="innovation-group">
              <img
                src={innovation.companyLogo}
                alt={innovation.companyName}
                className="comp-logo"
              />
              <h1 className="lg:text-4xl font-roboto">
                {innovation.companyName}
              </h1>
            </div>
            <div className="innovation-group">
              <h1 className="lg:text-xl font-roboto font-semibold">Title :</h1>
              <h1 className="lg:text-4xl font-roboto whitespace-nowrap">
                {innovation.title}
              </h1>
            </div>
            <div className="innovation-group">
              <h4 className="text-xl font-bold">Description</h4>
              <p
                dangerouslySetInnerHTML={{ __html: innovation.description }}
              ></p>
            </div>
            <div className="innovation-group space-y-2">
              <p className="text-xl font-bold font-roboto whitespace-nowrap">
                Deadline{" "}
                <span className="font-normal text-red-500">
                  {DOE.toLocaleString("default", {
                    month: "long",
                    day: "2-digit",
                    year: "numeric",
                  })}
                </span>
              </p>
              <button
                onClick={() => {
                  handleDownload();
                }}
                className="inline-flex items-center"
              >
                <AiOutlineDownload className="w-7 h-7 mr-2" />
                Download
              </button>
            </div>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default Call;
