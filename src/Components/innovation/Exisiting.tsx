import React, { Dispatch, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import {
  CONTENT_TYPE,
  CONTENT_TYPE_DOC,
  FILE_PATH,
  FILE_TYPE,
  InnovationItemFetched,
  InnovationUpdateItem,
  LIMIT,
  NONE,
  presignedData,
  SKIP,
} from "../constants";
import {
  deleteCallId,
  getCalls,
  updateCall,
} from "../../store/actioncreators/innovationactions";
import { RiEditBoxFill } from "react-icons/ri";
import { MdPreview } from "react-icons/md";
import { AiOutlineDownload } from "react-icons/ai";
import { RiDeleteBin6Fill } from "react-icons/ri";
import "./style.css";
import axios from "axios";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import { useNavigate } from "react-router-dom";
import { downloadFile } from "../utils/fileDownloader";

export type InnovationItemModal = {
  title: string;
  description: string;
  companyName: string;
  companyLogo: File | null;
  companyDocument: FileList | null;
  imageUrl: File | null;
  startDate: string;
  endDate: string;
};

const InnovationModal = ({
  currentInnovation,
  handleModal,
}: {
  currentInnovation: InnovationItemFetched;
  handleModal: () => void;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const [innovationModal, setModalData] = useState<InnovationItemModal>({
    title: currentInnovation.title,
    description: currentInnovation.description,
    companyName: currentInnovation.companyName,
    companyLogo: null,
    companyDocument: null,
    imageUrl: null,
    startDate: currentInnovation.startDate,
    endDate: currentInnovation.endDate,
  });
  const DOS: Date = new Date(currentInnovation.startDate);
  const DOE: Date = new Date(currentInnovation.endDate);

  const handleLogo = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files;

    if (!fileList) return;

    setModalData({ ...innovationModal, companyLogo: fileList[0] });
  };
  const handleBanner = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files;

    if (!fileList) return;

    setModalData({ ...innovationModal, imageUrl: fileList[0] });
  };
  const handleDocuments = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files;

    if (!fileList) return;

    setModalData({ ...innovationModal, companyDocument: fileList });
  };

  const getPresigned = async (content: presignedData) => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: "post",
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data: data,
    };

    await axios(config)
      .then(function (response) {
        result = response.data;
      })
      .catch(function (error) {
        result = "error";
        dispatch(failToast());
      });

    return result;
  };

  const postLogo = async (signed: string) => {
    var config = {
      method: "put",
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: innovationModal.companyLogo,
    };

    await axios(config)
      .then(function (response) {
        // console.log("logo uploaded");
        dispatch(successToast());
      })
      .catch(function (error) {
        // console.log("error uploading logo");
      });
  };
  const postBanner = async (signed: string) => {
    var config = {
      method: "put",
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: innovationModal.imageUrl,
    };

    await axios(config)
      .then(function (response) {
        // console.log("logo uploaded");
        dispatch(successToast());
      })
      .catch(function (error) {
        // console.log("error uploading logo");
      });
  };
  const postDocument = async (signed: string, file: File) => {
    var config = {
      method: "put",
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE_DOC,
        "Access-Control-Allow-Origin": true,
      },
      data: file,
    };

    await axios(config)
      .then(function (response) {
        // console.log("document uploaded");
        dispatch(successToast());
      })
      .catch(function (error) {
        // console.log("error uploading document");
      });
  };

  const handleEdit = async () => {
    let signedLogoURL: string = "";
    let signedBannerURL: string = "";
    let signedDocumentURLWhole: string = "";

    // if (!innovationModal.companyLogo?.size && !innovationModal.companyDocument) return;

    if (innovationModal.companyLogo) {
      const signedLogoData: presignedData = {
        fileName: innovationModal.companyLogo?.name || innovationModal.title,
        filePath: FILE_PATH.COMPANY_LOGO,
        fileType: FILE_TYPE.PNG,
      };
      signedLogoURL = await getPresigned(signedLogoData);
      await postLogo(signedLogoURL);
    }
    if (innovationModal.imageUrl) {
      const signedBannerData: presignedData = {
        fileName: innovationModal.companyLogo?.name || innovationModal.title,
        filePath: FILE_PATH.BANNER,
        fileType: FILE_TYPE.PNG,
      };
      signedBannerURL = await getPresigned(signedBannerData);
      await postBanner(signedBannerURL);
    }
    if (innovationModal.companyDocument) {
      Array.from(innovationModal.companyDocument).forEach(
        async (document, i) => {
          let signedDocumentData: presignedData = {
            fileName: document.name || innovationModal.title,
            filePath: FILE_PATH.COMPANY_DOCS,
            fileType: FILE_TYPE.PDF,
          };
          let tempurl = (await getPresigned(signedDocumentData)) + " ";
          signedDocumentURLWhole = tempurl.split("?")[0] + " ";
          postDocument(tempurl, document);
          // signedDocumentURL.push(tempurl);
        }
      );
    }
    setTimeout(() => {
      const data: InnovationUpdateItem = {
        innovationCallsId: currentInnovation._id,
        title: innovationModal.title,
        description: innovationModal.description,
        companyName: innovationModal.companyName,
        companyLogo: innovationModal.companyLogo
          ? signedLogoURL
          : currentInnovation.companyLogo,
        companyDocument: innovationModal.companyDocument
          ? signedDocumentURLWhole
          : currentInnovation.companyDocument,
        imageUrl: innovationModal.imageUrl
          ? signedBannerURL.split("?")[0]
          : currentInnovation.imageUrl,
        startDate: new Date(innovationModal.startDate).toISOString(),
        endDate: new Date(innovationModal.endDate).toISOString(),
      };
      dispatch(updateCall(data));
      handleModal();
    }, 2000);
  };
  return (
    <div className="innovation-modal-main">
      <div className="flex">
        <h4 className="text-lg font-roboto">Edit Call</h4>
        <button
          onClick={() => {
            handleModal();
          }}
          className="absolute right-0 top-0 font-bold hover:text-red-500 duration-300 border border-slate-100 px-3 py-1 rounded"
        >
          X
        </button>
      </div>
      <div className="flex flex-row w-full space-x-2">
        <input
          defaultValue={DOS.toLocaleString("default", {
            month: "short",
            day: "2-digit",
            year: "numeric",
          })}
          type="text"
          onFocus={(e) => {
            e.target.type = "date";
          }}
          onBlur={(e) => {
            e.target.type = "text";
          }}
          placeholder="Date of Post"
          onChange={(e) => {
            setModalData({ ...innovationModal, startDate: e.target.value });
          }}
          aria-label="dop"
          className="modal-input"
        />
        <input
          defaultValue={DOE.toLocaleString("default", {
            month: "short",
            day: "2-digit",
            year: "numeric",
          })}
          type="text"
          onFocus={(e) => {
            e.target.type = "date";
          }}
          onBlur={(e) => {
            e.target.type = "text";
          }}
          placeholder="Last deadline of Submission"
          onChange={(e) => {
            setModalData({ ...innovationModal, endDate: e.target.value });
          }}
          aria-label="lds"
          className="modal-input"
        />
      </div>
      <input
        defaultValue={currentInnovation.companyName}
        onChange={(e) => {
          setModalData({ ...innovationModal, companyName: e.target.value });
        }}
        type="text"
        aria-label="company-name"
        className="modal-input"
        placeholder="Company Name"
      />
      <input
        defaultValue={currentInnovation.title}
        onChange={(e) => {
          setModalData({ ...innovationModal, title: e.target.value });
        }}
        type="text"
        aria-label="title"
        className="modal-input"
        placeholder="Title"
      />
      <input
        defaultValue={currentInnovation.description}
        onChange={(e) => {
          setModalData({ ...innovationModal, description: e.target.value });
        }}
        type="text"
        aria-label="description"
        className="modal-input"
        placeholder="Description"
      />
      <div className="flex flex-col w-full">
        <label
          className="block mb-2 text-sm font-medium text-gray-900"
          htmlFor="banner"
        >
          Upload Innovation Banner
          <span className="text-red-500 text-xs">(.png only)</span>
        </label>
        <input
          onChange={handleBanner}
          accept=".png"
          type="file"
          id="banner"
          aria-label="company-logo"
          className="modal-input"
          placeholder="Click to upload Company's Logo"
        />
      </div>
      <div className="flex flex-col w-full">
        <label
          className="block mb-2 text-sm font-medium text-gray-900"
          htmlFor="logo"
        >
          Upload Company Logo{" "}
          <span className="text-red-500 text-xs">(.png only)</span>
        </label>
        <input
          onChange={handleLogo}
          accept=".png"
          type="file"
          id="logo"
          aria-label="company-logo"
          className="modal-input"
          placeholder="Click to upload Company's Logo"
        />
      </div>
      <div className="flex flex-col w-full">
        <label
          className="block mb-2 text-sm font-medium text-gray-900"
          htmlFor="documents"
        >
          Upload Documents
          <span className="text-red-500 text-xs">(.pdf only)</span>
        </label>
        <input
          onChange={handleDocuments}
          accept=".pdf"
          type="file"
          id="documents"
          multiple
          aria-label="company-documents"
          className="modal-input"
          placeholder="Click to upload Document"
        />
      </div>
      <button
        onClick={() => {
          handleEdit();
        }}
        className="innovation-button innovation-active"
      >
        Edit
      </button>
    </div>
  );
};

const Card = ({
  item,
  skip,
  handleSelect,
}: {
  item: InnovationItemFetched;
  skip: string;
  handleSelect: (data: InnovationItemFetched) => void;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();
  const DOS = new Date(item.startDate);

  const handleEdit = () => {
    handleSelect(item);
  };
  const handleView = () => {
    navigate(`/innovation-call/${item._id}`, { state: { id: item._id } });
  };
  const handleDelete = async () => {
    await dispatch(deleteCallId(item._id));
    await dispatch(getCalls(SKIP, LIMIT));
  };
  const handleDownload = () => {
    item.companyDocument.split(" ").map((document, id) => {
      downloadFile(document);
    });
  };

  return (
    <React.Fragment>
      <div className="existing-card-main">
        <div className="existing-item">
          <img
            src={item.companyLogo}
            alt={item.companyName}
            className="existing-company-logo"
          />
        </div>
        <div className="existing-item">
          <h4 className="font-roboto text-GTI-BLUE-default">
            {item.companyName}
          </h4>
          <h4 className="font-roboto text-xs">
            {item.description.length > 50
              ? item.description.substring(0, 150) + "..."
              : item.description}
          </h4>
        </div>
        <div>
          <h4 className="font-roboto text-xs whitespace-nowrap">
            Deadline of Submission{" "}
            <span className="font-bold text-GTI-BLUE-default">
              {DOS.toLocaleString("default", {
                month: "short",
                day: "2-digit",
                year: "numeric",
              })}
            </span>
          </h4>
        </div>
        <div className="existing-item-action">
          <RiEditBoxFill
            onClick={() => {
              handleEdit();
            }}
            className="w-7 h-7 mr-2"
          />
          <MdPreview
            onClick={() => {
              handleView();
            }}
            className="w-7 h-7 mr-2"
          />
          {/* <PDFDownloadLink document={<MyDoc innovation={item} />} fileName={`${item.title}.pdf`}  > */}
          <AiOutlineDownload
            onClick={() => {
              handleDownload();
            }}
            className="w-7 h-7 mr-2"
          />
          {/* </PDFDownloadLink> */}
          <RiDeleteBin6Fill
            onClick={() => {
              handleDelete();
            }}
            className="w-7 h-7"
          />
        </div>
      </div>
    </React.Fragment>
  );
};

const Existing = ({ skip, limit }: { skip: string; limit: string }) => {
  const dispatch: Dispatch<any> = useDispatch();
  useEffect(() => {
    dispatch(getCalls(skip, limit));
  }, [skip]);
  const innovation: INNOVATION = useSelector(
    (state: STATE) => state.INNOVATION.INNOVATION
  );

  const [innovationEditModal, setModal] = useState(false);
  const [currentInnovation, setInnovation] = useState<InnovationItemFetched>({
    _id: NONE,
    title: NONE,
    description: NONE,
    companyName: NONE,
    companyLogo: NONE,
    companyDocument: NONE,
    imageUrl: NONE,
    startDate: NONE,
    endDate: NONE,
    displayOnHomePage: false,
    isDeleted: false,
    createdAt: NONE,
    formLink: "",
    referenceCode: NONE,
    __v: -1,
  });

  const handleModel = () => {
    setModal(!innovationEditModal);
    dispatch(getCalls(skip, limit));
  };

  const handleSelect = (item: InnovationItemFetched) => {
    if (item.__v !== -1) {
      setInnovation(item);
      handleModel();
    }
  };

  const now = new Date().toISOString();

  const [showDrop, setDrop] = useState(false);
  const [dataFilter, setFilter] = useState({
    drop: false,
    dropval: "All",
    search: false,
    searchval: "",
  });
  const [loadMore, setMore] = useState(false);
  return (
    <React.Fragment>
      <div className="existing-main">
        {!loadMore &&
          innovation.INNOVATION_LIST.slice(0, 9)
            .filter((item: InnovationItemFetched) => {
              return item.endDate >= now;
            })
            .map((item: InnovationItemFetched, id) => {
              return (
                <Card
                  item={item}
                  key={id}
                  skip={skip}
                  handleSelect={handleSelect}
                />
              );
            })}
        {loadMore &&
          innovation.INNOVATION_LIST.filter((item: InnovationItemFetched) => {
            return item.endDate >= now;
          }).map((item: InnovationItemFetched, id) => {
            return (
              <Card
                item={item}
                key={id}
                skip={skip}
                handleSelect={handleSelect}
              />
            );
          })}
      </div>
      {innovationEditModal && (
        <InnovationModal
          currentInnovation={currentInnovation}
          handleModal={handleModel}
        />
      )}
      <div className="flex justify-center mb-5 py-5">
        {!loadMore && (
          <button
            onClick={() => {
              setMore(true);
            }}
            className=" items-center py-2 px-4 text-sm font-medium text-GTI-BLUE-default bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
          >
            Load More
          </button>
        )}
      </div>
    </React.Fragment>
  );
};

export default Existing;
