import React, { Dispatch, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { InnovationItemFetched } from "../constants";
import { getCalls } from "../../store/actioncreators/innovationactions";
import { MdPreview } from "react-icons/md";
import { AiOutlineDownload } from "react-icons/ai";
import { useNavigate } from "react-router-dom";
import "./style.css";
import { downloadFile } from "../utils/fileDownloader";

const Card = ({
  item,
  skip,
}: {
  item: InnovationItemFetched;
  skip: string;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();
  const DOS = new Date(item.startDate);

  const handleView = () => {
    navigate(`/innovation-call/${item._id}`, { state: { id: item._id } });
  };
  const handleDownload = () => {
    item.companyDocument.split(" ").map((document, id) => {
      downloadFile(document);
    });
  };
  return (
    <React.Fragment>
      <div className="existing-card-main">
        <div className="existing-item">
          <img
            src={item.companyLogo}
            alt={item.companyName}
            className="existing-company-logo"
          />
        </div>
        <div className="existing-item">
          <h4 className="font-roboto text-GTI-BLUE-default">
            {item.companyName}
          </h4>
          <h4 className="font-roboto text-xs">
            {item.description.length > 50
              ? item.description.substring(0, 150) + "..."
              : item.description}
          </h4>
        </div>
        <div>
          <h4 className="font-roboto text-xs whitespace-nowrap">
            Deadline of Submission{" "}
            <span className="font-bold text-GTI-BLUE-default">
              {DOS.toLocaleString("default", {
                month: "short",
                day: "2-digit",
                year: "numeric",
              })}
            </span>
          </h4>
        </div>
        <div className="existing-item-action">
          <MdPreview
            onClick={() => {
              handleView();
            }}
            className="w-7 h-7 mr-2"
          />
          {/* <PDFDownloadLink document={<MyDoc innovation={item} />} fileName={`${item.title}.pdf`}  > */}
          <AiOutlineDownload className="w-7 h-7 mr-2" />
          {/* </PDFDownloadLink> */}
        </div>
      </div>
    </React.Fragment>
  );
};

const Applied = ({ skip, limit }: { skip: string; limit: string }) => {
  const dispatch: Dispatch<any> = useDispatch();
  useEffect(() => {
    dispatch(getCalls(skip, limit));
  }, [skip]);
  const innovation: INNOVATION = useSelector(
    (state: STATE) => state.INNOVATION.INNOVATION
  );

  const now = new Date().toISOString();
  const [dataFilter, setFilter] = useState({
    drop: false,
    dropval: "All",
    dropTitle: false,
    titleVal: "All",
    search: false,
    searchval: "",
  });

  const [loadMore, setMore] = useState(false);
  return (
    <React.Fragment>
      <div className="applied-main">
        <div className="flex w-full justify-start ">
          <div className="flex flex-row justify-end duration-200 z-10 px-5">
            <button
              id="dropdownDefault"
              data-dropdown-toggle="dropdown"
              className="w-fit text-white border-2 bg-GTI-BLUE-default focus:outline-none font-medium font-roboto rounded-lg m-1 text-sm px-4 py-2.5  text-center inline-flex items-center border-slate-300 justify-center flex-shrink"
              type="button"
              onClick={() => {
                setFilter({
                  ...dataFilter,
                  drop: !dataFilter.drop,
                  dropTitle: false,
                  titleVal: "All",
                });
              }}
            >
              {innovation.INNOVATION_LIST && dataFilter.dropval}
              <svg
                className="ml-2 w-4 h-4"
                aria-hidden="true"
                fill="grey"
                stroke="grey"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div
              id="dropdown"
              className={
                "relative z-10 rounded " + (!dataFilter.drop ? "hidden" : "")
              }
              data-popper-placement="bottom"
            >
              <ul
                className="absolute flex flex-col  -left-20  top-14 text-sm font-roboto bg-slate-100"
                aria-labelledby="dropdownDefault"
              >
                <li
                  className="block z-10 py-2 px-4 rounded  text-GTI-BLUE-default  whitespace-nowrap hover:text-slate-500 "
                  onClick={() => {
                    setFilter({ ...dataFilter, drop: false, dropval: "All" });
                  }}
                >
                  All
                </li>
                {innovation &&
                  innovation.INNOVATION_LIST.map(
                    (item: InnovationItemFetched, id) => {
                      return (
                        <li
                          className="block z-10 py-2 px-4 rounded  text-GTI-BLUE-default  whitespace-nowrap hover:text-slate-500 "
                          onClick={() => {
                            setFilter({
                              ...dataFilter,
                              drop: false,
                              dropval: item.companyName,
                            });
                          }}
                        >
                          {item.companyName}
                        </li>
                      );
                    }
                  )}
              </ul>
            </div>
          </div>
          <div className="flex flex-row justify-end duration-200 z-10 px-5">
            <button
              id="dropdownDefault"
              data-dropdown-toggle="dropdown"
              className="w-fit text-white border-2 bg-GTI-BLUE-default focus:outline-none font-medium font-roboto rounded-lg m-1 text-sm px-4 py-2.5  text-center inline-flex items-center border-slate-300 justify-center flex-shrink"
              type="button"
              onClick={() => {
                setFilter({
                  ...dataFilter,
                  drop: false,
                  dropval: "All",
                  dropTitle: !dataFilter.dropTitle,
                });
              }}
            >
              {innovation.INNOVATION_LIST && dataFilter.titleVal}
              <svg
                className="ml-2 w-4 h-4"
                aria-hidden="true"
                fill="grey"
                stroke="grey"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div
              id="dropdown"
              className={
                "relative z-10 rounded " +
                (!dataFilter.dropTitle ? "hidden" : "")
              }
              data-popper-placement="bottom"
            >
              <ul
                className="absolute flex flex-col  -left-20  top-14 text-sm font-roboto bg-slate-100"
                aria-labelledby="dropdownDefault"
              >
                <li
                  className="block z-10 py-2 px-4 rounded  text-GTI-BLUE-default  whitespace-nowrap hover:text-slate-500 "
                  onClick={() => {
                    setFilter({
                      ...dataFilter,
                      dropTitle: false,
                      titleVal: "All",
                    });
                  }}
                >
                  All
                </li>
                {innovation &&
                  innovation.INNOVATION_LIST.map(
                    (item: InnovationItemFetched, id) => {
                      return (
                        <li
                          className="block z-10 py-2 px-4 rounded  text-GTI-BLUE-default  whitespace-nowrap hover:text-slate-500 "
                          onClick={() => {
                            setFilter({
                              ...dataFilter,
                              dropTitle: false,
                              titleVal: item.title,
                            });
                          }}
                        >
                          {item.title}
                        </li>
                      );
                    }
                  )}
              </ul>
            </div>
          </div>
        </div>
        <div className="applied-content-main">
          {!loadMore &&
            dataFilter.dropval !== "All" &&
            innovation.INNOVATION_LIST.slice(0, 9)
              .filter((item: InnovationItemFetched) => {
                return item.endDate < now;
              })
              .filter((item: InnovationItemFetched) => {
                return item.companyName === dataFilter.dropval;
              })
              .map((item: InnovationItemFetched, id: number) => {
                return <Card item={item} key={id} skip={skip} />;
              })}
          {!loadMore &&
            dataFilter.titleVal !== "All" &&
            innovation.INNOVATION_LIST.slice(0, 9)
              .filter((item: InnovationItemFetched) => {
                return item.endDate < now;
              })
              .filter((item: InnovationItemFetched) => {
                return item.title === dataFilter.titleVal;
              })
              .map((item: InnovationItemFetched, id: number) => {
                return <Card item={item} key={id} skip={skip} />;
              })}
          {!loadMore &&
            dataFilter.dropval === "All" &&
            dataFilter.titleVal === "All" &&
            innovation.INNOVATION_LIST.slice(0, 9)
              .filter((item: InnovationItemFetched) => {
                return item.endDate < now;
              })
              .map((item: InnovationItemFetched, id: number) => {
                return <Card item={item} key={id} skip={skip} />;
              })}
          {loadMore &&
            dataFilter.dropval !== "All" &&
            innovation.INNOVATION_LIST.filter((item: InnovationItemFetched) => {
              return item.endDate < now;
            })
              .filter((item: InnovationItemFetched) => {
                return item.companyName === dataFilter.dropval;
              })
              .map((item: InnovationItemFetched, id: number) => {
                return <Card item={item} key={id} skip={skip} />;
              })}
          {loadMore &&
            dataFilter.titleVal !== "All" &&
            innovation.INNOVATION_LIST.filter((item: InnovationItemFetched) => {
              return item.endDate < now;
            })
              .filter((item: InnovationItemFetched) => {
                return item.title === dataFilter.titleVal;
              })
              .map((item: InnovationItemFetched, id: number) => {
                return <Card item={item} key={id} skip={skip} />;
              })}
          {loadMore &&
            dataFilter.dropval === "All" &&
            dataFilter.titleVal === "All" &&
            innovation.INNOVATION_LIST.filter((item: InnovationItemFetched) => {
              return item.endDate < now;
            }).map((item: InnovationItemFetched, id: number) => {
              return <Card item={item} key={id} skip={skip} />;
            })}
        </div>
        <div className="flex justify-center mb-5 py-5">
          {!loadMore && (
            <button
              onClick={() => {
                setMore(true);
              }}
              className=" items-center py-2 px-4 text-sm font-medium text-GTI-BLUE-default bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
            >
              Load More
            </button>
          )}
        </div>
      </div>
    </React.Fragment>
  );
};

export default Applied;
