import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  FaEnvelope,
  FaPhone,
  FaBuilding,
  FaMapMarkerAlt,
  FaCheck,
} from "react-icons/fa";
import { ConnectionStatus } from "../constants";
import defaultPic from "../../assests/images/default-user.svg";
import { acceptConnection } from "../../api/user";
import { isSuccess, notify } from "../../utils";
import { getConnections } from "../../store/actioncreators/connectionactions";

interface ConnectionCardProps {
  user: any;
  status: ConnectionStatus;
}

const ConnectionCard: React.FC<ConnectionCardProps> = ({ user, status }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch<any>();
  const [isLoading, setIsLoading] = useState(false);

  const data = {
    ...(user.sender ? user.sender : user.receiver ? user.receiver : user),
    ...(user.sender
      ? user.senderCompany
      : user.receiverCompany
      ? user.receiverCompany
      : {}),
  };

  const handleAcceptConnection = async () => {
    setIsLoading(true);
    try {
      const response = await acceptConnection(user._id);
      if (isSuccess(response.status)) {
        notify("Connection accepted successfully!", "success");
        dispatch(getConnections(status));
      } else {
        notify("Failed to accept connection", "error");
      }
    } catch (error) {
      notify("Error accepting connection", "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewProfile = () => {
    navigate(`/user/${data._id}`);
  };

  const handleStartChat = () => {
    // Navigate to chat or messaging interface
    navigate(`/chat/${data._id}`);
  };

  const getStatusBadge = () => {
    const badges: Record<ConnectionStatus, string> = {
      [ConnectionStatus.CONNECTED]: "bg-green-100 text-green-800",
      [ConnectionStatus.PENDING]: "bg-yellow-100 text-yellow-800",
      [ConnectionStatus.REQUESTED]: "bg-blue-100 text-blue-800",
    };
    return badges[status] || "bg-gray-100 text-gray-800";
  };

  const renderActionButtons = () => {
    if (status === ConnectionStatus.PENDING) {
      return (
        <button
          onClick={handleAcceptConnection}
          disabled={isLoading}
          className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 disabled:opacity-50 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
        >
          <FaCheck className="w-4 h-4" />
          <span>Accept Connection</span>
        </button>
      );
    }

    if (status === ConnectionStatus.CONNECTED) {
      return (
        <div className="flex gap-3">
          <button
            onClick={handleStartChat}
            className="flex-1 px-4 py-3 bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            Chat
          </button>
          <button
            onClick={handleViewProfile}
            className="flex-1 px-4 py-3 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-xl hover:from-gray-200 hover:to-gray-300 transition-all duration-200 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            Profile
          </button>
        </div>
      );
    }

    return (
      <button
        onClick={handleViewProfile}
        className="w-full px-4 py-3 bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
      >
        View Profile
      </button>
    );
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-GTI-BLUE-default/30 h-full flex flex-col group">
      {/* Header with Status Badge */}
      <div className="relative p-6 pb-4 flex-shrink-0">
        <div
          className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-medium ${getStatusBadge()}`}
        >
          {status}
        </div>

        {/* Profile Section - Horizontal Layout */}
        <div className="flex items-center gap-4 mb-4">
          <img
            src={data.profileImage || defaultPic}
            alt={data.fullName || "User"}
            className="w-16 h-16 rounded-2xl object-cover border-4 border-white shadow-lg cursor-pointer hover:scale-105 transition-transform duration-200 flex-shrink-0"
            onClick={handleViewProfile}
          />
          <div className="flex-1 min-w-0">
            <h3
              className="text-lg font-semibold text-gray-900 mb-1 line-clamp-2 cursor-pointer hover:text-GTI-BLUE-default transition-colors duration-200"
              onClick={handleViewProfile}
            >
              {data.fullName || "Unknown User"}
            </h3>
            {data.name && (
              <div className="flex items-center gap-2 text-GTI-BLUE-default text-sm font-medium">
                <FaBuilding className="w-4 h-4 flex-shrink-0" />
                <span className="truncate">{data.name}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Contact Info */}
      <div className="px-6 pb-4 flex-grow">
        <div className="bg-gray-50 rounded-xl p-4 space-y-3">
          {data.email && (
            <div className="flex items-center gap-3 text-sm text-gray-700">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <FaEnvelope className="w-4 h-4 text-blue-600" />
              </div>
              <span className="truncate font-medium">{data.email}</span>
            </div>
          )}
          {data.phone && (
            <div className="flex items-center gap-3 text-sm text-gray-700">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <FaPhone className="w-4 h-4 text-green-600" />
              </div>
              <span className="truncate font-medium">{data.phone}</span>
            </div>
          )}
          {data.country && (
            <div className="flex items-center gap-3 text-sm text-gray-700">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <FaMapMarkerAlt className="w-4 h-4 text-purple-600" />
              </div>
              <span className="truncate font-medium">{data.country}</span>
            </div>
          )}
          {!data.email && !data.phone && !data.country && (
            <div className="text-center text-gray-500 text-sm py-2">
              No contact information available
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="px-6 pb-6 mt-auto">{renderActionButtons()}</div>
    </div>
  );
};

export default ConnectionCard;
