import React, { useEffect, useState, Dispatch } from "react";
import { useSelector } from "react-redux";
import Card2 from "../Pager/Card2";

import { useDispatch } from "react-redux";
import { getConnections } from "../../store/actioncreators/connectionactions";
import { connectionOptions, ConnectionStatus } from "../constants";

const Connections = () => {
  const dispatch: Dispatch<any> = useDispatch();

  const [dropdownConnectionStatus, setDropdownConnectionStatus] = useState(
    ConnectionStatus.CONNECTED
  );

  const connections: CONNECTION = useSelector(
    (state: STATE) => state.CONNECTION.CONNECTION
  );

  // const handleSearch = (e: any) => {
  //   if (e.target.value === "") {
  //     setSearch({ key: "", process: false });
  //     return;
  //   }
  //   setSearch({ key: e.target.value, process: true });
  // };

  useEffect(() => {
    dispatch(getConnections(dropdownConnectionStatus));
  }, [dropdownConnectionStatus, dispatch]);

  return (
    <div className="w-full">
      <div className="profile-form-section">
        <h2 className="profile-form-title">My Connections</h2>

        <div className="profile-form-group">
          <label className="profile-form-label">Filter by Status</label>
          <select
            id="connectionStatus"
            value={dropdownConnectionStatus}
            onChange={(event) => {
              const status: ConnectionStatus = event.target
                .value as ConnectionStatus;
              setDropdownConnectionStatus(status);
            }}
            className="profile-form-select"
          >
            {connectionOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.value}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="w-full mt-6">
        {connections && connections.CONNECTION_LIST.connections.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 lg:gap-8">
            {connections.CONNECTION_LIST.connections.map(
              (item: any, id: number) => {
                return (
                  <div key={id} className="h-full">
                    <Card2
                      title={item.fullName}
                      user={item}
                      org={item.companyId}
                      email={""}
                      Img={item.profileImage}
                      status={dropdownConnectionStatus}
                    />
                  </div>
                );
              }
            )}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="p-8 bg-white rounded-2xl shadow-lg max-w-lg mx-auto border border-gray-100">
              <div className="p-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                <div className="w-10 h-10 bg-gray-400 rounded-full"></div>
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                No connections found
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                You don't have any {dropdownConnectionStatus.toLowerCase()}{" "}
                connections yet. Start connecting with professionals in your
                industry.
              </p>
            </div>
          </div>
        )}
      </div>
      {/* {search.process &&
        connections &&
        connections.CONNECTION_LIST.connections.filter((item: connectionItem) =>
          item.fullName.match(new RegExp(search.key, "i"))
        ).map((item: any, id: number) => {
          return (
            <div className="grid grid-cols-5 w-full  py-4 mx-5 ">
              <Card2
                title={item.fullName}
                user={item}
                org={item.companyId}
                email={""}
                Img={item.profileImage}
              />
              ;
            </div>
          );
        })}

      {!search.process &&
        connections.CONNECTION_LIST.connections.map(
          (item: connectionItem, index: number) => {
            return (
              <div className="grid grid-cols-5 w-full  py-4 mx-5 " key={index}>
                <Card2
                  title={item.fullName}
                  org={item.companyId}
                  email={""}
                  Img={item.profileImage}
                  user={item}
                />
              </div>
            );
          }
        )} */}
    </div>
  );
};

export default Connections;
