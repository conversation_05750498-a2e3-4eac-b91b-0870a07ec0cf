import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FaArrowLeft, FaUsers } from "react-icons/fa";
import ConnectionCard from "./ConnectionCard";
import { getConnections } from "../../store/actioncreators/connectionactions";
import { connectionOptions, ConnectionStatus } from "../constants";

const ConnectionsPage: React.FC = () => {
  const dispatch = useDispatch<any>();
  const navigate = useNavigate();

  const [dropdownConnectionStatus, setDropdownConnectionStatus] = useState(
    ConnectionStatus.CONNECTED
  );
  const connections: CONNECTION = useSelector(
    (state: STATE) => state.CONNECTION.CONNECTION
  );

  useEffect(() => {
    dispatch(getConnections(dropdownConnectionStatus));
  }, [dropdownConnectionStatus, dispatch]);

  const getStatusBadge = (status: ConnectionStatus) => {
    const badges: Record<ConnectionStatus, string> = {
      [ConnectionStatus.CONNECTED]: "bg-green-100 text-green-800",
      [ConnectionStatus.PENDING]: "bg-yellow-100 text-yellow-800",
      [ConnectionStatus.REQUESTED]: "bg-blue-100 text-blue-800",
    };
    return badges[status] || "bg-gray-100 text-gray-800";
  };

  const getStatusIcon = (status: ConnectionStatus) => {
    switch (status) {
      case ConnectionStatus.CONNECTED:
        return "✓";
      case ConnectionStatus.PENDING:
        return "⏳";
      case ConnectionStatus.REQUESTED:
        return "📤";
      default:
        return "•";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 w-full pt-4">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100 w-full">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate(-1)}
                className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-GTI-BLUE-default transition-colors duration-200 rounded-lg hover:bg-gray-50"
              >
                <FaArrowLeft className="w-4 h-4" />
                <span className="font-medium">Back</span>
              </button>
              <div className="flex items-center gap-4">
                <div className="p-3 bg-GTI-BLUE-default/10 rounded-xl">
                  <FaUsers className="w-6 h-6 text-GTI-BLUE-default" />
                </div>
                <div className="py-1">
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 leading-tight">
                    My Connections
                  </h1>
                  <p className="text-gray-600 text-sm sm:text-base mt-1">
                    {connections?.CONNECTION_LIST?.connections?.length || 0}{" "}
                    {(connections?.CONNECTION_LIST?.connections?.length ||
                      0) === 1
                      ? "connection"
                      : "connections"}
                  </p>
                </div>
              </div>
            </div>

            {/* Status Badge */}
            <div
              className={`px-4 py-2 rounded-full text-sm font-medium ${getStatusBadge(
                dropdownConnectionStatus
              )}`}
            >
              <span className="mr-2">
                {getStatusIcon(dropdownConnectionStatus)}
              </span>
              {dropdownConnectionStatus}
            </div>
          </div>
        </div>
      </div>

      {/* Simple Search and Status Filter */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
        {/* Connection Status Filter */}
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <label className="text-base font-semibold text-gray-800">
              Filter by Status:
            </label>
            <select
              value={dropdownConnectionStatus}
              onChange={(e) =>
                setDropdownConnectionStatus(e.target.value as ConnectionStatus)
              }
              className="border border-gray-200 rounded-xl px-4 py-3 focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default text-sm font-medium bg-gray-50 hover:bg-white transition-colors duration-200"
            >
              {connectionOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.value}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Connections Grid */}
        {connections?.CONNECTION_LIST?.connections?.length > 0 ? (
          <div className="mt-10">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 lg:gap-8">
              {connections.CONNECTION_LIST.connections.map(
                (connection: any, index: number) => (
                  <div
                    key={connection.id || connection._id}
                    className="transform hover:scale-[1.02] transition-all duration-300 hover:shadow-2xl connection-card-enter"
                    style={{
                      animationDelay: `${index * 100}ms`,
                    }}
                  >
                    <ConnectionCard
                      user={connection}
                      status={dropdownConnectionStatus}
                    />
                  </div>
                )
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="p-8 bg-white rounded-2xl shadow-lg max-w-lg mx-auto border border-gray-100">
              <div className="p-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                <FaUsers className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                No connections found
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                You don't have any {dropdownConnectionStatus.toLowerCase()}{" "}
                connections yet. Start connecting with professionals in your
                industry.
              </p>
              <button
                onClick={() => navigate("/technology")}
                className="px-6 py-3 bg-GTI-BLUE-default text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 font-medium"
              >
                Explore Technologies
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConnectionsPage;
