import React from "react";
import { useNavigate } from "react-router-dom";
import {
  ExclamationTriangleIcon,
  ArrowRightIcon,
  ArrowPathIcon,
} from "@heroicons/react/24/outline";

import error from "../../assests/error.gif";
import "./style.css";

const PremiumServicesPaymentFailure = ({
  handleLoginModal,
}: {
  handleLoginModal: () => void;
}) => {
  let navigate = useNavigate();

  return (
    <div className="premium-services-main min-h-screen flex items-center justify-center">
      <div className="max-w-2xl mx-auto text-center p-8">
        {/* Error Animation */}
        <div className="mb-8">
          <div className="relative">
            <img
              src={error}
              alt="Payment Failed"
              className="h-32 w-32 mx-auto mb-4"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <ExclamationTriangleIcon className="w-20 h-20 text-red-500 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-red-600 via-orange-600 to-yellow-600 bg-clip-text text-transparent mb-6">
            Payment Failed
          </h1>
          <div className="flex items-center justify-center mb-6">
            <div className="bg-red-500/10 rounded-2xl p-3 mr-3">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
            </div>
            <span className="text-red-600 font-bold text-lg">
              Transaction Unsuccessful
            </span>
          </div>
          <div className="w-16 h-1 bg-gradient-to-r from-red-500 to-orange-500 mx-auto mb-6"></div>
          <p className="text-gray-600 text-lg leading-relaxed max-w-lg mx-auto">
            We're sorry, but your payment could not be processed at this time.
            This could be due to various reasons such as insufficient funds,
            network issues, or bank restrictions.
            <br />
            <br />
            Please try again or contact your bank if the issue persists.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => navigate(-1)}
            className="premium-btn-primary max-w-xs mx-auto sm:mx-0 flex items-center justify-center"
          >
            <ArrowPathIcon className="w-5 h-5 mr-2" />
            Try Again
          </button>
          <button
            onClick={() => navigate("/")}
            className="premium-btn-secondary max-w-xs mx-auto sm:mx-0 flex items-center justify-center"
          >
            Return to Home
            <ArrowRightIcon className="w-5 h-5 ml-2" />
          </button>
        </div>

        {/* Help Section */}
        <div className="mt-12 p-6 bg-red-50 rounded-2xl">
          <h3 className="text-lg font-semibold text-red-600 mb-2">
            Need Help?
          </h3>
          <p className="text-gray-600 mb-4">
            If you continue to experience payment issues, please contact our
            support team.
          </p>
          <a
            href="mailto:<EMAIL>"
            className="premium-btn-email max-w-xs mx-auto"
          >
            Contact Support
          </a>
        </div>

        {/* Common Issues */}
        <div className="mt-8 p-6 bg-gray-50 rounded-2xl">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Common Payment Issues
          </h3>
          <ul className="text-gray-600 text-left space-y-2 max-w-md mx-auto text-sm">
            <li className="flex items-start">
              <ExclamationTriangleIcon className="w-4 h-4 text-orange-500 mr-2 mt-0.5 flex-shrink-0" />
              Insufficient funds in your account
            </li>
            <li className="flex items-start">
              <ExclamationTriangleIcon className="w-4 h-4 text-orange-500 mr-2 mt-0.5 flex-shrink-0" />
              Card expired or blocked by bank
            </li>
            <li className="flex items-start">
              <ExclamationTriangleIcon className="w-4 h-4 text-orange-500 mr-2 mt-0.5 flex-shrink-0" />
              Network connectivity issues
            </li>
            <li className="flex items-start">
              <ExclamationTriangleIcon className="w-4 h-4 text-orange-500 mr-2 mt-0.5 flex-shrink-0" />
              Incorrect payment details
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PremiumServicesPaymentFailure;
