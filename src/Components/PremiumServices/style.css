/* Modern Premium Services Styles */

/* Main Container */
.premium-services-main {
  @apply flex flex-col relative min-h-screen w-full bg-gradient-to-br from-slate-50 via-white to-blue-50;
}

/* Hero Section */
.premium-hero-section {
  @apply relative bg-gradient-to-r from-GTI-BLUE-default via-blue-600 to-blue-700 text-white py-20 px-4 overflow-hidden;
}

.premium-hero-content {
  @apply max-w-7xl mx-auto text-center relative z-10;
}

.premium-hero-title {
  @apply text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent;
}

.premium-hero-subtitle {
  @apply text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed;
}

.premium-hero-background {
  @apply absolute inset-0 opacity-10;
  background-image: radial-gradient(
      circle at 20% 50%,
      white 1px,
      transparent 1px
    ),
    radial-gradient(circle at 80% 50%, white 1px, transparent 1px);
  background-size: 100px 100px;
}

/* Service Cards */
.premium-service-card {
  @apply bg-white rounded-3xl shadow-lg hover:shadow-2xl hover:shadow-GTI-BLUE-default/20 transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 overflow-hidden;
}

.premium-service-card:hover .premium-service-card-icon {
  @apply scale-110;
}

.premium-service-card-header {
  @apply relative p-8 bg-gradient-to-br from-GTI-BLUE-default to-blue-600 text-white;
}

.premium-service-card-icon {
  @apply w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-4 transition-transform duration-300;
}

.premium-service-card-title {
  @apply text-2xl font-bold mb-2;
}

.premium-service-card-subtitle {
  @apply text-blue-100 text-lg;
}

.premium-service-card-content {
  @apply p-8 flex-1 flex flex-col;
}

.premium-service-card-description {
  @apply text-gray-600 leading-relaxed mb-6 flex-1;
}

/* Feature Lists */
.premium-feature-list {
  @apply space-y-4 mb-8;
}

.premium-feature-item {
  @apply flex items-start space-x-3;
}

.premium-feature-icon {
  @apply w-6 h-6 text-green-500 mt-0.5 flex-shrink-0;
}

.premium-feature-text {
  @apply text-gray-700 font-medium;
}

/* Pricing Cards */
.premium-pricing-card {
  @apply bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-GTI-BLUE-default/20 transform hover:-translate-y-1;
}

.premium-pricing-header {
  @apply p-6 text-center;
}

.premium-pricing-title {
  @apply text-xl font-bold text-GTI-BLUE-default mb-2;
}

.premium-pricing-price {
  @apply text-3xl font-bold text-gray-900 mb-1;
}

.premium-pricing-period {
  @apply text-gray-500 text-sm;
}

.premium-pricing-content {
  @apply p-6 pt-0 flex-1 flex flex-col;
}

.premium-pricing-features {
  @apply space-y-3 mb-8 flex-1;
}

.premium-pricing-feature {
  @apply flex items-center space-x-3;
}

.premium-pricing-feature-icon {
  @apply w-5 h-5 text-green-500 flex-shrink-0;
}

.premium-pricing-feature-text {
  @apply text-gray-600;
}

/* Buttons */
.premium-btn-primary {
  @apply w-full bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white font-semibold py-4 px-6 rounded-xl hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-blue-300;
}

.premium-btn-secondary {
  @apply w-full bg-white text-GTI-BLUE-default border-2 border-GTI-BLUE-default font-semibold py-4 px-6 rounded-xl hover:bg-GTI-BLUE-default hover:text-white transform hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-blue-300;
}

.premium-btn-email {
  @apply w-full bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold py-4 px-6 rounded-xl hover:from-green-600 hover:to-green-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-green-300 flex items-center justify-center space-x-2;
}

/* Service Sections */
.premium-services-section {
  @apply py-16 px-4;
}

.premium-services-container {
  @apply max-w-7xl mx-auto;
}

.premium-section-title {
  @apply text-3xl md:text-4xl font-bold text-center text-gray-900 mb-4;
}

.premium-section-subtitle {
  @apply text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto;
}

/* Grid Layouts */
.premium-services-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16;
}

.premium-pricing-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

/* Additional Services */
.premium-additional-services {
  @apply bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-8 md:p-12;
}

.premium-additional-title {
  @apply text-2xl md:text-3xl font-bold text-GTI-BLUE-default text-center mb-8;
}

.premium-additional-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.premium-additional-card {
  @apply bg-white rounded-2xl p-6 shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1;
}

.premium-additional-number {
  @apply w-12 h-12 bg-gradient-to-br from-GTI-BLUE-default to-blue-600 text-white rounded-xl flex items-center justify-center font-bold text-lg mb-4;
}

.premium-additional-text {
  @apply text-gray-700 leading-relaxed;
}

/* Range Slider */
.premium-range-container {
  @apply mb-8;
}

.premium-range-label {
  @apply text-lg font-semibold text-GTI-BLUE-default mb-4 text-center;
}

.premium-range-slider {
  @apply w-full h-3 bg-gradient-to-r from-blue-200 to-blue-300 rounded-lg appearance-none cursor-pointer;
}

.premium-range-slider::-webkit-slider-thumb {
  @apply appearance-none w-6 h-6 bg-gradient-to-r from-GTI-BLUE-default to-blue-600 rounded-full cursor-pointer shadow-lg;
}

.premium-range-values {
  @apply flex justify-between items-center mt-4;
}

.premium-range-current {
  @apply text-2xl font-bold text-GTI-BLUE-default;
}

.premium-range-max {
  @apply text-gray-500;
}

.premium-range-note {
  @apply text-sm text-gray-600 text-center mt-2;
}

/* Image Containers */
.premium-image-container {
  @apply relative overflow-hidden rounded-2xl shadow-lg;
}

.premium-image {
  @apply w-full h-full object-cover transition-transform duration-300 hover:scale-105;
}

/* Responsive Design */
@media (max-width: 640px) {
  .premium-hero-title {
    @apply text-3xl;
  }

  .premium-hero-subtitle {
    @apply text-lg;
  }

  .premium-service-card-header {
    @apply p-6;
  }

  .premium-service-card-content {
    @apply p-6;
  }

  .premium-services-section {
    @apply py-12 px-4;
  }

  .premium-additional-services {
    @apply p-6;
  }
}

@media (max-width: 768px) {
  .premium-services-grid {
    @apply grid-cols-1 gap-6;
  }

  .premium-pricing-grid {
    @apply grid-cols-1 gap-6;
  }

  .premium-additional-grid {
    @apply grid-cols-1 gap-4;
  }
}
