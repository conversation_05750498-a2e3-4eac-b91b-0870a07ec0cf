import React from "react";
import { useNavigate } from "react-router-dom";
import { CheckCircleIcon, ArrowRightIcon } from "@heroicons/react/24/outline";

import success from "../../assests/success.gif";
import "./style.css";

const PremiumServicesPaymentSuccess = ({
  handleLoginModal,
}: {
  handleLoginModal: () => void;
}) => {
  let navigate = useNavigate();

  return (
    <div className="premium-services-main min-h-screen flex items-center justify-center">
      <div className="max-w-2xl mx-auto text-center p-8">
        {/* Success Animation */}
        <div className="mb-8">
          <div className="relative">
            <img
              src={success}
              alt="Payment Success"
              className="h-32 w-32 mx-auto mb-4"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <CheckCircleIcon className="w-20 h-20 text-green-500 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Success Message */}
        <div className="mb-8">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-green-600 via-green-500 to-emerald-500 bg-clip-text text-transparent mb-6">
            Payment Successful!
          </h1>
          <div className="flex items-center justify-center mb-6">
            <div className="bg-green-500/10 rounded-2xl p-3 mr-3">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <span className="text-green-600 font-bold text-lg">
              Service Request Submitted
            </span>
          </div>
          <div className="w-16 h-1 bg-gradient-to-r from-green-500 to-emerald-500 mx-auto mb-6"></div>
          <p className="text-gray-600 text-lg leading-relaxed max-w-lg mx-auto">
            Thank you for choosing GTI Premium Services! Your service request
            has been successfully submitted and payment processed.
            <br />
            <br />
            Our team will review your request and contact you within 24-48 hours
            to begin the process.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => navigate("/")}
            className="premium-btn-primary max-w-xs mx-auto sm:mx-0 flex items-center justify-center"
          >
            Return to Home
            <ArrowRightIcon className="w-5 h-5 ml-2" />
          </button>
          <a
            href="mailto:<EMAIL>"
            className="premium-btn-secondary max-w-xs mx-auto sm:mx-0 flex items-center justify-center"
          >
            Contact Support
          </a>
        </div>

        {/* Additional Info */}
        <div className="mt-12 p-6 bg-blue-50 rounded-2xl">
          <h3 className="text-lg font-semibold text-GTI-BLUE-default mb-2">
            What's Next?
          </h3>
          <ul className="text-gray-600 text-left space-y-2 max-w-md mx-auto">
            <li className="flex items-start">
              <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              You'll receive a confirmation email shortly
            </li>
            <li className="flex items-start">
              <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              Our team will contact you within 24-48 hours
            </li>
            <li className="flex items-start">
              <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              We'll begin working on your service request immediately
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PremiumServicesPaymentSuccess;
