import React from "react";
import { <PERSON> } from "react-router-dom";
import { Helmet } from "react-helmet";
import {
  ChartBarIcon,
  MagnifyingGlassIcon,
  UserGroupIcon,
  LightBulbIcon,
  ArrowRightIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";
import * as ROUTE from "../Constants/routes";
import { title, metaData } from "../constants";
import "./style.css";

interface PremiumServicesLandingProps {
  handleLoginModal: () => void;
}

const PremiumServicesLanding: React.FC<PremiumServicesLandingProps> = ({
  handleLoginModal,
}) => {
  const services = [
    {
      title: "For Technology Displayers",
      subtitle: "Market Access & Innovation Services",
      description:
        "Comprehensive support for technology companies, entrepreneurs, MSMEs, startups, and R&D institutions to deploy and validate their technology in new markets.",
      icon: <ChartBarIcon className="w-8 h-8" />,
      features: [
        "Market Insight Reports",
        "Technology Validation",
        "Partner/Customer/Investor Connections",
        "Market Entry Strategy",
      ],
      route: ROUTE.DISPLAYER_PREMIUM_SERVICES,
      color: "from-blue-500 to-blue-600",
    },
    {
      title: "For Technology Scouters",
      subtitle: "Technology Scouting & Innovation",
      description:
        "Customized assistance for finding technology partners for collaborations or investments through our comprehensive scouting platform.",
      icon: <MagnifyingGlassIcon className="w-8 h-8" />,
      features: [
        "Technology Scouting",
        "Open Innovation Challenges",
        "Technical Analysis",
        "Pitch Sessions & Workshops",
      ],
      route: ROUTE.SCOUTER_PREMIUM_SERVICES,
      color: "from-green-500 to-green-600",
    },
  ];

  const benefits = [
    {
      icon: <UserGroupIcon className="w-8 h-8" />,
      title: "Expert Network",
      description: "Access to our extensive network of industry experts and stakeholders",
    },
    {
      icon: <LightBulbIcon className="w-8 h-8" />,
      title: "Innovation Focus",
      description: "Cutting-edge solutions tailored to your specific business needs",
    },
    {
      icon: <ChartBarIcon className="w-8 h-8" />,
      title: "Market Intelligence",
      description: "Deep market insights and competitive analysis for informed decisions",
    },
  ];

  return (
    <div className="premium-services-main">
      <Helmet>
        <title>{title.PREMIUM_SERVICES || "GTI Premium Services"}</title>
        <meta
          name="description"
          key="description"
          content={metaData.PREMIUM_SERVICES || "GTI Market Access and Innovation Services"}
        />
        <meta name="title" key="title" content="GTI Premium Services" />
        <meta property="og:title" content="GTI Premium Services" />
        <meta property="og:description" content="GTI Market Access and Innovation Services" />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Hero Section */}
      <section className="premium-hero-section">
        <div className="premium-hero-background"></div>
        <div className="premium-hero-content">
          <h1 className="premium-hero-title">
            GTI Premium Services
          </h1>
          <p className="premium-hero-subtitle">
            Accelerate your innovation journey with our comprehensive market access and technology scouting services
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
            <Link
              to={ROUTE.DISPLAYER_PREMIUM_SERVICES}
              className="premium-btn-primary max-w-xs mx-auto sm:mx-0"
            >
              For Displayers
              <ArrowRightIcon className="w-5 h-5 ml-2" />
            </Link>
            <Link
              to={ROUTE.SCOUTER_PREMIUM_SERVICES}
              className="premium-btn-secondary max-w-xs mx-auto sm:mx-0"
            >
              For Scouters
              <ArrowRightIcon className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="premium-services-section">
        <div className="premium-services-container">
          <h2 className="premium-section-title">Our Premium Services</h2>
          <p className="premium-section-subtitle">
            Choose the service that best fits your innovation and market access needs
          </p>

          <div className="premium-services-grid">
            {services.map((service, index) => (
              <div key={index} className="premium-service-card">
                <div className={`premium-service-card-header bg-gradient-to-br ${service.color}`}>
                  <div className="premium-service-card-icon">
                    {service.icon}
                  </div>
                  <h3 className="premium-service-card-title">{service.title}</h3>
                  <p className="premium-service-card-subtitle">{service.subtitle}</p>
                </div>
                
                <div className="premium-service-card-content">
                  <p className="premium-service-card-description">
                    {service.description}
                  </p>
                  
                  <div className="premium-feature-list">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="premium-feature-item">
                        <CheckIcon className="premium-feature-icon" />
                        <span className="premium-feature-text">{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <Link
                    to={service.route}
                    className="premium-btn-primary flex items-center justify-center"
                  >
                    Learn More
                    <ArrowRightIcon className="w-5 h-5 ml-2" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="premium-services-section bg-white">
        <div className="premium-services-container">
          <h2 className="premium-section-title">Why Choose GTI Premium Services?</h2>
          <p className="premium-section-subtitle">
            We provide comprehensive support to accelerate your innovation and market access goals
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-GTI-BLUE-default to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 text-white">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="premium-services-section bg-gradient-to-r from-GTI-BLUE-default to-blue-600 text-white">
        <div className="premium-services-container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Accelerate Your Innovation?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Get started with our premium services and unlock new opportunities for growth and collaboration
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="premium-btn-email max-w-xs mx-auto sm:mx-0"
            >
              Contact Us
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PremiumServicesLanding;
