import React, { useState, useEffect, useRef } from "react";
import {
  ChevronDownIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  CheckIcon,
  FunnelIcon,
} from "@heroicons/react/24/outline";
import { sectorItem } from "../constants";

interface ModernSectorFilterProps {
  sectors: sectorItem[];
  selectedSector: {
    id: string;
    selected: string;
  };
  onSectorChange: (sector: { id: string; selected: string }) => void;
  isLoading?: boolean;
}

const ModernSectorFilter: React.FC<ModernSectorFilterProps> = ({
  sectors,
  selectedSector,
  onSectorChange,
  isLoading = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredSectors, setFilteredSectors] = useState<sectorItem[]>([]);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter sectors based on search term
  useEffect(() => {
    if (!sectors) {
      setFilteredSectors([]);
      return;
    }

    const filtered = sectors.filter((sector) =>
      sector.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredSectors(filtered);
  }, [sectors, searchTerm]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
      setFocusedIndex(-1);
    }
  }, [isOpen]);

  // Reset focused index when search term changes
  useEffect(() => {
    setFocusedIndex(-1);
  }, [searchTerm]);

  // Keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    const allOptions = [
      { id: "", name: "All Sectors" },
      ...filteredSectors.map((sector) => ({
        id: sector._id,
        name: sector.name,
      })),
    ];

    switch (e.key) {
      case "Escape":
        setIsOpen(false);
        setSearchTerm("");
        break;
      case "ArrowDown":
        e.preventDefault();
        setFocusedIndex((prev) =>
          prev < allOptions.length - 1 ? prev + 1 : 0
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setFocusedIndex((prev) =>
          prev > 0 ? prev - 1 : allOptions.length - 1
        );
        break;
      case "Enter":
        e.preventDefault();
        if (focusedIndex >= 0 && focusedIndex < allOptions.length) {
          const selectedOption = allOptions[focusedIndex];
          if (selectedOption.id === "") {
            handleSectorSelect(null);
          } else {
            handleSectorSelect(selectedOption);
          }
        }
        break;
    }
  };

  const handleSectorSelect = (sector: { id: string; name: string } | null) => {
    if (sector) {
      onSectorChange({ id: sector.id, selected: sector.name });
    } else {
      onSectorChange({ id: "", selected: "All Sectors" });
    }
    setIsOpen(false);
    setSearchTerm("");
  };

  const clearSearch = () => {
    setSearchTerm("");
    searchInputRef.current?.focus();
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm("");
    }
  };

  const getSectorCount = () => {
    return sectors ? sectors.length + 1 : 0; // +1 for "All Sectors"
  };

  const getDisplayText = () => {
    if (isLoading) return "Loading...";
    if (!selectedSector.id) return "All Sectors";
    return selectedSector.selected;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Filter Button */}
      <button
        onClick={toggleDropdown}
        onKeyDown={handleKeyDown}
        disabled={isLoading}
        className="group relative inline-flex items-center gap-3 px-6 py-3 bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 hover:border-GTI-BLUE-default/30 focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default min-w-[240px] disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <div className="flex items-center gap-2 flex-1">
          <FunnelIcon className="w-4 h-4 text-GTI-BLUE-default" />
          <span className="text-gray-700 font-medium truncate">
            {getDisplayText()}
          </span>
        </div>

        {/* Sector count badge */}
        {!isLoading && (
          <span className="px-2 py-1 bg-GTI-BLUE-default/10 text-GTI-BLUE-default text-xs font-medium rounded-full">
            {getSectorCount()}
          </span>
        )}

        <ChevronDownIcon
          className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-50 overflow-hidden filter-dropdown-enter">
          {/* Search Header */}
          <div className="p-4 border-b border-gray-100 bg-gray-50">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search sectors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full pl-10 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default text-sm"
              />
              {searchTerm && (
                <button
                  onClick={clearSearch}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-150"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              )}
            </div>

            {/* Search results count */}
            <div className="mt-2 text-xs text-gray-500">
              {searchTerm
                ? `${filteredSectors.length} sector${
                    filteredSectors.length !== 1 ? "s" : ""
                  } found`
                : `${getSectorCount()} total sectors`}
            </div>
          </div>

          {/* Options List */}
          <div className="max-h-64 overflow-y-auto">
            {/* All Sectors Option */}
            {(!searchTerm ||
              "all sectors".includes(searchTerm.toLowerCase())) && (
              <div
                onClick={() => handleSectorSelect(null)}
                className={`flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors duration-150 border-b border-gray-100 ${
                  focusedIndex === 0
                    ? "bg-GTI-BLUE-default/10 text-GTI-BLUE-default"
                    : "hover:bg-gray-50 text-gray-700"
                }`}
              >
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="font-medium flex-1">All Sectors</span>
                {!selectedSector.id && (
                  <CheckIcon className="w-4 h-4 text-GTI-BLUE-default" />
                )}
              </div>
            )}

            {/* Sector Options */}
            {filteredSectors.length > 0 ? (
              filteredSectors.map((sector, index) => {
                const optionIndex =
                  !searchTerm ||
                  "all sectors".includes(searchTerm.toLowerCase())
                    ? index + 1
                    : index;
                return (
                  <div
                    key={sector._id}
                    onClick={() =>
                      handleSectorSelect({ id: sector._id, name: sector.name })
                    }
                    className={`flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors duration-150 last:border-b-0 border-b border-gray-100 ${
                      focusedIndex === optionIndex
                        ? "bg-GTI-BLUE-default/10 text-GTI-BLUE-default"
                        : "hover:bg-gray-50 text-gray-700"
                    }`}
                  >
                    <div className="w-2 h-2 bg-GTI-BLUE-default rounded-full"></div>
                    <span className="flex-1">{sector.name}</span>
                    {selectedSector.id === sector._id && (
                      <CheckIcon className="w-4 h-4 text-GTI-BLUE-default" />
                    )}
                  </div>
                );
              })
            ) : searchTerm ? (
              <div className="px-4 py-8 text-center text-gray-500">
                <MagnifyingGlassIcon className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">
                  No sectors found matching "{searchTerm}"
                </p>
                <button
                  onClick={clearSearch}
                  className="mt-2 text-xs text-GTI-BLUE-default hover:text-blue-700 transition-colors duration-150"
                >
                  Clear search
                </button>
              </div>
            ) : (
              <div className="px-4 py-8 text-center text-gray-500">
                <FunnelIcon className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No sectors available</p>
              </div>
            )}
          </div>

          {/* Footer */}
          {filteredSectors.length > 0 && (
            <div className="p-3 border-t border-gray-100 bg-gray-50">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>Use search to find specific sectors</span>
                <span>Press ESC to close</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ModernSectorFilter;
