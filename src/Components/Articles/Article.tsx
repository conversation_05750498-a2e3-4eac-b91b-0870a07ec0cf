import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { Helmet } from "react-helmet";
import {
  CalendarIcon,
  ClockIcon,
  ShareIcon,
  ArrowLeftIcon,
  BookOpenIcon,
} from "@heroicons/react/24/outline";
import { PlayIcon } from "@heroicons/react/24/solid";

import { articleItemFetched, NONE } from "../constants";
import articlebanner from "../../assests/banners/articlebanner.png";
import gtiLogo from "../../assests/images/gti_logo.svg";
import ReactPlayer from "react-player";
import axios from "axios";
import SocialShare from "../Shareable/SocialShare";
import { getArticles } from "../../store/actioncreators/articleactions";

const Card = ({ item }: { item: articleItemFetched }) => {
  const DOC = new Date(item.createdAt);
  const navigate = useNavigate();

  const handleView = () => {
    navigate(`/articles/${item._id}`, { state: { id: item._id } });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getReadingTime = (description: string) => {
    const wordsPerMinute = 200;
    const words = description.replace(/<[^>]*>/g, "").split(" ").length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min read`;
  };

  return (
    <article
      className="group relative bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-xl hover:shadow-GTI-BLUE-default/10 transition-all duration-300 cursor-pointer transform hover:-translate-y-1"
      onClick={handleView}
    >
      {/* Image/Video Container */}
      <div className="relative aspect-[16/10] overflow-hidden bg-gray-100">
        {!item.youtubeLink || item.youtubeLink === "none" ? (
          <>
            <img
              src={item?.imageUrl ?? articlebanner}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              alt={item.topic}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </>
        ) : (
          <div className="relative w-full h-full">
            <ReactPlayer
              url={item.youtubeLink === NONE ? articlebanner : item.youtubeLink}
              className="w-full h-full"
              width="100%"
              height="100%"
              light
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="bg-white/90 backdrop-blur-sm rounded-full p-3">
                <PlayIcon className="w-6 h-6 text-GTI-BLUE-default" />
              </div>
            </div>
          </div>
        )}

        {/* Category Badge */}
        <div className="absolute top-4 left-4">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/90 backdrop-blur-sm text-GTI-BLUE-default border border-white/20">
            Related
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Meta Information */}
        <div className="flex items-center gap-3 text-xs text-gray-500 mb-2">
          <div className="flex items-center gap-1">
            <CalendarIcon className="w-3 h-3" />
            <time>{formatDate(DOC)}</time>
          </div>
          <div className="flex items-center gap-1">
            <ClockIcon className="w-3 h-3" />
            <span>{getReadingTime(item.shortDescription)}</span>
          </div>
        </div>

        {/* Title */}
        <h3 className="text-sm font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-GTI-BLUE-default transition-colors duration-200">
          {item.topic?.replace(/(<([^>]+)>)/gi, "")}
        </h3>

        {/* Description */}
        <p className="text-gray-600 text-xs leading-relaxed line-clamp-2">
          {item.shortDescription}
        </p>
      </div>
    </article>
  );
};

const Article = () => {
  let [article, setArticle] = useState<articleItemFetched>({
    _id: "",
    topic: "",
    description: "",
    shortDescription: "",
    displayOnHomePage: false,
    isDeleted: false,
    sectorId: "",
    subSectorId: "",
    articleType: "",
    imageUrl: "",
    externalLink: "",
    youtubeLink: "",
    webinarId: "",
    createdAt: "",
    __v: -1,
  });
  const location = useLocation();
  const navigate = useNavigate();

  const articles: ARTICLE = useSelector(
    (state: STATE) => state.ARTICLE.ARTICLE
  );

  const dispatch: Dispatch<any> = useDispatch();
  const { id } = useParams();

  useEffect(() => {
    if (article) dispatch(getArticles(article.sectorId, "0", "4"));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [article]);

  const loadArticle = (id: string) => {
    var config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/article/${id}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setArticle(response.data);
      })
      .catch(function (error) {});
  };

  const addQuery = async (articleTitle: string) => {
    const regex = /[!@#$%^&*()_+{}\[\]:;<>,.?~\\/-]/g;

    const queryParams = new URLSearchParams(location.search);
    queryParams.set(
      "title",
      articleTitle
        .toLocaleLowerCase()
        .replaceAll(regex, "")
        .replaceAll(" ", "_")
    );
    const newSearchString = queryParams.toString();
    navigate({
      pathname: location.pathname,
      search: newSearchString,
    });
  };

  useEffect(() => {
    id && loadArticle(id);
  }, [id]);

  useEffect(() => {
    if (article?.topic) addQuery(article.topic);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [article]);

  const DOC = new Date(article.createdAt);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  const getReadingTime = (description: string) => {
    const wordsPerMinute = 200;
    const words = description.replace(/<[^>]*>/g, "").split(" ").length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min read`;
  };

  return (
    <React.Fragment>
      <Helmet>
        <title>{article.topic}</title>
        <meta
          name="description"
          key="description"
          content={article.description}
        />
        <meta name="title" key="title" content={article.topic} />
        <meta property="og:title" content={article.topic} />
        <meta property="og:description" content={article.description} />
        <meta
          property="og:image"
          content={article.imageUrl ? article.imageUrl : gtiLogo}
        />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/article/${id}`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content={article.topic} />
        <meta name="twitter:description" content={article.description} />
        <meta
          name="twitter:image"
          content={article.imageUrl ? article.imageUrl : gtiLogo}
        />
        <meta name="twitter:card" content={article.topic} />
      </Helmet>

      {/* Modern Article Layout */}
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        {/* Back Navigation */}
        <div className="bg-white border-b border-gray-100">
          <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <button
              onClick={() => navigate("/articles")}
              className="inline-flex items-center gap-2 text-GTI-BLUE-default hover:text-blue-700 transition-colors duration-200"
            >
              <ArrowLeftIcon className="w-4 h-4" />
              <span className="font-medium">Back to Articles</span>
            </button>
          </div>
        </div>
        {/* Article Content */}
        <article className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="max-w-4xl mx-auto">
            {/* Article Header */}
            <header className="mb-12">
              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-6">
                <div className="flex items-center gap-2">
                  <CalendarIcon className="w-4 h-4" />
                  <time>{formatDate(DOC)}</time>
                </div>
                <div className="flex items-center gap-2">
                  <ClockIcon className="w-4 h-4" />
                  <span>{getReadingTime(article.description)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <BookOpenIcon className="w-4 h-4" />
                  <span>Article</span>
                </div>
              </div>

              {/* Title */}
              <h1
                className="text-3xl md:text-5xl font-bold text-gray-900 leading-tight mb-8"
                dangerouslySetInnerHTML={{ __html: article.topic }}
              />

              {/* Social Share */}
              <div className="flex items-center justify-between border-t border-b border-gray-200 py-4">
                <SocialShare
                  url={`${process.env.REACT_APP_BASE_API}/article/${id}`}
                  topic={article.topic}
                />
                <button className="inline-flex items-center gap-2 text-gray-600 hover:text-GTI-BLUE-default transition-colors duration-200">
                  <ShareIcon className="w-4 h-4" />
                  <span className="text-sm font-medium">Share</span>
                </button>
              </div>
            </header>

            {/* Featured Image/Video */}
            <div className="mb-12">
              <div className="relative aspect-[16/9] overflow-hidden rounded-2xl bg-gray-100 shadow-lg">
                {!article.youtubeLink ? (
                  <img
                    src={
                      article.imageUrl === "none" || !article.imageUrl
                        ? articlebanner
                        : article.imageUrl
                    }
                    className="w-full h-full object-cover"
                    alt={article.topic}
                    loading="lazy"
                  />
                ) : (
                  <div className="relative w-full h-full bg-black">
                    <ReactPlayer
                      url={
                        article.youtubeLink === NONE
                          ? articlebanner
                          : article.youtubeLink
                      }
                      className="w-full h-full"
                      width="100%"
                      height="100%"
                      controls
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Article Content */}
            <div className="prose prose-lg prose-gray max-w-none">
              <div
                className="text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: article.description }}
              />
            </div>
          </div>
        </article>

        {/* Related Articles Section */}
        <section className="bg-white border-t border-gray-100">
          <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Related Articles
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover more insights and knowledge from our comprehensive
                article collection
              </p>
            </div>

            {articles.articles
              .filter((article) => article?._id !== id)
              .slice(0, 3).length === 0 ? (
              <div className="text-center py-12">
                <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <BookOpenIcon className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No related articles
                </h3>
                <p className="text-gray-500">
                  Check back later for more content in this category.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {articles.articles
                  .filter((article) => article?._id !== id)
                  .slice(0, 3)
                  .map((item, index) => (
                    <Card item={item} key={index} />
                  ))}
              </div>
            )}

            {/* View All Articles Button */}
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/articles")}
                className="inline-flex items-center gap-2 px-8 py-3 bg-GTI-BLUE-default text-white font-medium rounded-xl hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
              >
                <BookOpenIcon className="w-5 h-5" />
                View All Articles
              </button>
            </div>
          </div>
        </section>
      </div>
    </React.Fragment>
  );
};

export default Article;
