import React from "react";

// Skeleton loader for article cards
export const ArticleCardSkeleton = () => {
  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden animate-pulse">
      {/* Image skeleton */}
      <div className="aspect-[16/10] bg-gray-200"></div>

      {/* Content skeleton */}
      <div className="p-6">
        {/* Meta information skeleton */}
        <div className="flex items-center gap-4 mb-3">
          <div className="flex items-center gap-1">
            <div className="w-4 h-4 bg-gray-200 rounded"></div>
            <div className="w-16 h-3 bg-gray-200 rounded"></div>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-4 h-4 bg-gray-200 rounded"></div>
            <div className="w-12 h-3 bg-gray-200 rounded"></div>
          </div>
        </div>

        {/* Title skeleton */}
        <div className="space-y-2 mb-3">
          <div className="w-full h-5 bg-gray-200 rounded"></div>
          <div className="w-3/4 h-5 bg-gray-200 rounded"></div>
        </div>

        {/* Description skeleton */}
        <div className="space-y-2 mb-4">
          <div className="w-full h-3 bg-gray-200 rounded"></div>
          <div className="w-full h-3 bg-gray-200 rounded"></div>
          <div className="w-2/3 h-3 bg-gray-200 rounded"></div>
        </div>

        {/* Read more skeleton */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-16 h-4 bg-gray-200 rounded"></div>
            <div className="w-4 h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Skeleton loader for article grid
export const ArticleGridSkeleton = ({ count = 9 }: { count?: number }) => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {Array.from({ length: count }).map((_, index) => (
          <ArticleCardSkeleton key={index} />
        ))}
      </div>
    </div>
  );
};

// Skeleton loader for individual article page
export const ArticleSkeleton = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Back Navigation Skeleton */}
      <div className="bg-white border-b border-gray-100">
        <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-2 animate-pulse">
            <div className="w-4 h-4 bg-gray-200 rounded"></div>
            <div className="w-24 h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>

      {/* Article Content Skeleton */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 animate-pulse">
        {/* Header Skeleton */}
        <div className="mb-12">
          {/* Meta Information Skeleton */}
          <div className="flex flex-wrap items-center gap-6 mb-6">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-200 rounded"></div>
              <div className="w-20 h-4 bg-gray-200 rounded"></div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-200 rounded"></div>
              <div className="w-16 h-4 bg-gray-200 rounded"></div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-200 rounded"></div>
              <div className="w-12 h-4 bg-gray-200 rounded"></div>
            </div>
          </div>

          {/* Title Skeleton */}
          <div className="space-y-4 mb-8">
            <div className="w-full h-8 bg-gray-200 rounded"></div>
            <div className="w-3/4 h-8 bg-gray-200 rounded"></div>
          </div>

          {/* Social Share Skeleton */}
          <div className="flex items-center justify-between border-t border-b border-gray-200 py-4">
            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-200 rounded"></div>
              <div className="w-12 h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>

        {/* Featured Image Skeleton */}
        <div className="mb-12">
          <div className="aspect-[16/9] bg-gray-200 rounded-2xl"></div>
        </div>

        {/* Content Skeleton */}
        <div className="space-y-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="space-y-2">
              <div className="w-full h-4 bg-gray-200 rounded"></div>
              <div className="w-full h-4 bg-gray-200 rounded"></div>
              <div className="w-3/4 h-4 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Related Articles Skeleton */}
      <div className="bg-white border-t border-gray-100">
        <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-16 animate-pulse">
          <div className="text-center mb-12">
            <div className="w-48 h-8 bg-gray-200 rounded mx-auto mb-4"></div>
            <div className="w-96 h-4 bg-gray-200 rounded mx-auto"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <ArticleCardSkeleton key={index} />
            ))}
          </div>

          <div className="text-center mt-12">
            <div className="w-40 h-12 bg-gray-200 rounded-xl mx-auto"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Skeleton for filter section
export const FilterSkeleton = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-pulse">
      <div className="flex flex-col sm:flex-row gap-6 items-start sm:items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-32 h-8 bg-gray-200 rounded"></div>
          <div className="w-20 h-6 bg-gray-200 rounded-full"></div>
        </div>

        <div className="w-full sm:w-auto">
          <div className="w-60 h-12 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    </div>
  );
};

// Skeleton for modern sector filter dropdown
export const ModernSectorFilterSkeleton = () => {
  return (
    <div className="relative animate-pulse">
      <div className="inline-flex items-center gap-3 px-6 py-3 bg-gray-100 border border-gray-200 rounded-xl min-w-[240px]">
        <div className="flex items-center gap-2 flex-1">
          <div className="w-4 h-4 bg-gray-200 rounded"></div>
          <div className="w-24 h-4 bg-gray-200 rounded"></div>
        </div>
        <div className="w-12 h-4 bg-gray-200 rounded-full"></div>
        <div className="w-5 h-5 bg-gray-200 rounded"></div>
      </div>
    </div>
  );
};

// Skeleton for hero section
export const HeroSkeleton = () => {
  return (
    <div className="relative overflow-hidden bg-gradient-to-r from-GTI-BLUE-default via-blue-700 to-indigo-800">
      <div className="absolute inset-0 bg-black/10"></div>
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24 animate-pulse">
        <div className="text-center">
          <div className="flex justify-center items-center mb-6">
            <div className="w-16 h-16 bg-white/20 rounded-2xl"></div>
          </div>

          <div className="space-y-4 mb-6">
            <div className="w-64 h-12 bg-white/20 rounded mx-auto"></div>
            <div className="w-48 h-12 bg-white/20 rounded mx-auto"></div>
          </div>

          <div className="max-w-3xl mx-auto space-y-2 mb-8">
            <div className="w-full h-4 bg-white/20 rounded"></div>
            <div className="w-3/4 h-4 bg-white/20 rounded mx-auto"></div>
          </div>

          <div className="flex flex-wrap justify-center gap-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="w-4 h-4 bg-white/20 rounded"></div>
                <div className="w-20 h-4 bg-white/20 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
