.button {
  @apply font-medium rounded-lg text-sm px-5 py-2.5 mx-1 mb-2  focus:outline-none;
}
.active {
  @apply bg-GTI-BLUE-default text-white hover:bg-blue-800;
}
.not-active {
  @apply bg-white text-GTI-BLUE-default border-2 border-slate-200;
}

/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

.stagger-children > * {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.stagger-children > *:nth-child(1) {
  animation-delay: 0.1s;
}
.stagger-children > *:nth-child(2) {
  animation-delay: 0.2s;
}
.stagger-children > *:nth-child(3) {
  animation-delay: 0.3s;
}
.stagger-children > *:nth-child(4) {
  animation-delay: 0.4s;
}
.stagger-children > *:nth-child(5) {
  animation-delay: 0.5s;
}
.stagger-children > *:nth-child(6) {
  animation-delay: 0.6s;
}
.stagger-children > *:nth-child(7) {
  animation-delay: 0.7s;
}
.stagger-children > *:nth-child(8) {
  animation-delay: 0.8s;
}
.stagger-children > *:nth-child(9) {
  animation-delay: 0.9s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern filter animations */
.filter-dropdown-enter {
  animation: dropdownSlideIn 0.2s ease-out;
}

.filter-dropdown-exit {
  animation: dropdownSlideOut 0.15s ease-in;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dropdownSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
}

/* Smooth focus transitions */
.focus-ring {
  transition: all 0.15s ease-in-out;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(21, 30, 112, 0.1);
}

/* Layout Fixes for Articles */
.max-w-none {
  max-width: none !important;
}

/* Full Width Container for Articles */
.articles-full-width-container {
  width: 100%;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}

/* Article Grid Layout */
.articles-grid {
  display: grid;
  gap: 1.5rem;
  width: 100%;
}

@media (min-width: 640px) {
  .articles-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .articles-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1280px) {
  .articles-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

/* Article Detail Layout */
.article-detail-container {
  max-width: 65ch;
  margin: 0 auto;
  width: 100%;
}

/* Responsive padding for articles */
.article-container-spacing {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .article-container-spacing {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .article-container-spacing {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
