import { useState } from "react";
import { BsFillPersonFill } from "react-icons/bs";
import { MdEmail } from "react-icons/md";
import { CgPhone } from "react-icons/cg";
import ReactFlagsSelect from "react-flags-select";
import { useSelector } from "react-redux";
import axios from "axios";
import { IoNotificationsCircle } from "react-icons/io5";
import { RequestMethods } from "../../shared/RequestMethods";
import EnhancedFormField from "../Profile/EnhancedFormField";

const Personal = () => {
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  let [emp_name, setEmpName] = useState(currentUser.user.name);
  let [emp_email, setEmpEmail] = useState(currentUser.user.email);
  let [emp_phone, setEmpPhone] = useState(String(currentUser.user.phone || ""));
  let [emp_phoneCountry, setEmpPhoneCountry] = useState(
    currentUser.user.country
  );
  let [emp_ref_code, setEmpRefCode] = useState("referal code");
  let [edit, setEdit] = useState(false);
  const [isSubscribeEmail, setIsSubscribeEmail] = useState(
    currentUser.subscribeEmail
  );

  // Validation functions
  const validateName = (name: string) => ({
    isValid: name.length >= 2 && name.length <= 50,
    message:
      name.length < 2
        ? "Name must be at least 2 characters"
        : "Name is too long",
  });

  const validatePhone = (phone: string) => ({
    isValid: phone.length >= 10 && /^\d+$/.test(phone),
    message: "Please enter a valid phone number (10+ digits)",
  });

  const validateRefCode = (code: string) => ({
    isValid: code.length >= 3,
    message: "Reference code must be at least 3 characters",
  });

  const toggleEmailNotification = async () => {
    const data = {
      subscribeEmail: !isSubscribeEmail,
    };

    const response = await axios.patch(
      `${process.env.REACT_APP_BASE_API}/users/subscribe`,
      data,
      {
        headers: {
          Authorization: `Bearer ${currentUser.token}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data) {
      setIsSubscribeEmail(!isSubscribeEmail);
    }
  };

  const updateDetails = () => {
    var data = JSON.stringify({
      fullName: emp_name,
      email: emp_email,
      countryCode: emp_phoneCountry,
      phoneNumber: emp_phone,
    });

    var config = {
      method: RequestMethods.PATCH,
      url: `${process.env.REACT_APP_BASE_API}/users/updateuserdetails`,
      headers: {
        Authorization: `Bearer ${currentUser.token}`,
        "Content-Type": "application/json",
      },
      data,
    };

    axios(config)
      .then(function (response) {})
      .catch(function (error) {});
  };
  let resetDetails = () => {
    setEmpEmail(currentUser.user.email);
    setEmpName(currentUser.user.name);
    setEmpPhone(String(currentUser.user.phone || ""));
    setEmpPhoneCountry(currentUser.user.country);
    setEmpRefCode(currentUser.user.ref);
  };

  let handleSavePersonal = async () => {
    await updateDetails();
    resetDetails();
    setEdit(!edit);
  };
  let handleCancelPersonal = () => {
    resetDetails();
    setEdit(!edit);
  };

  return (
    <div className="profile-form-container">
      <div className="profile-form-section">
        <h2 className="profile-form-title">Personal Information</h2>

        <div className="profile-form-grid">
          <EnhancedFormField
            label="Full Name"
            type="text"
            value={emp_name}
            onChange={setEmpName}
            disabled={!edit}
            required
            icon={<BsFillPersonFill />}
            validation={emp_name ? validateName(emp_name) : undefined}
            placeholder="Enter your full name"
            maxLength={50}
            showCharCount={edit}
          />

          <EnhancedFormField
            label="Email Address"
            type="email"
            value={emp_email}
            onChange={setEmpEmail}
            disabled={true}
            icon={<MdEmail />}
            helpText="Email cannot be changed"
          />

          <div className="profile-form-group">
            <label className="profile-form-label">Phone Number</label>
            <div className="flex gap-3">
              {edit && (
                <div className="w-32">
                  <ReactFlagsSelect
                    selected={emp_phoneCountry}
                    onSelect={(code) => setEmpPhoneCountry(code)}
                    fullWidth={false}
                    selectedSize={12}
                    showSelectedLabel={false}
                    showSecondarySelectedLabel={false}
                    showSecondaryOptionLabel={false}
                    searchable
                    className="block px-3 py-3 text-sm bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default h-[56px]"
                  />
                </div>
              )}
              <div className="flex-1">
                <EnhancedFormField
                  label="Phone Number"
                  type="tel"
                  value={emp_phone}
                  onChange={setEmpPhone}
                  disabled={!edit}
                  icon={<CgPhone />}
                  validation={
                    emp_phone ? validatePhone(String(emp_phone)) : undefined
                  }
                  placeholder="Enter phone number"
                />
              </div>
            </div>
          </div>

          <EnhancedFormField
            label="Reference Code"
            type="text"
            value={emp_ref_code}
            onChange={setEmpRefCode}
            disabled={!edit}
            icon={<BsFillPersonFill />}
            validation={
              emp_ref_code ? validateRefCode(emp_ref_code) : undefined
            }
            placeholder="Enter reference code"
            helpText="Optional referral or invitation code"
          />
        </div>

        <div className="profile-form-section">
          <h3 className="profile-form-title">Preferences</h3>

          <div className="profile-form-group">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <IoNotificationsCircle className="w-5 h-5 text-GTI-BLUE-default" />
                <div>
                  <label className="profile-form-label mb-0">
                    Email Notifications
                  </label>
                  <p className="text-sm text-gray-500">
                    Receive updates and notifications via email
                  </p>
                </div>
              </div>
              <button
                className={`relative w-12 h-6 rounded-full transition-colors duration-300 focus:outline-none focus:ring-4 focus:ring-GTI-BLUE-default/20
                  ${isSubscribeEmail ? "bg-GTI-BLUE-default" : "bg-gray-300"}`}
                onClick={toggleEmailNotification}
              >
                <span
                  className={`absolute left-1 top-1 w-4 h-4 bg-white rounded-full shadow-md transition-transform duration-300
                    ${isSubscribeEmail ? "translate-x-6" : "translate-x-0"}`}
                ></span>
              </button>
            </div>
          </div>
        </div>

        <div className="profile-button-group">
          {!edit ? (
            <button
              type="button"
              onClick={() => setEdit(true)}
              className="profile-btn-primary"
            >
              Edit Profile
            </button>
          ) : (
            <>
              <button
                type="button"
                onClick={handleSavePersonal}
                className="profile-btn-primary"
              >
                Save Changes
              </button>
              <button
                type="button"
                onClick={handleCancelPersonal}
                className="profile-btn-secondary"
              >
                Cancel
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Personal;
