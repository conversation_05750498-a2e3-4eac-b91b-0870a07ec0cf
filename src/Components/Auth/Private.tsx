import React, { useEffect } from "react";
import { Navigate } from "react-router-dom";
import { RedirectFunction } from "react-router-dom";
import { setUser } from "../../store/actioncreators/actionCreators";
import { Dispatch } from "redux";
import { useDispatch } from "react-redux";
import axios from "axios";
const Private = ({ handleLogin }: { handleLogin: () => void }) => {
  //   const dispatch: Dispatch<any> = useDispatch();

  //   const setUserDetails = React.useCallback(
  //     (newuser: User) => dispatch(setUser(newuser)),
  //     [dispatch]
  //   );
  const GTI_data = localStorage.getItem("GTI_data");
  if (GTI_data) {
    return;
  }
  //   const check=()=>{
  //   if (GTI_data) {
  //     let token: string = GTI_data.split(" ")[0];
  //     var data = "";

  //     var config = {
  //       method: "get",
  //       url: `${process.env.REACT_APP_BASE_API}/users/getLoggedInUserDetails`,
  //       headers: {
  //         Authorization:
  //           `Bearer ${token}`,
  //       },
  //       data: data,
  //     };

  //     axios(config)
  //       .then(function (response) {
  //         let currentUser:User={
  //             admin:+response.data.message.userDetails.userRole,
  //             token:response.data.message.access_token,
  //             user:{
  //                 name:response.data.message.userDetails.fullName,
  //                 email:response.data.message.userDetails.email,
  //                 phone: response.data.message.userDetails.phone,
  //                 // country: response.data.message.userDetails.country,
  //                 country: "IN",
  //                 ref: response.data.message.userDetails.ref,
  //             },
  //             company:{
  //                 // name:response.data.message.company.name,
  //                 // id:response.data.message.company.id,
  //                 // description: response.data.message.company.description,
  //                 // address: response.data.message.company.address,
  //                 // website: response.data.message.company.website,
  //                 // country: response.data.message.company.country,
  //                 // companyTurnover: response.data.message.company.companyTurnover,
  //                 name:'testing',
  //                 id:"-1",
  //                 description: "",
  //                 address: "",
  //                 website: "",
  //                 country: "IN",
  //                 companyTurnover: "",
  //             }
  //         }

  //         setUserDetails(currentUser)
  //         console.log('logged in using local');
  //       })
  //       .catch(function (error) {
  //         console.log('error getting logged in from local');
  //       });

  //     return;
  //   }
  //   }
  handleLogin();
};

export default Private;
