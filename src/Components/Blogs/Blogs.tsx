import React, { Dispatch, useEffect, useState } from "react";
import { SKIP } from "../constants";
import "./style.css";
import BlogsList from "./BlogsList";
import article_icon from "../../assests/images/article/article_icon.png";
import { useDispatch, useSelector } from "react-redux";
import { getSector } from "../../store/actioncreators/sectoractions";
import { getSubSector } from "../../store/actioncreators/sub-sectoractions";

const Blogs = () => {
  const sectorlist: SECTOR = useSelector((state: STATE) => state.SECTOR.SECTOR);
  const subsectorlist: SUB_SECTOR = useSelector(
    (state: STATE) => state.SUB_SECTOR.SUB_SECTOR
  );
  const dispatch: Dispatch<any> = useDispatch();
  const [page, setPage] = useState({
    skip: SKIP,
    limit: "0",
  });

  const [sector, setSector] = useState({
    drop: false,
    selected: "",
    id: "",
  });

  useEffect(() => {
    dispatch(getSector());
    dispatch(getSubSector());
    setSector({
      ...sector,
      selected: "All",
      id: "",
    });
  }, []);
  const fetchData = (value: number) => {
    let final = +page.skip + value < 0 ? +page.skip : +page.skip + value;
    setPage({ skip: final.toString(), limit: page.limit });
  };

  return (
    <div className="flex flex-col relative justify-center py-10 items-center w-full">
      <div className="flex flex-col items-center max-w-[1000px] w-full p-10 ">
        <h1 className="text-4xl font-roboto flex flex-row items-center mb-4">
          <img src={article_icon} alt="Blogs" className="w-8 mr-2" />
          Blogs
        </h1>
        <p>
          This blog is focussed on connecting new and innovation technologies,
          that solve global problems, societal challenges and address the
          UNSDGs, to markets to accelerate tech access and deployment globally.
          Global Business Inroads(GBI ) has been facilitating tech access and
          deployment for energy, environment, climate change, health,
          agritech/biotech areas for over a decade and we are now keen to scale
          our actions. One of the ways is to educate tech adopters/markets about
          technologies and tech innovators about markets/ tech deployment
          ecosystem. There is no lack of technologies and markets/needs, but the
          issue lies in between – how do we get the technologies to the markets,
          adapt and develop the appropriate business models, scale demand to
          reduce costs, localise and so on and so forth. Through our solidified
          commitment for since its inception in (2009), GBI has assisted over
          200+ firms in various capacities including viability assessments for
          market access, partnership analysis, technology uptake facilitation
          and market analysis. While we have focussed on action, we now plan to
          add education and capacity building via this blog and online platform
          Global Technology Interface® . Let’s share and learn about what’s out
          there, what can we use, how can we drive change. Watch out for the
          technologies and companies we feature, the markets and how they are
          growing, challenges and opportunities for technology deployment. The
          upcoming blog of this series will give the readers an insight on the
          prevailing solid waste management scenario in India and subsequent
          Indian market opportunities
        </p>
      </div>
      <div
        className="flex w-full justify-between  mx-10 py-5"
        onMouseLeave={() => {
          setSector({ ...sector, drop: false });
        }}
      >
        {/* <div className="flex flex-row justify-end duration-200 z-10 px-5" >
          <button
            id="dropdownDefault"
            data-dropdown-toggle="dropdown"
            className="w-fit text-white border-2 bg-GTI-BLUE-default focus:outline-none font-medium font-roboto rounded-lg m-1 text-sm px-4 py-2.5  text-center inline-flex items-center border-slate-300 justify-center flex-shrink"
            type="button"
            onClick={() => {
              setSector({ ...sector, drop: !sector.drop });
            }}
          >
            {sectorlist.SECTOR_LIST && sector.selected}
            <svg
              className="ml-2 w-4 h-4"
              aria-hidden="true"
              fill="grey"
              stroke="grey"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </button>
          <div
            id="dropdown"
            className={
              "relative z-10 rounded " + (!sector.drop ? "hidden" : "")
            }
            data-popper-placement="bottom"
          >
            <ul
              className="absolute flex flex-col  -left-20  top-14 text-sm font-roboto bg-slate-100"
              aria-labelledby="dropdownDefault"
            >
              <li
                className="block z-10 py-2 px-4 rounded  text-GTI-BLUE-default  whitespace-nowrap hover:text-slate-500 "
                onClick={() => {
                  setSector({ id: '', drop: false, selected: 'All' });
                }}
              >
                All
              </li>
              {
                sectorlist && sectorlist.SECTOR_LIST.map((item: sectorItem, id) => {
                  return (
                    <li
                      className="block z-10 py-2 px-4 rounded  text-GTI-BLUE-default  whitespace-nowrap hover:text-slate-500 "
                      onClick={() => {
                        setSector({ id: item._id, drop: false, selected: item.name });
                      }}
                    >
                      {item.name}
                    </li>
                  )
                })
              }
            </ul>
          </div>
        </div> */}
      </div>
      <BlogsList skip={page.skip} limit={page.limit} secId={sector.id} />
      {/* <div className="flex mb-5 py-5">
        <button
          disabled={page.skip === "0"}
          onClick={() => {
            fetchData(-1);
          }}
          className="inline-flex items-center py-2 px-4 text-sm font-medium text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
        >
          <svg
            aria-hidden="true"
            className="mr-2 w-5 h-5"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z"
              clipRule="evenodd"
            ></path>
          </svg>
          Previous
        </button>
        <button
          disabled
          className="inline-flex items-center mx-2 py-2 px-4 text-sm font-medium text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
        >
          {+page.skip + 1}
        </button>
        <button
          onClick={() => {
            fetchData(1);
          }}
          className="inline-flex items-center py-2 px-4 text-sm font-medium text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
        >
          Next
          <svg
            aria-hidden="true"
            className="ml-2 w-5 h-5"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
              clipRule="evenodd"
            ></path>
          </svg>
        </button>
      </div> */}
    </div>
  );
};
export default Blogs;
