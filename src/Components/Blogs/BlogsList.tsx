import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import articlebanner from "../../assests/banners/articlebanner.png";
import ReactPlayer from "react-player";
import { blogsItemFetched, NONE } from "../constants";
import { useNavigate } from "react-router-dom";
import { getBlogs } from "../../store/actioncreators/blogactions";
import { getQueryParams } from "../../utils";

export const Card = ({
  item,
  secId,
}: {
  item: blogsItemFetched;
  secId: string;
}) => {
  const DOC = new Date(item.createdAt);
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();

  const handleView = () => {
    navigate(`/blogs/${item._id}`, { state: { id: item._id } });
  };
  return (
    <div
      className="w-full max-w-[350px] overflow-hidden rounded-lg bg-white shadow h-full max-h-[320px]"
      onClick={handleView}
    >
      {!item.youtubeLink || item.youtubeLink === "none" ? (
        <img
          src={item?.imageUrl ?? ""}
          className="aspect-video w-full object-cover"
          alt={item.topic}
        />
      ) : (
        <ReactPlayer
          url={item.youtubeLink === NONE ? articlebanner : item.youtubeLink}
          className="flex rounded w-full justify-center"
          height="200px"
          width="auto"
          alt="Youtube video"
          light
        />
      )}
      <div className="p-4">
        <p className="mb-1 text-sm text-primary-500">
          {/* {item.topic} •{" "} */}
          <time>
            {DOC.toLocaleString("default", {
              month: "short",
              day: "2-digit",
              year: "numeric",
            })}
          </time>
        </p>
        <h3 className="text-xl font-medium text-gray-900 truncate">
          {item.topic}
        </h3>
        <p className="mt-1 text-gray-500 truncate">{item.shortDescription}</p>
      </div>
    </div>
  );
};

const BlogsList = ({
  skip: parentSkip,
  limit: parentLimit,
  secId,
}: {
  skip: string;
  limit: string;
  secId: string;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const blogs: BLOG = useSelector((state: STATE) => state.BLOG.BLOG);

  const navigate = useNavigate();
  const skip = getQueryParams("skip") ?? "0";
  const [page, setPage] = useState({
    skip: "0",
    limit: "8",
  });
  const fetchData = (val: number) => {
    let newSkip = parseInt(page.skip) + val;
    if (newSkip >= 0) {
      navigate(`/articles?skip=${newSkip}`);
      setPage({
        skip: newSkip.toString(),
        limit: page.limit,
      });
      dispatch(getBlogs(secId, skip, page.limit));
    }
  };
  useEffect(() => {
    dispatch(getBlogs(secId, skip, page.limit));
  }, [skip, secId]);
  return (
    <div className="flex w-full flex-col relative justify-center items-center">
      <div className="w-full flex flex-row flex-wrap gap-5 p-4 items-center justify-center">
        {blogs.BLOG_LIST.blogs.map((item: blogsItemFetched, id: number) => {
          return <Card item={item} key={id} secId={secId} />;
        })}
      </div>
      <div className="flex justify-center mb-5 py-5">
        {/* {!loadMore && (
          <button
            onClick={() => {
              setMore(true);
            }}
            className=" items-center py-2 px-4 text-sm font-medium text-GTI-BLUE-default bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
          >
            Load More
          </button>
        )} */}
        <div className="flex mb-5 py-5">
          <button
            disabled={page.skip === "0"}
            onClick={() => {
              fetchData(-1);
            }}
            className="inline-flex items-center py-2 px-4 text-sm font-medium text-GTI-BLUE-default disabled:text-gray-500 bg-white  rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
          >
            <svg
              aria-hidden="true"
              className="mr-2 w-5 h-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            Previous
          </button>
          <button
            disabled
            className="inline-flex items-center mx-2 py-2 px-4 text-sm font-medium text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
          >
            {parseInt(page.skip) + 1}
          </button>
          <button
            disabled={
              (parseInt(page.skip) + 1) * parseInt(page.limit) >=
              blogs.BLOG_LIST.blogsCount
            }
            onClick={() => {
              fetchData(1);
            }}
            className="inline-flex items-center py-2 px-4 text-sm font-medium text-GTI-BLUE-default disabled:text-gray-500 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 "
          >
            Next
            <svg
              aria-hidden="true"
              className="ml-2 w-5 h-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};
export default BlogsList;
