import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { BLOG, blogsItemFetched, NONE } from "../constants";
import articlebanner from "../../assests/banners/articlebanner.png";
import ReactPlayer from "react-player";
import axios from "axios";
import { store } from "../../store";
import SocialShare from "../Shareable/SocialShare";
import { getBlogs } from "../../store/actioncreators/blogactions";

export const Card = ({ item }: { item: blogsItemFetched }) => {
  const DOC = new Date(item.createdAt);
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();

  const handleView = () => {
    window.scrollTo(0, 0);
    navigate(`/blogs/${item._id}`, { state: { id: item._id } });
  };
  return (
    <div
      className="w-full cursor-pointer max-w-[350px] overflow-hidden rounded-lg bg-white shadow h-full max-h-[320px]"
      onClick={handleView}
    >
      {!item.youtubeLink || item.youtubeLink === "none" ? (
        <img
          src={item?.imageUrl ?? ""}
          className="aspect-video w-full object-cover"
          alt={item.topic}
        />
      ) : (
        <ReactPlayer
          url={item.youtubeLink === NONE ? articlebanner : item.youtubeLink}
          className="flex rounded w-full justify-center"
          height="200px"
          width="auto"
          alt={item.topic}
          light
        />
      )}
      <div className="p-4">
        <p className="mb-1 text-sm text-primary-500">
          {/* {item.topic} •{" "} */}
          <time>
            {DOC.toLocaleString("default", {
              month: "short",
              day: "2-digit",
              year: "numeric",
            })}
          </time>
        </p>
        <h3 className="text-xl font-medium text-gray-900 truncate">
          {item.topic}
        </h3>
        <p className="mt-1 text-gray-500 truncate">{item.shortDescription}</p>
      </div>
    </div>
  );
};

const Blog = () => {
  let [blog, setBlog] = useState<blogsItemFetched>({
    _id: "",
    topic: "",
    description: "",
    shortDescription: "",
    displayOnHomePage: false,
    isDeleted: false,
    sectorId: "",
    subSectorId: "",
    blogsType: "",
    imageUrl: "",
    externalLink: "",
    youtubeLink: "",
    webinarId: "",
    createdAt: "",
    __v: -1,
  });
  const blogs: BLOG = useSelector((state: STATE) => state.BLOG.BLOG);
  const dispatch: Dispatch<any> = useDispatch();
  const { id } = useParams();
  const state = {
    id: id ?? "",
  };
  useEffect(() => {
    dispatch(getBlogs(blog.sectorId, "0", "4"));
  }, []);
  const loadBlog = (id: string) => {
    var config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/blog/${id}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setBlog(response.data);
        // console.log("blog id fetched ");
      })
      .catch(function (error) {
        // console.log("blog id not fetched ", error);
      });
  };
  useEffect(() => {
    id && loadBlog(id);
  }, []);
  const DOC = new Date(blog.createdAt);

  return (
    <React.Fragment>
      <div className="flex flex-col mx-auto w-full space-y-5 h-full items-center ">
        <div className=" flex flex-col w-2/3 space-y-5 items-start relative">
          <SocialShare
            url={`${process.env.REACT_APP_BASE_API}/article/${id}`}
            topic={blog.topic}
          />
          <h1 className="text-3xl w-full py-10 font-roboto text-left font-bold">
            {blog.topic}
          </h1>
          <div className="flex flex-col w-full h-1/2 rounded  shadow-lg shadow-slate-500 justify-items-start">
            {!blog.youtubeLink || blog.youtubeLink === "none" ? (
              <img
                src={blog?.imageUrl ?? ""}
                className="aspect-video w-full object-cover max-h-[400px]"
                alt={blog.topic}
              />
            ) : (
              <ReactPlayer
                url={
                  blog.youtubeLink === NONE ? articlebanner : blog.youtubeLink
                }
                className="flex w-full  object-cover rounded"
                width="auto"
                height="20em"
                alt={blog.topic}
                light
              />
            )}
          </div>
          <div className="flex w-full flex-row-end text-gray-500 text-xs">
            <h4>
              {DOC.toLocaleString("default", {
                month: "short",
                day: "2-digit",
                year: "numeric",
              })}
            </h4>
            <h4>{"| 2 min read"}</h4>
          </div>
          <div dangerouslySetInnerHTML={{ __html: blog.description }}>
            {/* {ReactHtmlParser(blog.description)} */}
          </div>
        </div>
        <div className="flex flex-col h-full  pb-32 w-5/6">
          <h3 className="font-roboto font-bold text-2xl">Related Blogs</h3>
          <div className="grid grid-cols-3 ">
            {blogs.BLOG_LIST.blogs?.length ? (
              blogs.BLOG_LIST.blogs
                .filter((blogs) => blogs._id !== id)
                .slice(0, 3)
                .map((item, id) => {
                  return <Card item={item} key={id} />;
                })
            ) : (
              <h4 className="font-semibold text-lg">No related blogs</h4>
            )}
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};
export default Blog;
