import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

interface WebsiteDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSignIn: () => void;
}

const WebsiteDetailsModal: React.FC<WebsiteDetailsModalProps> = ({
  isOpen,
  onClose,
  onSignIn,
}) => {
  const navigate = useNavigate();
  const [isAnimating, setIsAnimating] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  // Handle animation states
  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      // Small delay to ensure DOM is ready, then start animation
      setTimeout(() => setIsAnimating(true), 10);
    } else {
      setIsAnimating(false);
      // Wait for animation to complete before removing from DOM
      setTimeout(() => setShouldRender(false), 300);
    }
  }, [isOpen]);

  if (!shouldRender) return null;

  return (
    <div
      className={`fixed inset-0 bg-black flex items-center justify-center z-50 transition-all duration-300 ease-out ${
        isAnimating ? "bg-opacity-50" : "bg-opacity-0"
      }`}
    >
      <div
        className={`bg-white rounded-2xl p-8 max-w-4xl w-full mx-4 h-[80vh] relative flex flex-col transition-all duration-300 ease-out transform ${
          isAnimating
            ? "scale-100 opacity-100 translate-y-0"
            : "scale-95 opacity-0 translate-y-4"
        }`}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className={`absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-2xl transition-all duration-300 ease-out transform hover:scale-110 hover:rotate-90 ${
            isAnimating
              ? "opacity-100 translate-x-0"
              : "opacity-0 translate-x-2"
          }`}
          style={{ transitionDelay: isAnimating ? "50ms" : "0ms" }}
          aria-label="Close modal"
        >
          ×
        </button>

        {/* Modal Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div
            className={`text-center mb-6 transition-all duration-500 ease-out transform ${
              isAnimating
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-4"
            }`}
            style={{ transitionDelay: isAnimating ? "100ms" : "0ms" }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome to Global Technology Interface
            </h2>
            <p className="text-gray-600">
              Your Gateway to Innovation and Technology
            </p>
          </div>

          {/* Features Grid */}
          <div className="flex-1 grid grid-cols-2 gap-4 mb-4">
            <div
              className={`bg-blue-50 p-4 rounded-xl transition-all duration-500 ease-out transform hover:scale-105 ${
                isAnimating
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-6"
              }`}
              style={{ transitionDelay: isAnimating ? "200ms" : "0ms" }}
            >
              <div className="text-blue-600 text-xl mb-2">🌐</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                Global Network
              </h3>
              <p className="text-sm text-gray-600">
                Connect with innovators and technology leaders worldwide.
              </p>
            </div>

            <div
              className={`bg-green-50 p-4 rounded-xl transition-all duration-500 ease-out transform hover:scale-105 ${
                isAnimating
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-6"
              }`}
              style={{ transitionDelay: isAnimating ? "300ms" : "0ms" }}
            >
              <div className="text-green-600 text-xl mb-2">🚀</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                Innovation Hub
              </h3>
              <p className="text-sm text-gray-600">
                Discover cutting-edge technologies and breakthrough innovations.
              </p>
            </div>

            <div
              className={`bg-purple-50 p-4 rounded-xl transition-all duration-500 ease-out transform hover:scale-105 ${
                isAnimating
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-6"
              }`}
              style={{ transitionDelay: isAnimating ? "400ms" : "0ms" }}
            >
              <div className="text-purple-600 text-xl mb-2">🤝</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                Partnership Opportunities
              </h3>
              <p className="text-sm text-gray-600">
                Find meaningful partnerships and collaboration opportunities.
              </p>
            </div>

            <div
              className={`bg-orange-50 p-4 rounded-xl transition-all duration-500 ease-out transform hover:scale-105 ${
                isAnimating
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-6"
              }`}
              style={{ transitionDelay: isAnimating ? "500ms" : "0ms" }}
            >
              <div className="text-orange-600 text-xl mb-2">📈</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                Market Access
              </h3>
              <p className="text-sm text-gray-600">
                Access global markets and showcase your technologies.
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div
            className={`text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-xl transition-all duration-500 ease-out transform ${
              isAnimating
                ? "opacity-100 translate-y-0 scale-100"
                : "opacity-0 translate-y-4 scale-95"
            }`}
            style={{ transitionDelay: isAnimating ? "600ms" : "0ms" }}
          >
            <h3 className="text-lg font-semibold mb-2">
              Ready to Get Started?
            </h3>
            <p className="text-sm mb-3">
              Join thousands of innovators already part of our platform
            </p>
            <div className="flex justify-center space-x-3">
              <button
                onClick={() => {
                  onClose();
                  onSignIn();
                }}
                className="bg-white text-blue-600 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-100 transition-all duration-200 transform hover:scale-105"
              >
                Sign In
              </button>
              <button
                onClick={() => {
                  onClose();
                  navigate("/signup");
                }}
                className="bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-800 transition-all duration-200 transform hover:scale-105"
              >
                Get Started
              </button>
            </div>
          </div>

          {/* Platform Stats */}
          <div
            className={`grid grid-cols-3 gap-3 text-center mt-4 transition-all duration-500 ease-out transform ${
              isAnimating
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-4"
            }`}
            style={{ transitionDelay: isAnimating ? "700ms" : "0ms" }}
          >
            <div className="transition-all duration-300 hover:scale-110">
              <div className="text-xl font-bold text-blue-600">10K+</div>
              <div className="text-xs text-gray-600">Technologies</div>
            </div>
            <div className="transition-all duration-300 hover:scale-110">
              <div className="text-xl font-bold text-green-600">5K+</div>
              <div className="text-xs text-gray-600">Opportunities</div>
            </div>
            <div className="transition-all duration-300 hover:scale-110">
              <div className="text-xl font-bold text-purple-600">50+</div>
              <div className="text-xs text-gray-600">Countries</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WebsiteDetailsModal;
