import React, { useState, useEffect } from "react";

interface SponsorModalProps {
  isOpen: boolean;
  onClose: () => void;
  sponsors: Array<{
    _id: string;
    name: string;
    image: string;
  }>;
}

const SponsorModal: React.FC<SponsorModalProps> = ({
  isOpen,
  onClose,
  sponsors,
}) => {
  const [currentSponsorIndex, setCurrentSponsorIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Auto-rotate sponsors every 3 seconds
  useEffect(() => {
    if (!isOpen || sponsors.length <= 1) return;

    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentSponsorIndex((prev) => (prev + 1) % sponsors.length);
        setIsAnimating(false);
      }, 300);
    }, 3000);

    return () => clearInterval(interval);
  }, [isOpen, sponsors.length]);

  if (!isOpen || sponsors.length === 0) return null;

  const currentSponsor = sponsors[currentSponsorIndex];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
      <div className="bg-white rounded-3xl p-8 max-w-md mx-4 relative overflow-hidden">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl z-10 transition-colors duration-300"
          aria-label="Close modal"
        >
          ×
        </button>

        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600"></div>
        </div>

        {/* Modal Content */}
        <div className="relative z-10 text-center space-y-6">
          {/* Header */}
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-gray-900">
              Trusted by Industry Leaders
            </h2>
            <p className="text-gray-600">
              Join thousands of companies who trust our platform
            </p>
          </div>

          {/* Sponsor Display */}
          <div className="py-8">
            <div
              className={`transition-all duration-300 transform ${
                isAnimating
                  ? "scale-95 opacity-50"
                  : "scale-100 opacity-100"
              }`}
            >
              {/* Sponsor Logo */}
              <div className="mb-6">
                <div className="w-32 h-32 mx-auto bg-white rounded-2xl shadow-lg flex items-center justify-center p-4 border border-gray-100">
                  <img
                    src={currentSponsor.image}
                    alt={currentSponsor.name}
                    className="max-w-full max-h-full object-contain filter grayscale hover:grayscale-0 transition-all duration-500"
                  />
                </div>
              </div>

              {/* Sponsor Name */}
              <div className="space-y-2">
                <p className="text-lg font-semibold text-gray-900">
                  Trusted by
                </p>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {currentSponsor.name}
                </h3>
              </div>
            </div>
          </div>

          {/* Progress Indicators */}
          {sponsors.length > 1 && (
            <div className="flex justify-center space-x-2">
              {sponsors.map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setIsAnimating(true);
                    setTimeout(() => {
                      setCurrentSponsorIndex(index);
                      setIsAnimating(false);
                    }, 300);
                  }}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    index === currentSponsorIndex
                      ? "bg-blue-600 w-6"
                      : "bg-gray-300 hover:bg-gray-400"
                  }`}
                />
              ))}
            </div>
          )}

          {/* Call to Action */}
          <div className="pt-4">
            <p className="text-sm text-gray-600 mb-4">
              Ready to join our trusted partners?
            </p>
            <button
              onClick={onClose}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Explore Platform
            </button>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-10 animate-pulse"></div>
        <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full opacity-10 animate-pulse delay-1000"></div>
      </div>
    </div>
  );
};

export default SponsorModal;
