import React from "react";
import { PROFILE_TYPES } from "../../shared/enum";
import {
  RocketLaunchIcon,
  MagnifyingGlassIcon,
  NewspaperIcon,
  StarIcon,
  CheckIcon,
  ArrowRightIcon,
} from "@heroicons/react/24/outline";

interface UserTypeSelectionProps {
  profileType: PROFILE_TYPES | null;
  handleProfileTypes: (type: PROFILE_TYPES) => void;
  onNext: () => void;
}

const UserTypeSelection: React.FC<UserTypeSelectionProps> = ({
  profileType,
  handleProfileTypes,
  onNext,
}) => {
  const userTypes = [
    {
      type: PROFILE_TYPES.DISPLAYER,
      title: "Technology Displayer",
      subtitle: "Showcase your innovations",
      description:
        "Perfect for companies, startups, and innovators looking to showcase their technologies, products, or services to a global audience of potential investors, partners, and customers.",
      icon: RocketLaunchIcon,
      features: [
        "Upload and showcase technologies",
        "Connect with investors and partners",
        "Access global market opportunities",
        "Build your innovation portfolio",
      ],
      color: "from-emerald-500 to-emerald-600",
      bgColor: "bg-emerald-50",
      iconColor: "text-emerald-600",
    },
    {
      type: PROFILE_TYPES.SCOUTER,
      title: "Technology Scouter",
      subtitle: "Find innovative solutions",
      description:
        "Ideal for organizations, NGOs, government institutions, and corporates seeking innovative technologies and solutions to address specific business challenges and requirements.",
      icon: MagnifyingGlassIcon,
      features: [
        "Post technology requirements",
        "Discover innovative solutions",
        "Connect with solution providers",
        "Access curated technology database",
      ],
      color: "from-orange-500 to-orange-600",
      bgColor: "bg-orange-50",
      iconColor: "text-orange-600",
    },
    {
      type: PROFILE_TYPES.GENERAL_SUBSCRIBER,
      title: "General Subscriber",
      subtitle: "Stay informed and connected",
      description:
        "Great for individuals, researchers, and professionals who want to stay updated with the latest technological innovations and industry trends without active participation.",
      icon: NewspaperIcon,
      features: [
        "Access latest technology news",
        "Browse innovation showcase",
        "Receive industry insights",
        "Network with professionals",
      ],
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      iconColor: "text-green-600",
    },
  ];

  const handleTypeSelect = (type: PROFILE_TYPES) => {
    handleProfileTypes(type);
  };

  const handleContinue = () => {
    if (profileType) {
      onNext();
    }
  };

  return (
    <div className="user-type-selection">
      <div className="selection-header">
        <div className="header-icon">
          <StarIcon />
        </div>
        <h1 className="selection-title">Choose Your Path</h1>
        <p className="selection-subtitle">
          Select the option that best describes your role and goals on the GTI
          platform
        </p>
      </div>

      <div className="user-type-grid">
        {userTypes.map((userType) => {
          const IconComponent = userType.icon;
          return (
            <div
              key={userType.type}
              className={`user-type-card ${
                profileType === userType.type ? "selected" : ""
              }`}
              onClick={() => handleTypeSelect(userType.type)}
            >
              <div
                className={`card-gradient bg-gradient-to-br ${userType.color}`}
              >
                <IconComponent className="card-icon text-white" />
              </div>

              <div className="card-content">
                <h3 className="card-title">{userType.title}</h3>
                <p className="card-subtitle">{userType.subtitle}</p>
                <p className="card-description">{userType.description}</p>

                <div className="card-features">
                  {userType.features.map((feature, index) => (
                    <div key={index} className="feature-item">
                      <CheckIcon />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="card-selector">
                <div
                  className={`selector-circle ${
                    profileType === userType.type ? "selected" : ""
                  }`}
                >
                  {profileType === userType.type && <CheckIcon />}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="selection-footer">
        <button
          onClick={handleContinue}
          disabled={!profileType}
          className="continue-button"
        >
          <span>Continue</span>
          <ArrowRightIcon />
        </button>

        {!profileType && (
          <p className="selection-hint">
            Please select a user type to continue
          </p>
        )}
      </div>
    </div>
  );
};

export default UserTypeSelection;
