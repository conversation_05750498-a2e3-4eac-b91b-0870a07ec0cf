// import React, { useRef, useState } from "react";
// import ReactFlagsSelect from "react-flags-select";
// import { Dispatch } from "redux";
// import { useSelector, useDispatch } from "react-redux";
// import { I<PERSON><PERSON><PERSON> } from "react-icons/im";
// import axios from "axios";
// import { redirect } from "react-router-dom";
// import { useFormik } from "formik";
// import { AiFillEye, AiFillEyeInvisible } from "react-icons/ai";
// import { MdArrowRightAlt } from "react-icons/md";
// import { Helmet } from "react-helmet";
// import { CSSTransition, TransitionGroup } from "react-transition-group";

// import companyDetails from "../../assests/images/signup/company-details.png";
// import displayer from "../../assests/images/signup/displayer.png";
// import generalSubscriber from "../../assests/images/signup/general-subscriber.png";
// import scouter from "../../assests/images/signup/scouter.png";
// import upload_logo_baner from "../../assests/banners/upload_logo.png";
// import * as ROUTE from "../Constants/routes";
// import {
//   companySignupSchema,
//   companySignupSchema2,
//   personalSignupSchema,
// } from "../validations/signupValidations";
// import {
//   CONTENT_TYPE,
//   CONTENT_TYPE_DOC,
//   DOCUMENT_TYPE,
//   FILE_PATH,
//   FILE_TYPE,
//   metaData,
//   presignedData,
//   title,
// } from "../constants";
// import { Spinner } from "../utils/loader";
// import {
//   spinnerLoaderStart,
//   spinnerLoaderStop,
// } from "../../store/actioncreators/loaderactions";
// import { formatUserType, isSuccess, notify } from "../../utils";
// import { createUser } from "../../api/user";
// import {
//   countryList,
//   displayerMessage,
//   generalSubscriberMessage,
//   scouterMessage,
// } from "../../shared/constants";
// import { PROFILE_TYPES } from "../../shared/enum";
// import {
//   Document,
//   FormValues,
//   FormValues2,
//   PersonalFormValues,
// } from "../../shared/interface";
// import SuccessModal from "./SuccessModal";
// import ErrorModal from "./ErrorModal";
// import { RequestMethods } from "../../shared/RequestMethods";
// import globe from "../../assests/home/<USER>";
// import "./style.css";
// import TechnologyDetails from "./TechnologyDetails";

// const Signup = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
//   const [signUpImage, setSignUpImage] = useState(generalSubscriber);
//   const [signUpMessage, setSignUpMessage] = useState(generalSubscriberMessage);
//   const Error_modal = document.getElementById("error-modal");
//   let [successModal, setSuccessModal] = useState<boolean>(false);
//   let [errorModal, setErrorModal] = useState({
//     show: false,
//     message: "",
//   });
//   let handleSuccessModal = () => {
//     setSuccessModal(!successModal);
//   };
//   let handleErrorModal = (msg: string) => {
//     setErrorModal({ show: !errorModal.show, message: msg });
//   };
//   const [showPassword, setShowPassword] = useState(false);
//   const dispatch: Dispatch<any> = useDispatch();
//   const loader: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);
//   let [country, setCountry] = useState("IN");

//   const [step, setStep] = useState(0);
//   const [formData, setFormData] = useState({
//     name: "",
//     email: "",
//     password: "",
//   });

//   const handleNext = () => {
//     setStep((prev) => prev + 1);
//   };

//   const handlePrev = () => {
//     setStep((prev) => prev - 1);
//   };

//   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setFormData({
//       ...formData,
//       [e.target.name]: e.target.value,
//     });
//   };

//   const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
//     e.preventDefault();
//     console.log(formData);
//   };

//   const steps = [
//     {
//       label: "Name",
//       // input: <TechnologyDetails />,
//     },
//     {
//       label: "Email",
//       input: (
//         <input
//           type="email"
//           name="email"
//           value={formData.email}
//           onChange={handleChange}
//           placeholder="Enter your email"
//           className="p-2 border border-gray-300 rounded"
//         />
//       ),
//     },
//     {
//       label: "Password",
//       input: (
//         <input
//           type="password"
//           name="password"
//           value={formData.password}
//           onChange={handleChange}
//           placeholder="Enter your password"
//           className="p-2 border border-gray-300 rounded"
//         />
//       ),
//     },
//   ];

//   let [uploadLogo, setUploadLogoM] = useState(false);
//   const [comp_logo, setCompLogo] = useState<FormValues["logo"]>(null);
//   const [comp_documents, setCompDocs] = useState<FormValues["documents"]>([]);
//   const [profileType, setProfileType] = useState(PROFILE_TYPES.DISPLAYER);
//   const [comp_documents_start, setCompDocsStart] = useState<
//     FormValues["s_documents"]
//   >({ document: null, documentType: DOCUMENT_TYPE.OPEN });

//   const formik = useFormik<FormValues>({
//     initialValues: {
//       name: "",
//       description: "",
//       address: "",
//       website: "",
//       country: "",
//       turnover: "",
//       logo: null,
//       documents: [],
//       s_documents: { document: null, documentType: DOCUMENT_TYPE.OPEN },
//     },
//     validationSchema: companySignupSchema,
//     onSubmit: (values, formikHelpers) => {
//       handleCompanySubmit(values);
//     },
//   });

//   const personal_formik = useFormik<PersonalFormValues>({
//     initialValues: {
//       name: "",
//       email: "",
//       password: "",
//       country: "",
//       phone: "",
//       ref: "",
//     },
//     validationSchema: personalSignupSchema,
//     onSubmit: (values, formikHelpers) => {
//       const isCompany = profileType !== PROFILE_TYPES.GENERAL_SUBSCRIBER;
//       !isCompany ? handleGeneralProfile(values) : handleCompanySubmitWithId();
//     },
//   });
//   const formik2 = useFormik<FormValues2>({
//     initialValues: {
//       comp_id: "",
//     },
//     validationSchema: companySignupSchema2,
//     onSubmit: (values, formikHelpers) => {
//       handleCompanySubmitWithId();
//     },
//   });

//   const handleGeneralProfile = async (values: PersonalFormValues) => {
//     dispatch(spinnerLoaderStart());
//     const data = {
//       userType: profileType,
//       fullName: values.name,
//       email: values.email,
//       countryCode: country,
//       phoneNumber: values.phone.toString(),
//       referenceCode: values.ref,
//       password: values.password,
//     };
//     const res = await createUser(data);
//     dispatch(spinnerLoaderStop());
//     if (isSuccess(res)) {
//       handleSuccessModal();
//       notify("Registered successfully!", "success");
//     } else {
//       notify("Registration failed!", "error");
//     }
//   };

//   let toggle_error = () => {
//     if (Error_modal?.hasAttribute("hidden")) {
//       Error_modal?.classList.remove("hidden");
//       Error_modal?.classList.add("container");
//     } else {
//       Error_modal?.classList.remove("container");
//       Error_modal?.classList.add("hidden");
//     }
//   };

//   const handleCompLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setCompLogo(e.target.files?.[0] || null);
//     formik.setFieldValue("logo", e.target.files![0]);
//   };

//   const handleRemoveCompLogo = () => {
//     setCompLogo(null);
//     formik.setFieldValue("logo", null);
//   };

//   let handleCompanySubmitWithId = () => {
//     dispatch(spinnerLoaderStart());
//     let data = JSON.stringify({
//       userType: profileType,
//       fullName: personal_formik.values.name,
//       email: personal_formik.values.email,
//       countryCode: country,
//       phoneNumber: personal_formik.values.phone.toString(),
//       referenceCode: personal_formik.values.ref,
//       password: personal_formik.values.password,
//       companyId: formik2.values.comp_id,
//       companyDetails: {
//         name: null,
//         logo: null,
//         description: null,
//         address: null,
//         website: null,
//         country: null,
//         companyTurnover: null,
//       },
//       companyDocuments: [],
//     });
//     let config = {
//       method: RequestMethods.POST,
//       url: `${process.env.REACT_APP_BASE_API}/users`,
//       headers: {
//         "Content-Type": "application/json",
//       },
//       data,
//     };

//     axios(config)
//       .then(function (response) {
//         if (response.data.status === 200 || response.data.status === 201) {
//           redirect(ROUTE.HOME);
//           handleSuccessModal();
//           dispatch(spinnerLoaderStop());
//         } else {
//           handleErrorModal(response.data.message);
//           dispatch(spinnerLoaderStop());
//         }
//       })
//       .catch(function (error) {
//         handleErrorModal("Error in Submitting , Please try again");
//         dispatch(spinnerLoaderStop());
//       });
//   };

//   const getPresigned = async (content: presignedData) => {
//     const data = JSON.stringify(content);
//     let result: string = "";
//     const config = {
//       method: RequestMethods.POST,
//       url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
//       headers: {
//         "Content-Type": "application/json",
//       },
//       data: data,
//     };

//     await axios(config)
//       .then(function (response) {
//         result = response.data;
//       })
//       .catch(function (error) {
//         result = "error";
//       });

//     return result;
//   };

//   const postFile = async (signed: string) => {
//     var config = {
//       method: RequestMethods.PUT,
//       url: signed,
//       headers: {
//         "Content-Type": CONTENT_TYPE,
//         "Access-Control-Allow-Origin": true,
//       },
//       data: comp_logo,
//     };

//     await axios(config)
//       .then(function (response) {})
//       .catch(function (error) {
//         toggle_error();
//       });
//   };

//   const postDocument = async (signed: string, file: File | null) => {
//     var config = {
//       method: RequestMethods.PUT,
//       url: signed,
//       headers: {
//         "Content-Type": CONTENT_TYPE_DOC,
//         "Access-Control-Allow-Origin": true,
//       },
//       data: file,
//     };
//     if (file) {
//       await axios(config)
//         .then(function (response) {})
//         .catch(function (error) {
//           toggle_error();
//         });
//     }
//   };

//   let handleCompanySubmit = async (values: FormValues) => {
//     dispatch(spinnerLoaderStart());
//     let signedLogoURL: string = "";
//     let signedDocumentURLWhole: string = "";

//     const signedData: presignedData = {
//       fileName: values?.logo?.name || values.name,
//       filePath: FILE_PATH.COMPANY_LOGO,
//       fileType: FILE_TYPE.PNG,
//     };
//     signedLogoURL = await getPresigned(signedData);
//     postFile(signedLogoURL);
//     comp_documents.push(comp_documents_start);

//     const temp_docs: any[] = [];
//     if (comp_documents) {
//       Array.from(comp_documents).forEach(async (document: Document, i) => {
//         let signedDocumentData: presignedData = {
//           fileName: document.document?.name || values.name,
//           filePath: FILE_PATH.COMPANY_DOCS,
//           fileType: FILE_TYPE.PDF,
//         };
//         let tempurl = (await getPresigned(signedDocumentData)) + " ";
//         temp_docs.push({
//           document: tempurl,
//           documentType: document.documentType,
//         });
//         signedDocumentURLWhole = tempurl.split("?")[0] + " ";
//         postDocument(tempurl, document.document);
//       });
//     }

//     let data = JSON.stringify({
//       userType: profileType,
//       fullName: personal_formik.values.name,
//       email: personal_formik.values.email,
//       countryCode: country,
//       phoneNumber: personal_formik.values.phone.toString(),
//       referenceCode: personal_formik.values.ref,
//       password: personal_formik.values.password,
//       companyId: null,
//       companyDetails: {
//         name: values.name,
//         logo: signedLogoURL.split("?")[0],
//         description: values.description,
//         address: values.address,
//         website: values.website,
//         country: values.country,
//         companyTurnover: values.turnover,
//       },
//       companyDocuments: temp_docs,
//     });

//     let config = {
//       method: RequestMethods.POST,
//       url: `${process.env.REACT_APP_BASE_API}/users`,
//       headers: {
//         "Content-Type": "application/json",
//       },
//       data: data,
//     };

//     axios(config)
//       .then(function (response) {
//         if (response.data.status === 200 || response.data.status === 201) {
//           dispatch(spinnerLoaderStop());
//           handleSuccessModal();
//           formik.resetForm();
//           formik2.resetForm();
//         } else {
//           handleErrorModal(response.data.message);
//           dispatch(spinnerLoaderStop());
//         }
//       })
//       .catch(function (error) {
//         handleErrorModal("Error in Submitting , Please try again");
//         dispatch(spinnerLoaderStop());
//       });
//   };

//   let handleLogoUpload = (event: any) => {
//     setUploadLogoM(!uploadLogo);
//     setCompLogo(event.target.files[0]);
//   };

//   const companyRef = useRef<HTMLDivElement>(null);

//   const handleType = (key: any) => {
//     setProfileType(key);
//     if (key === PROFILE_TYPES.DISPLAYER) {
//       setSignUpImage(displayer);
//       setSignUpMessage(displayerMessage);
//     } else if (key === PROFILE_TYPES.SCOUTER) {
//       setSignUpImage(scouter);
//       setSignUpMessage(scouterMessage);
//     } else if (key === PROFILE_TYPES.GENERAL_SUBSCRIBER) {
//       setSignUpImage(generalSubscriber);
//       setSignUpMessage(generalSubscriberMessage);
//     }
//   };

//   return (
//     <div className="flex flex-col justify-center w-full items-center -z-10">
//       <Helmet>
//         <title>{title.SIGN_UP}</title>
//         <meta name="description" key="description" content={metaData.SIGN_UP} />
//         <meta name="title" key="title" content={title.SIGN_UP} />
//         <meta property="og:title" content={title.SIGN_UP} />
//         <meta property="og:description" content={metaData.SIGN_UP} />
//         <meta property="og:image" content={globe} />
//         <meta
//           property="og:url"
//           content={`${process.env.REACT_APP_BASE_URL}/signup`}
//         />
//         <meta property="og:type" content="website" />
//         <meta name="twitter:title" content={title?.SIGN_UP} />
//         <meta name="twitter:description" content={metaData?.SIGN_UP} />
//         <meta name="twitter:image" content={globe} />
//         <meta name="twitter:card" content={title?.SIGN_UP} />
//       </Helmet>

//       <div className="flex items-center justify-center min-h-screen">
//         <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg">
//           <TransitionGroup>
//             <CSSTransition key={step} timeout={300} classNames="fade">
//               {steps[step].input}
//             </CSSTransition>
//           </TransitionGroup>

//           <div className="flex justify-between mt-4">
//             {step > 0 && (
//               <button
//                 type="button"
//                 onClick={handlePrev}
//                 className="p-2 bg-gray-300 rounded-md"
//               >
//                 ← Previous
//               </button>
//             )}
//             {step < steps.length - 1 ? (
//               <button
//                 type="button"
//                 onClick={handleNext}
//                 className="p-2 bg-blue-500 text-white rounded-md"
//               >
//                 Next →
//               </button>
//             ) : (
//               <button
//                 type="submit"
//                 className="p-2 bg-green-500 text-white rounded-md"
//               >
//                 Submit
//               </button>
//             )}
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default Signup;

// {
//   <>
//     {/* {successModal && (
//       <SuccessModal
//         show={successModal}
//         state={state}
//         message={message}
//         toggle={handleSuccessModal}
//       />
//     )}
//     {errorModal.show && (
//       <ErrorModal modal={errorModal} toggle={handleErrorModal} />
//     )}

//     {profileType === PROFILE_TYPES.DISPLAYER && (
//       <TechnologyDetails
//         handleTechnologySubmit={handleUserRegistration}
//         handleSuccessModal={handleSuccessModal}
//         handleErrorModal={handleErrorModal}
//       />
//     )}

//     {profileType === PROFILE_TYPES.SCOUTER && (
//       <OpportunityDetails
//         handleTechnologySubmit={handleUserRegistration}
//         handleSuccessModal={handleSuccessModal}
//         handleErrorModal={handleErrorModal}
//       />
//     )} */}
//   </>;
// }

// // import React from "react";

// // const SignUpUpdated = () => {
// //   return <div></div>;
// // };

// // export default SignUpUpdated;

import React from "react";

const SignUpUpdated = () => {
  return <div></div>;
};

export default SignUpUpdated;
