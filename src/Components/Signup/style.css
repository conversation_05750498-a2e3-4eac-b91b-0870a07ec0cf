/* Modern SignIn Modal Styles */

/* Overlay */
.modern-signin-overlay {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Main Container */
.modern-signin-container,
.modern-forgot-container {
  @apply relative w-full max-w-md mx-auto;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Close Button */
.modern-close-button {
  @apply absolute top-4 right-4 z-10 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #64748b;
}

.modern-close-button:hover {
  @apply bg-red-50 text-red-600 border-red-200;
  transform: scale(1.05);
}

.modern-close-button svg {
  @apply w-5 h-5;
}

/* Header */
.modern-signin-header {
  @apply p-8 pb-4 text-center;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05),
    rgba(147, 51, 234, 0.05)
  );
}

.signin-logo-section {
  @apply space-y-4;
}

.signin-icon {
  @apply w-16 h-16 mx-auto rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.signin-icon.forgot-icon {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.signin-icon svg {
  @apply w-8 h-8;
}

.signin-title {
  @apply text-2xl font-bold text-gray-800;
  font-family: "Inter", sans-serif;
}

.signin-subtitle {
  @apply text-gray-600;
  font-family: "Inter", sans-serif;
}

/* Form */
.modern-signin-form {
  @apply p-8 pt-4 space-y-6;
}

/* Enhanced Input Groups */
.enhanced-input-group {
  @apply space-y-2;
  animation: slideInUp 0.4s ease-out;
}

.enhanced-input-group:nth-child(2) {
  animation-delay: 0.1s;
}

.enhanced-input-group:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-label {
  @apply block text-sm font-semibold text-gray-700 mb-2;
  font-family: "Inter", sans-serif;
}

.input-wrapper {
  @apply relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(248, 250, 252, 0.9)
  );
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  height: 56px;
  display: flex;
  align-items: center;
}

.input-wrapper:focus-within {
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1),
    0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.input-wrapper:hover:not(:focus-within) {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(255, 255, 255, 0.95);
}

.input-icon {
  @apply flex-shrink-0 w-5 h-5 text-gray-400 transition-all duration-300;
  margin-left: 16px;
  margin-right: 12px;
  pointer-events: none;
}

.input-wrapper:focus-within .input-icon {
  @apply text-blue-600;
  transform: scale(1.1);
}

.enhanced-input {
  @apply flex-1 bg-transparent border-none outline-none text-gray-900 placeholder-gray-400;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  padding: 0;
  height: 100%;
  line-height: 1.5;
}

.enhanced-input::placeholder {
  @apply text-gray-400;
  font-style: italic;
}

.password-toggle {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-all duration-300 rounded-lg hover:bg-gray-100;
  margin-right: 12px;
  margin-left: 8px;
}

.password-toggle svg {
  @apply w-5 h-5;
}

.input-helper {
  @apply mt-2;
}

.helper-text {
  @apply text-xs text-gray-500;
  font-family: "Inter", sans-serif;
}

/* Input Validation States */
.input-wrapper.success {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.input-wrapper.success .input-icon {
  @apply text-green-600;
}

.input-wrapper.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  animation: shake 0.5s ease-in-out;
}

.input-wrapper.error .input-icon {
  @apply text-red-600;
}

.helper-text.success {
  @apply text-green-600;
}

.helper-text.error {
  @apply text-red-600;
}

/* Enhanced Form Styling */
.modern-signin-form {
  @apply p-8 pt-4 space-y-8;
}

/* Better spacing for mobile */
@media (max-width: 640px) {
  .enhanced-input-group {
    @apply space-y-1;
  }

  .input-wrapper {
    border-radius: 12px;
    height: 52px;
  }

  .enhanced-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .input-icon {
    @apply w-4 h-4;
    margin-left: 12px;
    margin-right: 10px;
  }

  .password-toggle {
    @apply w-7 h-7;
    margin-right: 10px;
    margin-left: 6px;
  }
}

/* Forgot Password Section */
.forgot-password-section {
  @apply text-center;
}

.forgot-password-link {
  @apply text-sm text-blue-600 hover:text-blue-700 font-medium transition-all duration-300 hover:underline;
  font-family: "Inter", sans-serif;
}

.forgot-password-link:hover {
  transform: translateY(-1px);
}

/* Submit Button */
.modern-submit-button {
  @apply w-full py-4 px-6 rounded-xl font-semibold text-white transition-all duration-300 flex items-center justify-center space-x-2;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border: none;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  font-family: "Inter", sans-serif;
}

.modern-submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.modern-submit-button:actives:not(:disabled) {
  transform: translateY(0);
}

.modern-submit-button:disabled {
  @apply opacity-70 cursor-not-allowed;
  transform: none;
}

.modern-submit-button svg {
  @apply w-5 h-5;
}

/* Error Message */
.error-message {
  @apply flex items-center space-x-3 p-4 rounded-xl;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #dc2626;
}

.error-message svg {
  @apply w-5 h-5 flex-shrink-0;
}

.error-message span {
  @apply text-sm font-medium;
  font-family: "Inter", sans-serif;
}

/* Success Message */
.success-message {
  @apply flex items-center space-x-3 p-4 rounded-xl;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  color: #059669;
}

.success-message svg {
  @apply w-5 h-5 flex-shrink-0;
}

.success-message span {
  @apply text-sm font-medium;
  font-family: "Inter", sans-serif;
}

/* Sign Up Section */
.signup-section {
  @apply text-center pt-4 border-t border-gray-100;
}

.signup-text {
  @apply text-gray-600 text-sm;
  font-family: "Inter", sans-serif;
}

.signup-link {
  @apply ml-2 text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-300;
  font-family: "Inter", sans-serif;
}

.signup-link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 640px) {
  .modern-signin-container,
  .modern-forgot-container {
    @apply mx-4;
    max-width: calc(100vw - 32px);
  }

  .modern-signin-header {
    @apply p-6 pb-3;
  }

  .modern-signin-form {
    @apply p-6 pt-3 space-y-5;
  }

  .signin-icon {
    @apply w-14 h-14;
  }

  .signin-icon svg {
    @apply w-7 h-7;
  }

  .signin-title {
    @apply text-xl;
  }

  .signin-subtitle {
    @apply text-sm;
  }

  .modern-input {
    @apply py-3;
  }

  .modern-submit-button {
    @apply py-3;
  }
}

/* Animation for form validation */
.modern-input.error {
  @apply border-red-500;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Loading spinner integration */
.modern-submit-button .spinner {
  @apply w-5 h-5;
}

/* Focus trap for accessibility */
.modern-signin-overlay:focus-within {
  outline: none;
}

/* Smooth transitions for all interactives elements */
* {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Modern Signup Page */
.modern-signup-overlay {
  @apply min-h-screen w-full;
  background: linear-gradient(
    135deg,
    #f1f5f9 0%,
    #e2e8f0 30%,
    #cbd5e1 70%,
    #94a3b8 100%
  );
  animation: fadeIn 0.3s ease-out;
}

/* Modern Signup Container */
.modern-signup-container {
  @apply relative w-full max-w-6xl mx-auto min-h-screen;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 0 60px rgba(0, 0, 0, 0.08);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Progress Header - Modern Compact Design */
.signup-progress-header {
  @apply px-6 py-4;
  background: linear-gradient(
    135deg,
    rgba(248, 250, 252, 0.95),
    rgba(241, 245, 249, 0.98)
  );
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.progress-steps {
  @apply flex items-start justify-center mb-4 overflow-x-auto;
  gap: 0;
  padding: 0 2rem;
  position: relative;
}

.progress-step {
  @apply flex items-center min-w-0 relative;
  flex: 1;
  max-width: 180px;
  flex-direction: column;
  padding: 0.75rem 0.5rem;
  align-items: center;
}

/* Connection lines between steps - Better positioning */
.progress-step:not(:last-child)::after {
  content: "";
  @apply absolute h-0.5 bg-gray-300 transition-all duration-500;
  z-index: 1;
  top: 16px; /* Align with center of step circle */
  left: calc(50% + 16px); /* Start from edge of circle */
  right: calc(-50% + 16px); /* End at edge of next circle */
  width: calc(100% - 32px); /* Account for circle widths */
}

.progress-step.completed:not(:last-child)::after {
  @apply bg-emerald-500;
  box-shadow: 0 0 6px rgba(16, 185, 129, 0.4);
  height: 2px;
}

.progress-step.actives:not(:last-child)::after {
  background: linear-gradient(90deg, #10b981 0%, #6b7280 100%);
  height: 2px;
}

/* Step Circle Styles - Enhanced */
.progress-step.completed .step-circle {
  @apply text-white border-2 border-emerald-500;
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3),
    0 0 0 4px rgba(16, 185, 129, 0.1);
  z-index: 3;
  position: relative;
}

.progress-step.completed .step-title {
  @apply text-emerald-700 font-bold;
}

.progress-step.completed .step-subtitle {
  @apply text-emerald-600 font-medium;
}

.progress-step.actives .step-circle {
  @apply text-gray-700 border-2;
  background: white !important;
  border-color: #10b981 !important;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3),
    0 0 0 4px rgba(16, 185, 129, 0.1);
  transform: scale(1.1);
  z-index: 3;
  position: relative;
  animation: pulse-step 3s ease-in-out infinite;
}

.progress-step.actives .step-title {
  @apply text-emerald-700 font-bold;
  font-size: 0.8rem;
}

.progress-step.actives .step-subtitle {
  @apply text-emerald-600 font-semibold;
  font-size: 0.7rem;
}

.progress-step.upcoming .step-circle {
  @apply bg-white text-gray-500 border-2 border-gray-300;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 2;
  position: relative;
}

.progress-step.upcoming .step-title {
  @apply text-gray-500 font-medium;
}

.progress-step.upcoming .step-subtitle {
  @apply text-gray-400;
}

.step-circle {
  @apply w-8 h-8 rounded-full flex items-center justify-center font-bold text-xs transition-all duration-300;
  min-width: 32px;
  max-width: 32px;
  height: 32px;
  font-family: "Inter", sans-serif;
  border-width: 2px;
}

.step-circle svg {
  @apply w-4 h-4;
  flex-shrink: 0;
}

.step-info {
  @apply min-w-0 flex-1 text-center mt-3;
  max-width: 140px;
}

.step-title {
  @apply font-semibold text-gray-800 leading-tight;
  font-family: "Inter", sans-serif;
  font-size: 0.75rem;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.step-subtitle {
  @apply text-gray-500 leading-tight;
  font-family: "Inter", sans-serif;
  font-size: 0.65rem;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 400;
}

.progress-bar {
  @apply w-full h-1.5 bg-gray-200 rounded-full overflow-hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.progress-fill {
  @apply h-full transition-all duration-700 ease-out;
  background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
  box-shadow: 0 1px 4px rgba(16, 185, 129, 0.2);
  border-radius: inherit;
}

/* Enhanced animations and hover effects */
@keyframes pulse-step {
  0%,
  100% {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3),
      0 0 0 4px rgba(16, 185, 129, 0.1);
  }
  50% {
    transform: scale(1.12);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4),
      0 0 0 6px rgba(16, 185, 129, 0.15);
  }
}

@keyframes step-entrance {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation is applied via the main .progress-step.actives .step-circle rule above */

.progress-step {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: step-entrance 0.6s ease-out;
}

.progress-step:hover .step-circle {
  transform: scale(1.05);
}

.progress-step.completed:hover .step-circle {
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4),
    0 0 0 6px rgba(16, 185, 129, 0.15);
}

.progress-step.actives:hover .step-circle {
  animation-play-state: paused;
  transform: scale(1.15);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4),
    0 0 0 6px rgba(16, 185, 129, 0.2);
}

.progress-step.upcoming:hover .step-circle {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Step Content */
.signup-step-content {
  @apply p-8 min-h-screen;
  padding-top: 2rem;
  padding-bottom: 4rem;
}

.step-wrapper {
  @apply w-full max-w-4xl mx-auto;
}

/* User Type Selection - Modern Design */
.user-type-selection {
  @apply space-y-12 max-w-6xl mx-auto;
}

.selection-header {
  @apply text-center space-y-6 mb-12;
}

.header-icon {
  @apply w-20 h-20 mx-auto rounded-3xl flex items-center justify-center mb-6;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 20px 40px rgba(16, 185, 129, 0.3);
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.3);
  }
  100% {
    box-shadow: 0 25px 50px rgba(16, 185, 129, 0.4);
  }
}

.header-icon svg {
  @apply w-10 h-10;
}

.selection-title {
  @apply text-4xl font-bold text-gray-900 mb-4;
  font-family: "Plus Jakarta Sans", sans-serif;
  background: linear-gradient(135deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.selection-subtitle {
  @apply text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed;
  font-family: "DM Sans", sans-serif;
}

.user-type-grid {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-8;
}

.user-type-card {
  @apply relative bg-white rounded-3xl border border-gray-200 p-8 cursor-pointer transition-all duration-500 hover:shadow-2xl hover:-translate-y-2;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.user-type-card:hover {
  border-color: rgba(16, 185, 129, 0.4);
  box-shadow: 0 25px 60px rgba(16, 185, 129, 0.15);
  transform: translateY(-8px) scale(1.02);
}

.user-type-card.selected {
  @apply border-2;
  border-color: #10b981;
  box-shadow: 0 30px 70px rgba(16, 185, 129, 0.25);
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.05) 0%,
    rgba(5, 150, 105, 0.08) 100%
  );
  transform: translateY(-8px) scale(1.02);
}

.card-gradient {
  @apply w-20 h-20 rounded-2xl flex items-center justify-center mb-6 transition-all duration-300;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.user-type-card:hover .card-gradient {
  transform: scale(1.1);
}

.card-icon {
  @apply text-4xl transition-transform duration-300;
}

.user-type-card:hover .card-icon {
  transform: rotate(12deg);
}

.card-content {
  @apply space-y-6;
}

.card-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.card-subtitle {
  @apply text-blue-600 font-semibold text-lg mb-4;
  font-family: "Plus Jakarta Sans", sans-serif;
}

.card-description {
  @apply text-gray-600 leading-relaxed mb-6;
  font-family: "DM Sans", sans-serif;
  font-size: 15px;
  line-height: 1.6;
}

.card-features {
  @apply space-y-3;
}

.feature-item {
  @apply flex items-start space-x-3 text-gray-700;
  font-family: "DM Sans", sans-serif;
  font-size: 14px;
}

.feature-item svg {
  @apply w-5 h-5 text-green-500 flex-shrink-0 mt-0.5;
}

.card-selector {
  @apply absolute top-6 right-6;
}

.selector-circle {
  @apply w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center transition-all duration-300;
  background: white;
}

.selector-circle.selected {
  @apply border-emerald-500 text-white;
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  transform: scale(1.1);
}

.selector-circle svg {
  @apply w-5 h-5;
}

.selection-footer {
  @apply text-center space-y-6 mt-12;
}

.continue-button {
  @apply inline-flex items-center space-x-3 px-12 py-4 rounded-2xl font-bold text-white transition-all duration-300 text-lg;
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 12px 30px rgba(16, 185, 129, 0.4);
  font-family: "Plus Jakarta Sans", sans-serif;
}

.continue-button:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 20px 50px rgba(16, 185, 129, 0.5);
  background: linear-gradient(135deg, #047857, #065f46);
}

.continue-button:disabled {
  @apply opacity-50 cursor-not-allowed;
  transform: none;
  background: linear-gradient(135deg, #9ca3af, #6b7280);
}

.continue-button svg {
  @apply w-6 h-6;
}

.selection-hint {
  @apply text-gray-500 text-base;
  font-family: "DM Sans", sans-serif;
}

/* Enhanced Personal Information */
.enhanced-personal-info {
  @apply space-y-8;
}

.personal-info-header {
  @apply text-center space-y-4 mb-8;
}

.personal-info-header .header-icon {
  @apply w-16 h-16 mx-auto rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.personal-info-header .header-icon svg {
  @apply w-8 h-8;
}

.info-title {
  @apply text-3xl font-bold text-gray-800;
  font-family: "Inter", sans-serif;
}

.info-subtitle {
  @apply text-lg text-gray-600 max-w-2xl mx-auto;
  font-family: "Inter", sans-serif;
}

.personal-info-form {
  @apply max-w-2xl mx-auto;
}

.form-grid {
  @apply space-y-6;
}

/* Phone Input Wrapper */
.phone-input-wrapper {
  @apply flex space-x-3;
}

.country-selector {
  @apply flex-shrink-0;
  width: 80px;
}

.phone-input {
  @apply flex-1;
}

.optional-text {
  @apply text-gray-400 font-normal text-sm;
}

/* Form Actions */
.form-actions {
  @apply mt-8 text-center;
}

/* Error state for helper text */
.helper-text.error {
  @apply text-red-600;
}

/* Review Submit Component */
.review-submit {
  @apply space-y-8;
}

.review-header {
  @apply text-center space-y-4 mb-8;
}

.review-header .header-icon {
  @apply w-16 h-16 mx-auto rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.review-header .header-icon svg {
  @apply w-8 h-8;
}

.review-title {
  @apply text-3xl font-bold text-gray-800;
  font-family: "Inter", sans-serif;
}

.review-subtitle {
  @apply text-lg text-gray-600 max-w-2xl mx-auto;
  font-family: "Inter", sans-serif;
}

.review-content {
  @apply max-w-3xl mx-auto space-y-6;
}

.review-section {
  @apply space-y-4;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 border-b border-gray-200 pb-2;
  font-family: "Inter", sans-serif;
}

.review-card {
  @apply bg-white rounded-xl border border-gray-200 p-6 space-y-4;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.review-item {
  @apply flex justify-between items-start;
}

.item-label {
  @apply font-medium text-gray-600 min-w-0 flex-shrink-0 mr-4;
  font-family: "Inter", sans-serif;
  width: 140px;
}

.item-value {
  @apply text-gray-800 text-right break-words;
  font-family: "Inter", sans-serif;
}

.terms-card {
  @apply bg-blue-50 rounded-xl border border-blue-200 p-6;
}

.terms-title {
  @apply text-lg font-semibold text-blue-800 mb-3;
  font-family: "Inter", sans-serif;
}

.terms-text {
  @apply text-blue-700 text-sm leading-relaxed mb-4;
  font-family: "Inter", sans-serif;
}

.terms-links {
  @apply flex space-x-4;
}

.terms-link {
  @apply text-blue-600 hover:text-blue-800 text-sm font-medium underline transition-colors duration-300;
  font-family: "Inter", sans-serif;
}

.review-actions {
  @apply flex justify-between items-center max-w-3xl mx-auto pt-6;
}

.back-button {
  @apply inline-flex items-center space-x-2 px-6 py-3 rounded-xl font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-all duration-300;
  font-family: "Inter", sans-serif;
}

.back-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.back-button svg {
  @apply w-5 h-5;
}

.submit-button {
  @apply inline-flex items-center space-x-2 px-8 py-4 rounded-xl font-semibold text-white transition-all duration-300;
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4);
}

.submit-button:disabled {
  @apply opacity-70 cursor-not-allowed;
  transform: none;
}

.submit-button svg {
  @apply w-5 h-5;
}

.spinner {
  @apply w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* Slide Transitions */
.slide-enter {
  opacity: 0;
  transform: translateX(30px);
}

.slide-enter-actives {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.slide-exit {
  opacity: 1;
  transform: translateX(0);
}

.slide-exit-actives {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-signup-container {
    @apply mx-0;
  }

  .signup-progress-header {
    @apply px-4 py-3;
  }

  .progress-steps {
    @apply flex-row justify-center mb-3 px-1;
    gap: 0.5rem;
  }

  .progress-step {
    @apply flex-col items-center;
    flex: 1;
    max-width: 120px;
    padding: 0.5rem 0.25rem;
  }

  /* Adjust connection lines for mobile */
  .progress-step:not(:last-child)::after {
    top: 12px;
    left: calc(50% + 12px);
    right: calc(-50% + 12px);
    width: calc(100% - 24px);
    height: 1px;
  }

  .step-info {
    @apply mt-2;
    max-width: 100px;
  }

  .step-title {
    font-size: 0.65rem;
    @apply font-medium;
  }

  .step-subtitle {
    @apply hidden; /* Hide subtitles on mobile to save space */
  }

  .step-circle {
    @apply w-6 h-6 text-xs;
    min-width: 24px;
    max-width: 24px;
    height: 24px;
  }

  .step-circle svg {
    @apply w-3 h-3;
  }

  .progress-bar {
    @apply h-1;
  }

  .signup-step-content {
    @apply p-4;
  }

  .user-type-selection {
    @apply space-y-8;
  }

  .selection-header {
    @apply space-y-4 mb-8;
  }

  .header-icon {
    @apply w-16 h-16 mb-4;
  }

  .selection-title {
    @apply text-3xl;
  }

  .selection-subtitle {
    @apply text-lg;
  }

  .user-type-grid {
    @apply grid-cols-1 gap-6;
  }

  .user-type-card {
    @apply p-6;
  }

  .card-gradient {
    @apply w-16 h-16 mb-4;
  }

  .card-icon {
    @apply text-3xl;
  }

  .card-title {
    @apply text-xl;
  }

  .card-subtitle {
    @apply text-base;
  }

  .card-description {
    @apply text-sm;
  }

  .continue-button {
    @apply px-8 py-3 text-base;
  }

  .selection-footer {
    @apply space-y-4 mt-8;
  }

  .phone-input-wrapper {
    @apply flex-col space-x-0 space-y-3;
  }

  .country-selector {
    @apply w-full;
  }

  .review-actions {
    @apply flex-col space-y-4;
  }

  .back-button,
  .submit-button {
    @apply w-full justify-center;
  }

  .review-item {
    @apply flex-col items-start space-y-1;
  }

  .item-label {
    @apply w-full mr-0;
  }

  .item-value {
    @apply text-left;
  }
}

@media (max-width: 640px) {
  .signup-progress-header {
    @apply px-3 py-2;
  }

  .progress-steps {
    @apply flex justify-center mb-2 px-1;
    gap: 0.25rem;
  }

  .progress-step {
    @apply flex-col items-center;
    flex: none;
    min-width: 50px;
    padding: 0.25rem;
  }

  /* Hide connection lines on very small screens */
  .progress-step:not(:last-child)::after {
    @apply hidden;
  }

  .step-info {
    @apply mt-1;
    max-width: 50px;
  }

  .step-title {
    @apply font-medium leading-tight;
    font-size: 8px;
    max-width: 50px;
  }

  .step-subtitle {
    @apply hidden;
  }

  .step-circle {
    @apply w-5 h-5;
    min-width: 20px;
    max-width: 20px;
    height: 20px;
    font-size: 8px;
  }

  .step-circle svg {
    @apply w-2.5 h-2.5;
  }

  .progress-bar {
    @apply h-0.5;
  }

  .modern-signup-container {
    max-height: 98vh;
  }

  .signup-step-content {
    max-height: calc(98vh - 80px);
    @apply p-3;
  }
}
