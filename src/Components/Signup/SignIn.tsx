import React, { useState } from "react";
import "./style.css";
import axios from "axios";
import LoginInterface from "../Modules/LoginModalInterface/LoginModalInterface";
import { useNavigate } from "react-router-dom";
import { setUser } from "../../store/actioncreators/actionCreators";
import { Dispatch } from "redux";
import { useDispatch } from "react-redux";
import { Spinner } from "../utils/loader";
import { IoMdClose } from "react-icons/io";
import ReactGA from "react-ga4";
import { RequestMethods } from "../../shared/RequestMethods";
import { ResponseStatusCode } from "../../shared/ResponseStatusCode";

const Signin: React.FC<LoginInterface> = ({ handleLoginModal }) => {
  const dispatch: Dispatch<any> = useDispatch();

  const [signin, setSigninData] = useState({
    email: "",
    password: "",
    signinModal: true,
  });
  const [sigin_loader, setLoader] = useState(false);
  const [forgot_msg, setForgotMSG] = useState({
    msg: "",
    show: false,
  });
  const [signin_msg, setSigninMSG] = useState({
    msg: "",
    show: false,
  });

  const showForgotMsg = () => {
    setForgotMSG({
      msg: "Please check your mail for further details",
      show: true,
    });
  };

  const showSigninMsg = (meessage: string) => {
    setSigninMSG({ msg: meessage, show: true });
  };

  const setUserDetails = React.useCallback(
    (newuser: USER) => dispatch(setUser(newuser)),
    [dispatch]
  );

  let navigate = useNavigate();

  let handleNewPassword = async () => {
    let regexpEmail = new RegExp("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$");
    if (!regexpEmail.test(signin.email)) {
      let email = document.getElementById("email") as HTMLInputElement;
      email?.classList.remove("text-slate-500");
      email?.classList.add("text-red-500");
      email.textContent = "Enter valid email";
    }

    toggleLoader();

    axios
      .get(
        `${process.env.REACT_APP_BASE_API}/users/sendforgetpasswordemail/${signin.email}`,
        {
          method: RequestMethods.GET,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Credentials": true,
            xsrfCookieName: "XSRF-TOKEN",
            xsrfHeaderName: "X-XSRF-TOKEN",
          },
          withCredentials: true,
          data: {
            email: signin.email,
          },
        }
      )
      .finally(() => {
        toggleLoader();
        closeLoader();
        showForgotMsg();
      });
  };

  let showPass = () => {
    let passkey = document.getElementById(
      "passkey_input"
    ) as HTMLInputElement | null;
    if (passkey?.type === "text") {
      passkey.setAttribute("type", "password");
    } else {
      passkey?.setAttribute("type", "text");
    }
  };

  let handleSignUpNavi = () => {
    handleLoginModal();
    navigate("/signup");
  };

  let fetchDetails = async (extoken: string) => {
    let config = {
      method: RequestMethods.GET,
      url: `${process.env.REACT_APP_BASE_API}/users/getLoggedInUserDetails`,
      headers: {
        Authorization: `Bearer ${extoken}`,
      },
    };

    await axios(config)
      .then(function (response) {
        if (response.status === ResponseStatusCode.OK) {
          let getuser: USER = {
            ...response.data,
            id: response.data._id,
            admin: +response.data.userRole,
            token: extoken,
            loader: false,
            user: {
              name: response.data.fullName,
              email: response.data.email,
              phone: response.data.phoneNumber,
              country: response.data.countryCode,
              ref: response.data.referenceCode,
              pwd: signin.password,
              emailVerified: response.data.isEmailVerified,
              coverImg: response.data.bannerImage,
              rejected: response.data.isRejected,
              userVerified: response.data.isUserVerified,
              profileImg: response.data.profileImage,
            },
            company: response.data.company ?? [],
          };
          setUserDetails(getuser);
        } else {
          showSigninMsg("Please enter valid login details");
        }
      })
      .catch(function (error) {
        showSigninMsg("Please enter valid login details");
        closeLoader();
      });
  };

  const handleLogin = async () => {
    const regexpEmail = new RegExp(
      "^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,10}$"
    );
    const email = document.getElementById("email") as HTMLInputElement;
    const passkey = document.getElementById("passkey") as HTMLInputElement;

    if (!signin.email || !signin.password) {
      showSigninMsg("Please enter Email and Password");
      return;
    }

    if (!regexpEmail.test(signin.email)) {
      email?.classList.remove("text-slate-500");
      email?.classList.add("text-red-500");
      email.textContent = "Enter valid email id";
      return;
    }

    toggleLoader();

    const data = JSON.stringify({
      email: signin.email,
      password: signin.password,
    });

    const config = {
      method: RequestMethods.POST,
      url: `${process.env.REACT_APP_BASE_API}/users/login`,
      headers: {
        "Content-Type": "application/json",
      },
      data,
    };

    await axios(config)
      .then(function (response) {
        console.log({ response });
        if (response.data.status === ResponseStatusCode.OK) {
          localStorage.setItem(
            "GTI_data",
            response.data.message.access_token +
              " " +
              response.data.message.userDetails.email +
              " " +
              response.data.message.userDetails._id
          );

          ReactGA.event({
            category: "Authentication",
            action: "SignIn",
            label: "",
          });

          fetchDetails(response.data.message.access_token);
          handleLoginModal();
          // navigate(ROUTE.HOME);
          window.location.reload();
        } else {
          showSigninMsg(response?.data?.message);
        }
        closeLoader();
      })
      .catch(function (error) {
        console.log({ error });
        email?.classList.remove("text-slate-500");
        email?.classList.add("text-red-500");
        email.textContent = "Enter valid email id";
        passkey?.classList.remove("text-slate-500");
        passkey?.classList.add("text-red-500");
        passkey.textContent = "Enter valid password";
        toggleLoader();
      });
  };

  const toggleLoader = () => {
    setLoader(!sigin_loader);
  };
  const closeLoader = () => {
    setLoader(false);
  };

  return (
    <div className="modern-signin-overlay">
      {signin.signinModal ? (
        <div className="modern-signin-container">
          {/* Close Button */}
          <button
            onClick={handleLoginModal}
            className="modern-close-button"
            aria-label="Close sign in modal"
          >
            <IoMdClose />
          </button>

          {/* Header */}
          <div className="modern-signin-header">
            <div className="signin-logo-section">
              <div className="signin-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                </svg>
              </div>
              <h1 className="signin-title">Welcome Back</h1>
              <p className="signin-subtitle">Sign in to your GTI account</p>
            </div>
          </div>

          {/* Form */}
          <div className="modern-signin-form">
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleLogin();
              }}
            >
              {/* Email Field */}
              <div className="enhanced-input-group">
                <label htmlFor="email_input" className="input-label">
                  Email Address
                </label>
                <div className="input-wrapper">
                  <div className="input-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                    </svg>
                  </div>
                  <input
                    type="email"
                    id="email_input"
                    value={signin.email}
                    onChange={(e) => {
                      setSigninData({ ...signin, email: e.target.value });
                    }}
                    className="enhanced-input"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div className="input-helper">
                  <span className="helper-text">
                    We'll never share your email
                  </span>
                </div>
              </div>

              {/* Password Field */}
              <div className="enhanced-input-group">
                <label htmlFor="passkey_input" className="input-label">
                  Password
                </label>
                <div className="input-wrapper">
                  <div className="input-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18,8h-1V6c0-2.76-2.24-5-5-5S7,3.24,7,6v2H6c-1.1,0-2,0.9-2,2v10c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V10C20,8.9,19.1,8,18,8z M12,17c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S13.1,17,12,17z M15.1,8H8.9V6c0-1.71,1.39-3.1,3.1-3.1s3.1,1.39,3.1,3.1V8z" />
                    </svg>
                  </div>
                  <input
                    type="password"
                    id="passkey_input"
                    value={signin.password}
                    onChange={(e) => {
                      setSigninData({ ...signin, password: e.target.value });
                    }}
                    className="enhanced-input"
                    placeholder="••••••••"
                    required
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => {
                      showPass();
                    }}
                    aria-label="Toggle password visibility"
                  >
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z" />
                    </svg>
                  </button>
                </div>
                <div className="input-helper">
                  <span className="helper-text">
                    Minimum 8 characters required
                  </span>
                </div>
              </div>

              {/* Forgot Password */}
              <div className="forgot-password-section">
                <button
                  type="button"
                  onClick={() => {
                    setSigninData({ ...signin, signinModal: false });
                    setForgotMSG({ msg: "", show: false });
                    setSigninMSG({ msg: "", show: false });
                    closeLoader();
                  }}
                  className="forgot-password-link"
                >
                  Forgot your password?
                </button>
              </div>

              {/* Error Message */}
              {signin_msg.show && (
                <div className="error-message">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M13,17h-2v-2h2V17z M13,13h-2V7h2V13z" />
                  </svg>
                  <span>{signin_msg.msg}</span>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={sigin_loader}
                className="modern-submit-button"
              >
                {sigin_loader ? (
                  <>
                    <Spinner />
                    <span>Signing in...</span>
                  </>
                ) : (
                  <>
                    <span>Sign In</span>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,4l1.41,1.41L7.83,11H20v2H7.83l5.58,5.59L12,20l-8-8L12,4z" />
                    </svg>
                  </>
                )}
              </button>
            </form>

            {/* Sign Up Link */}
            <div className="signup-section">
              <p className="signup-text">
                Don't have an account?
                <button onClick={handleSignUpNavi} className="signup-link">
                  Create account
                </button>
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="modern-forgot-container">
          {/* Close Button */}
          <button
            onClick={handleLoginModal}
            className="modern-close-button"
            aria-label="Close forgot password modal"
          >
            <IoMdClose />
          </button>

          {/* Header */}
          <div className="modern-signin-header">
            <div className="signin-logo-section">
              <div className="signin-icon forgot-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M18,8h-1V6c0-2.76-2.24-5-5-5S7,3.24,7,6v2H6c-1.1,0-2,0.9-2,2v10c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V10C20,8.9,19.1,8,18,8z M12,17c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S13.1,17,12,17z M15.1,8H8.9V6c0-1.71,1.39-3.1,3.1-3.1s3.1,1.39,3.1,3.1V8z" />
                </svg>
              </div>
              <h1 className="signin-title">Forgot Password?</h1>
              <p className="signin-subtitle">
                Enter your email to reset your password
              </p>
            </div>
          </div>

          {/* Form */}
          <div className="modern-signin-form">
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleNewPassword();
              }}
            >
              {/* Email Field */}
              <div className="enhanced-input-group">
                <label htmlFor="forgot_email_input" className="input-label">
                  Email Address
                </label>
                <div className="input-wrapper">
                  <div className="input-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                    </svg>
                  </div>
                  <input
                    type="email"
                    id="forgot_email_input"
                    value={signin.email}
                    onChange={(e) => {
                      setSigninData({ ...signin, email: e.target.value });
                    }}
                    className="enhanced-input"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div className="input-helper">
                  <span className="helper-text">
                    Enter the email associated with your account
                  </span>
                </div>
              </div>

              {/* Error/Success Message */}
              {forgot_msg.show && (
                <div className="success-message">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M10,17l-5-5l1.41-1.41L10,14.17l7.59-7.59L19,8L10,17z" />
                  </svg>
                  <span>{forgot_msg.msg}</span>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={sigin_loader}
                className="modern-submit-button"
              >
                {sigin_loader ? (
                  <>
                    <Spinner />
                    <span>Sending...</span>
                  </>
                ) : (
                  <>
                    <span>Send Reset Link</span>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
                    </svg>
                  </>
                )}
              </button>
            </form>

            {/* Back to Sign In */}
            <div className="signup-section">
              <p className="signup-text">
                Remember your password?
                <button
                  onClick={() => {
                    setSigninData({ ...signin, signinModal: true });
                    setForgotMSG({ msg: "", show: false });
                    closeLoader();
                  }}
                  className="signup-link"
                >
                  Back to Sign In
                </button>
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Signin;
