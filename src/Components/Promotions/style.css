.product-list-main {
  @apply grid w-full md:px-10 mx-auto lg:gap-x-5 gap-x-10 gap-y-10 lg:grid-cols-3 sm:grid-cols-2 grid-cols-1;
}

.product-card-title {
  @apply flex text-left px-4 space-y-1;
}

.product-card-main {
  @apply flex flex-col justify-start sm:m-10 duration-150 border md:pb-2 shadow-lg hover:shadow-lg hover:shadow-GTI-BLUE-default space-y-1 rounded-xl cursor-pointer;
}

.product-card-img {
  @apply flex w-full duration-150;
}

* {
  box-sizing: border-box;
  font-family: Arial, sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
}

/* Dropdown styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #ffffff;
  border: 1px solid #ccc;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
  border-radius: 4px;
}

.dropdown-toggle:hover {
  background-color: #f8f8f8;
}

.dropdown-option-image {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  border-radius: 50%;
}

.dropdown-option-label {
  flex-grow: 1;
  font-weight: bold;
}

.dropdown-caret {
  display: inline-block;
  margin-left: 12px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 8px 0 8px;
  border-color: #999 transparent transparent transparent;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  margin: 0;
  padding: 12px 0;
  background-color: #fff;
  border: 1px solid #ccc;
  border-top: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  list-style: none;
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-12px);
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
  max-height: 200px;
  overflow-y: auto;
  border-radius: 4px;
}

.dropdown-menu.open {
  opacity: 1;
  visibility: visible;
}

.dropdown-menu li {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-menu li:hover {
  background-color: #f8f8f8;
}

.dropdown-menu li .dropdown-option-image {
  margin-right: 12px;
}

.dropdown-menu li .dropdown-option-label {
  flex-grow: 1;
  font-weight: bold;
}

/* Centering the dropdown */
.dropdown.open .dropdown-menu {
  transform: translateY(0);
}

/* Adjust dropdown width */
.dropdown.open .dropdown-toggle {
  border-radius: 4px 4px 0 0;
  border-bottom: none;
}

.dropdown.open .dropdown-menu {
  border-radius: 0 0 4px 4px;
  border-top: none;
}

/* Enhanced Modern Promotions Styles */

/* Premium Button Styles with Advanced Animations */
.button {
  @apply font-semibold rounded-xl text-sm px-6 py-3 mx-1 mb-2 focus:outline-none transition-all duration-300 transform hover:scale-105 relative overflow-hidden;
}

.button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.button:hover::before {
  left: 100%;
}

.active {
  @apply bg-gradient-to-r from-GTI-BLUE-default to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-xl hover:shadow-2xl;
}

.not-active {
  @apply bg-white text-GTI-BLUE-default border-2 border-gray-200 hover:border-GTI-BLUE-default hover:shadow-lg hover:bg-blue-50;
}

/* Enhanced Modern Modal Styles */
.product-modal-main {
  @apply duration-300 ease-in-out w-full max-w-3xl h-fit flex flex-col space-y-8 bg-white shadow-2xl p-10 rounded-3xl border border-gray-100 relative overflow-hidden;
}

.product-modal-main::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  border-radius: 24px 24px 0 0;
}

.modal-input {
  @apply w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-300 bg-gray-50 focus:bg-white placeholder-gray-400 text-gray-700;
}

.modal-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
}

/* Enhanced Card Animations with 3D Effects */
.promotion-card {
  @apply transition-all duration-500 ease-in-out relative;
  transform-style: preserve-3d;
}

.promotion-card:hover {
  @apply transform -translate-y-4 shadow-2xl;
  transform: translateY(-16px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.promotion-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(139, 92, 246, 0.1)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.promotion-card:hover::before {
  opacity: 1;
}

/* Enhanced Modern Form Styles */
.modern-form-input {
  @apply w-full px-5 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-300 bg-gradient-to-r from-gray-50 to-white focus:from-white focus:to-blue-50 placeholder-gray-400;
}

.modern-form-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(59, 130, 246, 0.1);
}

/* Enhanced Button Variants with Premium Effects */
.btn-primary {
  @apply bg-gradient-to-r from-GTI-BLUE-default to-blue-700 text-white px-8 py-4 rounded-2xl font-bold hover:from-blue-700 hover:to-blue-800 focus:ring-4 focus:ring-GTI-BLUE-default/30 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl relative overflow-hidden;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-secondary {
  @apply bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-8 py-4 rounded-2xl font-semibold hover:from-gray-200 hover:to-gray-300 focus:ring-4 focus:ring-gray-300/50 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl border border-gray-200 hover:border-gray-300;
}

.btn-ghost {
  @apply bg-transparent text-GTI-BLUE-default px-8 py-4 rounded-2xl font-semibold border-2 border-GTI-BLUE-default hover:bg-GTI-BLUE-default hover:text-white focus:ring-4 focus:ring-GTI-BLUE-default/30 transition-all duration-300 transform hover:scale-105;
}

/* Platform Card Styles */
.platform-card {
  @apply bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2;
}

/* Social Media Button Styles */
.social-btn {
  @apply bg-blue-50 hover:bg-blue-100 p-4 rounded-xl transition-colors duration-200 group;
}

.social-btn img {
  @apply w-full group-hover:scale-110 transition-transform duration-200;
}

/* Enhanced Loading States with Modern Animations */
.loading-shimmer {
  @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] rounded-2xl;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* New Modern Animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.5s ease-out;
}

/* Enhanced Responsive Design */
@media (max-width: 640px) {
  .product-modal-main {
    @apply w-full mx-2 p-6 rounded-2xl max-h-[90vh] overflow-y-auto;
  }

  .btn-primary,
  .btn-secondary,
  .btn-ghost {
    @apply px-6 py-3 text-sm rounded-xl;
  }

  .platform-card {
    @apply rounded-2xl;
  }

  .promotion-card:hover {
    transform: translateY(-8px) rotateX(2deg) rotateY(2deg);
  }

  .modern-form-input {
    @apply px-4 py-3 text-sm;
  }
}

@media (max-width: 768px) {
  .btn-primary,
  .btn-secondary,
  .btn-ghost {
    @apply px-6 py-3;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .product-modal-main {
    @apply bg-gray-800 border-gray-700;
  }

  .modal-input,
  .modern-form-input {
    @apply bg-gray-700 border-gray-600 text-white placeholder-gray-400;
  }

  .btn-secondary {
    @apply bg-gray-700 text-gray-200 hover:bg-gray-600;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .promotion-card,
  .button,
  .btn-primary,
  .btn-secondary,
  .btn-ghost {
    transition: none;
    animation: none;
  }

  .promotion-card:hover {
    transform: none;
  }
}

/* Focus Styles for Better Accessibility */
.button:focus-visible,
.btn-primary:focus-visible,
.btn-secondary:focus-visible,
.btn-ghost:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.modal-input:focus-visible,
.modern-form-input:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
