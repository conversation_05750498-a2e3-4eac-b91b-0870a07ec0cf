import React, { Di<PERSON>atch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useParams, useLocation, useNavigate, Link } from "react-router-dom";
import axios from "axios";
import { useSelector } from "react-redux";
import ReactCountryFlag from "react-country-flag";
import Slider from "react-slick";
import { Helmet } from "react-helmet";
import "./Featured.css";
import {
  StarIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  LinkIcon,
  UserGroupIcon,
  HeartIcon,
  DocumentTextIcon,
  EyeIcon,
  PhotoIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/react/24/outline";

import companyLogo from "../../assests/banners/company_logo.png";
import {
  followUser,
  unfollowUser,
} from "../../store/actioncreators/followactions";
import { sendConnection } from "../../store/actioncreators/connectionactions";
import { ScreenSpinner } from "../utils/loader";
import { spinnerLoaderStop } from "../../store/actioncreators/loaderactions";
import RenderHTML from "../utils/RenderHTML";
import { BiLinkExternal } from "react-icons/bi";
// Types are imported from store/type.d.ts globally

const Featured = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  const dispatch: Dispatch<any> = useDispatch();
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);

  let [promotion, setPromotion] = useState({
    _id: "",
    title: "",
    description: "",
    contentTitle: "",
    contentDescription: "",
    images: [],
    userId: "",
    productId: "",
    opportunityId: "",
    paymentStatus: "",
    verificationStatus: "",
    createdAt: "",
    __v: -1,
    users: {
      _id: "",
      fullName: "",
      email: "",
      phoneNumber: "",
      countryCode: "",
      referenceCode: "",
      isEmailVerified: false,
      isUserVerified: false,
      isRejected: false,
      password: "",
      userRole: -1,
      userType: "",
      companyId: "",
      follower: [],
      following: [],
      connections: [
        {
          connectionStatus: "",
          userId: "",
        },
      ],
      createdAt: "",
      __v: -1,
    },
    company: {
      _id: "",
      name: "",
      logo: "",
      description: "",
      address: "",
      website: "",
      country: "",
      companyTurnover: -1,
      companyId: "",
      typeAndSizeOfPartnersRequired: [],
      typesOfPartnershipConsidered: [],
      createdAt: "",
      developmentStage: "",
      iprStatus: [],
      __v: -1,
    },
  });

  const sidebar_carousal_settings = {
    dots: true,
    infinite: true,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    swipeToSlide: true,
    autoplay: true,
  };

  const user: USER = useSelector((state: STATE) => state.USER.USER);
  const location = useLocation();
  // const navigate = useNavigate(); // Commented out as not currently used

  const [following, setFollowing] = useState(false);
  const [connected, setConnection] = useState("Connect");

  let { id } = useParams();
  let promotionId: string = id ? id : "";

  const loadPromotion = async (id: string) => {
    let config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/promotions/details/${id}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    await axios(config)
      .then(function (response) {
        setPromotion(response.data.promotion);
        dispatch(spinnerLoaderStop());
      })
      .catch(function (error) {
        dispatch(spinnerLoaderStop());
      });
  };

  const isFollowing = async (userid: string) => {
    const extoken: string =
      localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";

    let config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/users/checkisfollowing/${userid}`,
      headers: {
        Authorization: `Bearer ${extoken}`,
        "Content-Type": "application/json",
      },
    };

    await axios(config)
      .then(function (response) {
        setFollowing(response.data);
      })
      .catch(function (error) {});
  };

  const isConnected = async (userid: string) => {
    const extoken: string =
      localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";

    let config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/users/checkisconnected/${userid}`,
      headers: {
        Authorization: `Bearer ${extoken}`,
        "Content-Type": "application/json",
      },
    };

    await axios(config)
      .then(function (response) {
        if (typeof response.data == "string") {
          setConnection(response.data);
        }
      })
      .catch(function (error) {});
  };

  useEffect(() => {
    loadPromotion(promotionId);
    isFollowing(promotion.userId);
    isConnected(promotion.userId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const DOC = new Date(promotion.createdAt);

  const handleFollow = () => {
    if (user?.admin !== -1) {
      if (!following) {
        dispatch(followUser(promotion.userId));
        setFollowing(true);
      }
      return;
    }
    handleLoginModal();
  };
  const handleUnfollow = () => {
    if (user?.admin !== -1) {
      if (following) {
        dispatch(unfollowUser(promotion.userId));
        setFollowing(false);
      }
      return;
    }
    handleLoginModal();
  };
  const handleConnect = () => {
    if (user?.admin !== -1) {
      if (connected === "Connect") {
        dispatch(sendConnection(promotion.userId));
        setConnection("Connection Requested");
      }
      return;
    }
    handleLoginModal();
  };

  return (
    <React.Fragment>
      <Helmet>
        <title>{promotion?.company?.name}</title>
        <meta
          name="description"
          key="description"
          content={
            promotion?.contentDescription
              ? promotion?.contentDescription
              : promotion?.company?.description
          }
        />
        <meta name="title" key="title" content={promotion?.company?.name} />
        <meta property="og:title" content={promotion?.company?.name} />
        <meta
          property="og:description"
          content={
            promotion.contentDescription
              ? promotion.contentDescription
              : promotion.company.description
          }
        />
        {/* <meta
          property="og:image"
          content={product.image === NONE ? productbanner : product.image}
        /> */}
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/featuredview/${promotion._id}`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content={promotion?.company?.name} />
        <meta
          name="twitter:description"
          content={
            promotion?.contentDescription
              ? promotion?.contentDescription
              : promotion?.company?.description
          }
        />
        {/* <meta
          name="twitter:image"
          content={product?.image === NONE ? productbanner : product?.image}
        /> */}
        <meta name="twitter:card" content={promotion?.company?.name} />
      </Helmet>
      <div className="min-h-screen bg-gray-50">
        {spinner.SPINNER ? (
          <ScreenSpinner />
        ) : (
          <>
            {/* Enhanced Hero Section */}
            <div className="relative bg-gradient-to-br from-GTI-BLUE-default via-blue-700 to-indigo-900 overflow-hidden">
              <div className="absolute inset-0 bg-black/20"></div>

              {/* Enhanced Animated Background Elements */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute -top-40 -right-40 w-96 h-96 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/5 rounded-full blur-2xl animate-ping"></div>

                {/* Floating particles */}
                <div className="absolute top-20 left-20 w-2 h-2 bg-white/30 rounded-full animate-float"></div>
                <div className="absolute top-32 right-32 w-3 h-3 bg-white/20 rounded-full animate-float delay-500"></div>
                <div className="absolute bottom-20 left-1/3 w-2 h-2 bg-white/25 rounded-full animate-float delay-1000"></div>
              </div>

              <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-32">
                {/* Enhanced Icon with Animation */}
                <div className="flex items-center justify-center mb-8">
                  <div className="relative group">
                    <div className="absolute inset-0 bg-white/30 rounded-full blur-2xl group-hover:blur-3xl transition-all duration-500"></div>
                    <div className="relative bg-white/15 backdrop-blur-sm rounded-3xl p-6 border border-white/30 group-hover:border-white/50 transition-all duration-300 transform group-hover:scale-110 animate-glow">
                      <StarIcon className="h-16 w-16 md:h-20 md:w-20 text-white animate-pulse" />
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  {/* Enhanced Typography */}
                  <h1 className="text-4xl md:text-6xl lg:text-7xl font-extrabold text-white mb-6 leading-tight tracking-tight animate-slide-in-up">
                    <span className="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                      {promotion.contentTitle}
                    </span>
                  </h1>

                  <div className="w-24 h-1 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-8"></div>

                  {/* Enhanced Date Display */}
                  <div className="flex items-center justify-center space-x-3 text-blue-100 mb-8 animate-fade-in-scale">
                    <div className="bg-white/10 rounded-xl p-2">
                      <CalendarIcon className="h-6 w-6" />
                    </div>
                    <span className="text-xl font-medium">
                      Featured on{" "}
                      {DOC.toLocaleString("default", {
                        month: "long",
                        day: "2-digit",
                        year: "numeric",
                      })}
                    </span>
                  </div>

                  {/* Featured Badge */}
                  <div className="inline-flex items-center space-x-2 bg-white/15 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/30 animate-fade-in-scale">
                    <StarIcon className="h-5 w-5 text-yellow-300" />
                    <span className="text-white font-semibold">
                      Premium Featured Content
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
                {/* Enhanced Content Section */}
                <div className="lg:col-span-2">
                  <article className="bg-white rounded-3xl shadow-2xl p-10 border border-gray-100 hover:shadow-3xl transition-all duration-500 relative overflow-hidden group">
                    {/* Decorative Background */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-GTI-BLUE-default/5 to-purple-500/5 rounded-full blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-500/5 to-GTI-BLUE-default/5 rounded-full blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Content Header */}
                    <div className="relative z-10 mb-8 pb-6 border-b border-gray-100">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="bg-GTI-BLUE-default/10 rounded-xl p-2">
                          <DocumentTextIcon className="h-6 w-6 text-GTI-BLUE-default" />
                        </div>
                        <span className="text-GTI-BLUE-default font-semibold text-lg">
                          Featured Content
                        </span>
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900 mb-2">
                        Content Description
                      </h2>
                      <p className="text-gray-600">
                        Detailed information about this featured innovation
                      </p>
                    </div>

                    {/* Enhanced Content Body */}
                    <div className="relative z-10 prose prose-lg prose-blue max-w-none">
                      <div className="text-gray-700 leading-relaxed">
                        <RenderHTML html={promotion.contentDescription} />
                      </div>
                    </div>

                    {/* Reading Progress Indicator */}
                    <div className="mt-8 pt-6 border-t border-gray-100">
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span>Reading time: ~3 min</span>
                        <div className="flex items-center space-x-2">
                          <EyeIcon className="h-4 w-4" />
                          <span>Featured Content</span>
                        </div>
                      </div>
                    </div>
                  </article>
                </div>

                {/* Enhanced Sidebar */}
                <div className="space-y-8">
                  {/* Enhanced Image Gallery */}
                  {promotion?.images?.length > 0 && (
                    <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-100 hover:shadow-3xl transition-all duration-500 group">
                      <div className="p-8">
                        {/* Gallery Header */}
                        <div className="mb-6">
                          <div className="flex items-center space-x-3 mb-3">
                            <div className="bg-GTI-BLUE-default/10 rounded-xl p-2">
                              <PhotoIcon className="h-6 w-6 text-GTI-BLUE-default" />
                            </div>
                            <h3 className="text-xl font-bold text-gray-900">
                              Featured Gallery
                            </h3>
                          </div>
                          <p className="text-gray-600 text-sm">
                            {promotion.images.length} image
                            {promotion.images.length > 1 ? "s" : ""} showcasing
                            this innovation
                          </p>
                        </div>

                        {/* Enhanced Image Slider */}
                        <div className="relative group/slider">
                          <Slider
                            {...sidebar_carousal_settings}
                            className="rounded-2xl overflow-hidden shadow-lg"
                          >
                            {promotion.images.map((image, index) => (
                              <div
                                key={index}
                                className="relative aspect-video group/image"
                              >
                                <img
                                  className="w-full h-full object-cover rounded-2xl transition-transform duration-500 group-hover/image:scale-105"
                                  src={image}
                                  alt={`${promotion.contentTitle} - View ${
                                    index + 1
                                  }`}
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover/image:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
                                <div className="absolute bottom-4 left-4 opacity-0 group-hover/image:opacity-100 transition-opacity duration-300">
                                  <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                                    <span className="text-sm font-medium text-gray-800">
                                      {index + 1} / {promotion.images.length}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </Slider>

                          {/* Image Counter */}
                          <div className="mt-4 flex items-center justify-center space-x-2">
                            <div className="bg-GTI-BLUE-default/10 rounded-full px-3 py-1">
                              <span className="text-GTI-BLUE-default text-sm font-medium">
                                {promotion.images.length} Images
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Enhanced Related Links */}
                  {(promotion?.productId || promotion?.opportunityId) && (
                    <div className="bg-white rounded-3xl shadow-2xl p-8 border border-gray-100 hover:shadow-3xl transition-all duration-500 group">
                      {/* Section Header */}
                      <div className="mb-6">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="bg-GTI-BLUE-default/10 rounded-xl p-2">
                            <LinkIcon className="h-6 w-6 text-GTI-BLUE-default" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-900">
                            Related Content
                          </h3>
                        </div>
                        <p className="text-gray-600 text-sm">
                          Explore the original{" "}
                          {promotion?.productId ? "technology" : "opportunity"}{" "}
                          behind this featured content
                        </p>
                      </div>

                      {/* Enhanced Link Card */}
                      <Link
                        className="block p-6 bg-gradient-to-br from-GTI-BLUE-default/5 to-purple-500/5 rounded-2xl hover:from-GTI-BLUE-default/10 hover:to-purple-500/10 transition-all duration-300 group/link border border-GTI-BLUE-default/20 hover:border-GTI-BLUE-default/40 transform hover:scale-105 hover:shadow-lg"
                        to={`${
                          promotion?.productId
                            ? `/product/${promotion?.productId}`
                            : `/opportunity/${promotion?.opportunityId}`
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="bg-GTI-BLUE-default/15 rounded-2xl p-3 group-hover/link:bg-GTI-BLUE-default/25 transition-colors duration-300">
                              <BuildingOfficeIcon className="h-8 w-8 text-GTI-BLUE-default" />
                            </div>
                            <div>
                              <p className="font-bold text-gray-900 text-lg group-hover/link:text-GTI-BLUE-default transition-colors duration-300">
                                View{" "}
                                {promotion?.productId
                                  ? "Technology"
                                  : "Opportunity"}
                              </p>
                              <p className="text-gray-600 text-sm">
                                Learn more about the original content
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <ArrowTopRightOnSquareIcon className="h-6 w-6 text-GTI-BLUE-default group-hover/link:translate-x-1 group-hover/link:-translate-y-1 transition-transform duration-300" />
                          </div>
                        </div>
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Company Information Section */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
              <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-100 hover:shadow-3xl transition-all duration-500 relative">
                {/* Decorative Background */}
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-GTI-BLUE-default/5 to-purple-500/5 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-500/5 to-GTI-BLUE-default/5 rounded-full blur-2xl"></div>

                {/* Enhanced Header */}
                <div className="relative bg-gradient-to-br from-GTI-BLUE-default to-blue-800 p-8">
                  <div className="flex items-center space-x-4">
                    <div className="bg-white/20 rounded-2xl p-3">
                      <BuildingOfficeIcon className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h2 className="text-3xl font-bold text-white">
                        Company Information
                      </h2>
                      <p className="text-blue-100 mt-1">
                        Learn more about the organization behind this innovation
                      </p>
                    </div>
                  </div>
                </div>

                <div className="relative p-10">
                  <div className="flex flex-col lg:flex-row items-start lg:items-center space-y-8 lg:space-y-0 lg:space-x-12">
                    {/* Enhanced Company Logo */}
                    <div className="flex-shrink-0">
                      <div className="relative group">
                        <div className="absolute inset-0 bg-GTI-BLUE-default/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                        <div className="relative w-40 h-40 md:w-48 md:h-48 bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl overflow-hidden shadow-xl border-4 border-white group-hover:scale-105 transition-transform duration-300">
                          <img
                            className="w-full h-full object-contain p-6"
                            src={
                              !promotion?.company?.logo
                                ? companyLogo
                                : promotion?.company?.logo
                            }
                            alt={promotion?.company?.name}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Company Details */}
                    <div className="flex-1">
                      <div className="mb-8">
                        <h3 className="text-3xl font-bold text-gray-900 mb-4">
                          {promotion?.company?.name}
                        </h3>
                        {promotion?.company?.country && (
                          <div className="flex items-center space-x-3 mb-4">
                            <div className="bg-gray-100 rounded-xl p-2">
                              <ReactCountryFlag
                                countryCode={promotion.company?.country}
                                svg
                                style={{ width: "1.5em", height: "1.5em" }}
                                className="rounded"
                              />
                            </div>
                            <div>
                              <span className="text-gray-900 font-semibold text-lg">
                                {promotion.company?.country}
                              </span>
                              <p className="text-gray-500 text-sm">
                                Company Location
                              </p>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Enhanced Company Description */}
                      {promotion.company?.description && (
                        <div className="mb-8">
                          <h4 className="text-lg font-semibold text-gray-900 mb-3">
                            About the Company
                          </h4>
                          <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200">
                            <p className="text-gray-700 leading-relaxed text-lg">
                              {promotion.company?.description}
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Enhanced Action Buttons */}
                      {promotion?.userId !== user?.id && (
                        <div className="space-y-4">
                          <h4 className="text-lg font-semibold text-gray-900">
                            Connect with this Company
                          </h4>
                          <div className="flex flex-wrap gap-4">
                            <button
                              type="button"
                              className="flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-GTI-BLUE-default to-blue-700 text-white rounded-2xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-bold shadow-xl hover:shadow-2xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none group relative overflow-hidden"
                              disabled={connected !== "Connect"}
                              onClick={() => {
                                handleConnect();
                              }}
                            >
                              <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                              <UserGroupIcon className="h-6 w-6 relative z-10" />
                              <span className="relative z-10">{connected}</span>
                            </button>

                            <button
                              type="button"
                              className="flex items-center space-x-3 px-8 py-4 bg-white text-GTI-BLUE-default border-2 border-GTI-BLUE-default rounded-2xl hover:bg-GTI-BLUE-default hover:text-white transition-all duration-300 font-bold shadow-lg hover:shadow-xl transform hover:scale-105 group"
                              onClick={() => {
                                !following ? handleFollow() : handleUnfollow();
                              }}
                            >
                              <HeartIcon
                                className={`h-6 w-6 transition-transform duration-300 group-hover:scale-110 ${
                                  following ? "fill-current text-red-500" : ""
                                }`}
                              />
                              <span>{following ? "Unfollow" : "Follow"}</span>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </React.Fragment>
  );
};

export default Featured;
