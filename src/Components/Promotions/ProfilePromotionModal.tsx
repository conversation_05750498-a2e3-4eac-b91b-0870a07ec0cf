import React, { Dispatch, useEffect, useState } from "react";
import ReactD<PERSON> from "react-dom";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { Form, Formik } from "formik";
import { AiOutlineClose } from "react-icons/ai";
import { useDispatch, useSelector } from "react-redux";
import {
  StarIcon,
  SparklesIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  PhotoIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";

import {
  CONTENT_TYPE,
  FILE_PATH,
  FILE_TYPE,
  presignedData,
  promotionFiles,
  promotionsCreate,
} from "../constants";
import { notify } from "../../utils";
import { RequestMethods } from "../../shared/RequestMethods";
import {
  failToast,
  successToast,
} from "../../store/actioncreators/toastactions";
import { promotionsSchema } from "../validations/promotionsValidation";
import { Company } from "../../shared/constants";
import CustomEditor from "../shared/CustomEditor";
import gbi_home_logo from "../../assests/home/<USER>";
import { createPromotion } from "../../store/actioncreators/promotionsaction";

interface ProfilePromotionModalProps {
  changeModalState: () => void;
  handleSuccessModal: (isOpen: boolean, state: string, message: string) => void;
}

const ProfilePromotionModal: React.FC<ProfilePromotionModalProps> = ({
  changeModalState,
  handleSuccessModal,
}) => {
  const user: USER = useSelector((state: STATE) => state.USER.USER);
  const navigate = useNavigate();

  useEffect(() => {
    if (!user.id) {
      notify("Unauthorized", "error");
      navigate("/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const initialValues: promotionsCreate = {
    title: "",
    description: "",
    images: [],
  };

  const dispatch: Dispatch<any> = useDispatch();

  const [files, setFiles] = useState<promotionFiles>({
    image: false,
    imageFile: [],
  });

  const handleImage = function (e: React.ChangeEvent<HTMLInputElement>) {
    const fileList = e.target.files || [];
    if (!fileList) return;

    const images = [];
    const totalFiles = fileList?.length || 0;
    for (let i = 0; i < totalFiles; i++) {
      images.push(fileList[i]);
    }

    setFiles({ ...files, imageFile: images, image: false });
  };

  const getPresigned = async (content: presignedData) => {
    const data = JSON.stringify(content);
    let result: string = "";
    const config = {
      method: RequestMethods.POST,
      url: `${process.env.REACT_APP_BASE_API}/users/getPresignedUrl`,
      headers: {
        "Content-Type": "application/json",
      },
      data,
    };

    await axios(config)
      .then(function (response) {
        result = response.data;
      })
      .catch(function (error) {
        result = error.message;
        dispatch(failToast());
      });

    return result;
  };

  const postLogo = async (signed: string, imageFile: File) => {
    var config = {
      method: RequestMethods.PUT,
      url: signed,
      headers: {
        "Content-Type": CONTENT_TYPE,
        "Access-Control-Allow-Origin": true,
      },
      data: imageFile,
    };

    await axios(config)
      .then(function (response) {
        dispatch(successToast());
      })
      .catch(function (error) {});
  };

  const paymentHandler = async (
    amount: number,
    currency: string,
    order_id: string,
    promotionId: string
  ) => {
    let options = {
      key: process.env.REACT_APP_RAZORPAY_KEY_ID,
      amount,
      currency,
      name: Company.NAME,
      description: Company.DESCRIPTION,
      image: gbi_home_logo,
      order_id,
      callback_url: `${process.env.REACT_APP_BASE_API}/payments/validate`,
      prefill: {
        name: user?.user?.name,
        email: user?.user?.email,
      },
      notes: {
        promotionId,
      },
      theme: {
        color: "#3399cc",
      },
    };

    let rzp1 = new (window as any).Razorpay(options);

    rzp1.on("payment.failed", function (response: any) {
      alert("Payment Failed. Please Retry again.");
      navigate("/featured/failed");
    });

    rzp1.open();
  };

  const handleCreate = async (values: promotionsCreate) => {
    try {
      handleSuccessModal(true, "LOADING", "");

      if (!files.imageFile) {
        return setFiles({ ...files, image: true });
      }
      setFiles({ ...files, image: false });

      let promotionImages: string[] = [];

      for (const file of files.imageFile) {
        const signedLogoData: presignedData = {
          fileName: file.name ?? values.title,
          filePath: FILE_PATH.PROMOTIONS_IMAGE,
          fileType: FILE_TYPE.PNG,
        };

        let imageUrl = await getPresigned(signedLogoData);
        await postLogo(imageUrl, file);
        promotionImages.push(imageUrl.split("?")[0]);
      }

      const data: promotionsCreate = {
        title: values.title,
        description: values.description,
        images: promotionImages,
      };

      const { order_id, currency, amount, promotionId } = await createPromotion(
        data
      );

      handleSuccessModal(false, "LOADING", "");

      paymentHandler(amount, currency, order_id, promotionId);
    } catch (err) {
      notify("Failed to create Featured!", "error");
    }
  };

  useEffect(() => {
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, []);

  const content = (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md z-50 flex items-center justify-center p-4 animate-fade-in-scale">
      <div className="bg-white rounded-3xl shadow-2xl max-w-3xl w-full max-h-[95vh] overflow-y-auto relative animate-slide-in-up">
        {/* Decorative Background Elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-GTI-BLUE-default/10 to-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-500/10 to-GTI-BLUE-default/10 rounded-full blur-2xl"></div>

        {/* Enhanced Modern Header */}
        <div className="relative z-10 flex items-center justify-between p-8 border-b border-gray-100">
          <div className="flex items-center space-x-4">
            <div className="relative group">
              <div className="absolute inset-0 bg-GTI-BLUE-default/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-gradient-to-br from-GTI-BLUE-default/10 to-purple-500/10 rounded-2xl p-4 border border-GTI-BLUE-default/20">
                <SparklesIcon className="h-8 w-8 text-GTI-BLUE-default" />
              </div>
            </div>
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-GTI-BLUE-default via-purple-600 to-blue-600 bg-clip-text text-transparent">
                Create Featured Content
              </h2>
              <p className="text-gray-600 text-base mt-1">
                Promote your content with custom details and reach global
                audiences
              </p>
            </div>
          </div>
          <button
            onClick={() => {
              changeModalState();
            }}
            className="group p-3 hover:bg-red-50 rounded-2xl transition-all duration-300 transform hover:scale-110 border border-gray-200 hover:border-red-200"
          >
            <AiOutlineClose className="w-6 h-6 text-gray-400 group-hover:text-red-500 transition-colors duration-300" />
          </button>
        </div>

        {/* Enhanced Form Content */}
        <div className="relative z-10 p-8">
          <Formik
            initialValues={initialValues}
            validationSchema={promotionsSchema}
            onSubmit={(values) => handleCreate(values)}
          >
            {({
              handleChange,
              setFieldValue,
              handleSubmit,
              errors,
              values,
            }) => {
              return (
                <>
                  <Form className="space-y-8">
                    {/* Enhanced Title Input */}
                    <div className="space-y-3">
                      <label className="flex items-center space-x-2 text-base font-semibold text-gray-800">
                        <StarIcon className="h-5 w-5 text-GTI-BLUE-default" />
                        <span>Featured Title</span>
                        <span className="text-red-500">*</span>
                      </label>
                      <div className="relative group">
                        <input
                          onChange={(e) =>
                            setFieldValue("title", e.target.value)
                          }
                          type="text"
                          className="modern-form-input w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-GTI-BLUE-default/20 focus:border-GTI-BLUE-default transition-all duration-300 bg-gradient-to-r from-gray-50 to-white focus:from-white focus:to-blue-50 placeholder-gray-400 text-gray-800 font-medium"
                          placeholder="Enter an engaging title for your featured content..."
                        />
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-GTI-BLUE-default/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      </div>
                      {errors.title && (
                        <div className="flex items-center space-x-2 text-red-600 bg-red-50 px-4 py-2 rounded-xl border border-red-200">
                          <ExclamationTriangleIcon className="h-4 w-4" />
                          <p className="text-sm font-medium">{errors.title}</p>
                        </div>
                      )}
                    </div>

                    {/* Enhanced Description Editor */}
                    <div className="space-y-3">
                      <label className="flex items-center space-x-2 text-base font-semibold text-gray-800">
                        <DocumentTextIcon className="h-5 w-5 text-GTI-BLUE-default" />
                        <span>Content Description</span>
                        <span className="text-red-500">*</span>
                      </label>
                      <div className="relative group">
                        <div className="border-2 border-gray-200 rounded-2xl overflow-hidden focus-within:ring-4 focus-within:ring-GTI-BLUE-default/20 focus-within:border-GTI-BLUE-default transition-all duration-300 bg-gradient-to-r from-gray-50 to-white focus-within:from-white focus-within:to-blue-50">
                          <CustomEditor
                            onChange={(content: string) => {
                              setFieldValue("description", content);
                            }}
                          />
                        </div>
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-GTI-BLUE-default/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      </div>
                      {errors.description && (
                        <div className="flex items-center space-x-2 text-red-600 bg-red-50 px-4 py-2 rounded-xl border border-red-200">
                          <ExclamationTriangleIcon className="h-4 w-4" />
                          <p className="text-sm font-medium">
                            {errors.description}
                          </p>
                        </div>
                      )}
                      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                        <p className="text-blue-700 text-sm font-medium">
                          💡 Tip: Write a compelling description that highlights
                          the unique value and benefits of your content to
                          attract more engagement.
                        </p>
                      </div>
                    </div>

                    {/* Enhanced File Upload */}
                    <div className="space-y-3">
                      <label className="flex items-center space-x-2 text-base font-semibold text-gray-800">
                        <PhotoIcon className="h-5 w-5 text-GTI-BLUE-default" />
                        <span>Featured Images</span>
                        <div className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-lg text-xs font-medium">
                          PNG only, max 3
                        </div>
                      </label>

                      <div className="relative group">
                        <div className="border-2 border-dashed border-gray-300 rounded-2xl p-8 text-center hover:border-GTI-BLUE-default transition-all duration-300 bg-gradient-to-br from-gray-50 to-white group-hover:from-blue-50 group-hover:to-purple-50">
                          <div className="space-y-4">
                            <div className="mx-auto w-16 h-16 bg-GTI-BLUE-default/10 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                              <PhotoIcon className="h-8 w-8 text-GTI-BLUE-default" />
                            </div>
                            <div>
                              <p className="text-lg font-semibold text-gray-700 mb-2">
                                Upload Featured Images
                              </p>
                              <p className="text-sm text-gray-500 mb-4">
                                Drag and drop your images here, or click to
                                browse
                              </p>
                              <div className="inline-flex items-center px-4 py-2 bg-GTI-BLUE-default text-white rounded-xl hover:bg-blue-700 transition-colors duration-200 font-medium">
                                Choose Files
                              </div>
                            </div>
                          </div>
                          <input
                            onChange={handleImage}
                            accept=".png"
                            type="file"
                            id="logo"
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            multiple
                            max={3}
                          />
                        </div>
                      </div>

                      {files.imageFile.length > 0 && (
                        <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                          <div className="flex items-center space-x-2 text-green-700 mb-2">
                            <CheckCircleIcon className="h-5 w-5" />
                            <span className="font-semibold">
                              Files Selected
                            </span>
                          </div>
                          <div className="space-y-2">
                            {files.imageFile.map((file, index) => (
                              <div
                                key={index}
                                className="flex items-center space-x-3 bg-white rounded-lg p-3 border border-green-200"
                              >
                                <PhotoIcon className="h-4 w-4 text-green-600" />
                                <span className="text-sm text-gray-700 font-medium">
                                  {file.name}
                                </span>
                                <span className="text-xs text-gray-500">
                                  ({(file.size / 1024 / 1024).toFixed(2)} MB)
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {files.image && (
                        <div className="flex items-center space-x-2 text-red-600 bg-red-50 px-4 py-2 rounded-xl border border-red-200">
                          <ExclamationTriangleIcon className="h-4 w-4" />
                          <p className="text-sm font-medium">
                            Please upload product images
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Enhanced Action Buttons */}
                    <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-8 border-t border-gray-100">
                      <button
                        type="button"
                        onClick={() => changeModalState()}
                        className="px-8 py-4 text-gray-700 bg-white border-2 border-gray-200 rounded-2xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-300 font-semibold transform hover:scale-105 flex items-center justify-center space-x-2"
                      >
                        <span>Cancel</span>
                      </button>
                      <button
                        type="submit"
                        onClick={() => handleSubmit}
                        className="px-8 py-4 bg-gradient-to-r from-GTI-BLUE-default to-blue-700 text-white rounded-2xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-bold shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center justify-center space-x-3 group relative overflow-hidden"
                      >
                        <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                        <SparklesIcon className="h-6 w-6 relative z-10 group-hover:rotate-12 transition-transform duration-300" />
                        <span className="relative z-10">
                          Create Featured Content
                        </span>
                      </button>
                    </div>
                  </Form>
                </>
              );
            }}
          </Formik>
        </div>
      </div>
    </div>
  );
  return ReactDOM.createPortal(content, document.body);
};

export default ProfilePromotionModal;
