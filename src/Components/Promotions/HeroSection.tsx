import React from "react";
import {
  StarIcon,
  SparklesIcon,
  CreditCardIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";

const HeroSection: React.FC = () => {
  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-GTI-BLUE-default via-blue-700 to-indigo-900">
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/3 rounded-full blur-2xl animate-ping"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-32">
        <div className="text-center">
          {/* Enhanced Icon with Animation */}
          <div className="flex justify-center items-center mb-8">
            <div className="relative group">
              <div className="absolute inset-0 bg-white/30 rounded-full blur-2xl group-hover:blur-3xl transition-all duration-500"></div>
              <div className="relative bg-white/15 backdrop-blur-sm rounded-3xl p-6 border border-white/30 group-hover:border-white/50 transition-all duration-300 transform group-hover:scale-110">
                <SparklesIcon className="h-16 w-16 md:h-20 md:w-20 text-white animate-pulse" />
              </div>
            </div>
          </div>

          {/* Enhanced Typography */}
          <div className="mb-8">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-extrabold text-white mb-6 tracking-tight">
              <span className="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                Featured
              </span>
              <br />
              <span className="text-blue-100">Content</span>
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-transparent via-white to-transparent mx-auto mb-6"></div>
          </div>

          <p className="text-xl md:text-2xl lg:text-3xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed font-light">
            Amplify your reach with our premium promotion platform.
            <span className="text-white font-medium">
              {" "}
              Get featured across global networks
            </span>{" "}
            and connect with your target audience like never before.
          </p>

          {/* Enhanced Feature Pills */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <div className="bg-white/15 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/30 hover:border-white/50 transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center space-x-2">
                <StarIcon className="h-5 w-5 text-yellow-300" />
                <span className="text-white font-medium">
                  Premium Placement
                </span>
              </div>
            </div>
            <div className="bg-white/15 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/30 hover:border-white/50 transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center space-x-2">
                <UserGroupIcon className="h-5 w-5 text-green-300" />
                <span className="text-white font-medium">
                  Global Audience
                </span>
              </div>
            </div>
            <div className="bg-white/15 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/30 hover:border-white/50 transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center space-x-2">
                <CreditCardIcon className="h-5 w-5 text-blue-300" />
                <span className="text-white font-medium">Secure Payment</span>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button className="bg-white text-GTI-BLUE-default px-8 py-4 rounded-2xl font-bold text-lg hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-white/20">
              Start Promoting Now
            </button>
            <button className="border-2 border-white/50 text-white px-8 py-4 rounded-2xl font-medium text-lg hover:bg-white/10 transition-all duration-300 transform hover:scale-105">
              Learn More
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
