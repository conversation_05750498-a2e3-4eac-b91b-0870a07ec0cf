import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Link, useParams, useNavigate } from "react-router-dom";
import axios from "axios";
import { useSelector } from "react-redux";
import ReactCountryFlag from "react-country-flag";
import Slider from "react-slick";

import SuccessModal from "./SuccessModal";
import companyLogo from "../../assests/banners/company_logo.png";
import gbi_home_logo from "../../assests/home/<USER>";
import { ScreenSpinner } from "../utils/loader";
import { notify } from "../../utils";
import { spinnerLoaderStop } from "../../store/actioncreators/loaderactions";
import RenderHTML from "../utils/RenderHTML";
import { RequestMethods } from "../../shared/RequestMethods";
import { PaymentStatus } from "../../store/reducer/userreducer";
import { VerificationStatus } from "../../shared/enum";
import { BiLinkExternal } from "react-icons/bi";
import {
  StarIcon,
  CalendarIcon,
  CreditCardIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { Company } from "../../shared/constants";
import { retryPromotionPayment } from "../../store/actioncreators/promotionsaction";
// Types are imported from store/type.d.ts globally

const Promotion = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  const dispatch: Dispatch<any> = useDispatch();
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);
  const user: USER = useSelector((state: STATE) => state.USER.USER);
  const navigate = useNavigate();

  let [promotion, setPromotion] = useState({
    _id: "",
    title: "",
    description: "",
    images: [],
    userId: "",
    productId: "",
    opportunityId: "",
    paymentStatus: "",
    verificationStatus: "",
    createdAt: "",
    __v: -1,
    users: {
      _id: "",
      fullName: "",
      email: "",
      phoneNumber: "",
      countryCode: "",
      referenceCode: "",
      isEmailVerified: false,
      isUserVerified: false,
      isRejected: false,
      password: "",
      userRole: -1,
      userType: "",
      companyId: "",
      follower: [],
      following: [],
      connections: [
        {
          connectionStatus: "",
          userId: "",
        },
      ],
      createdAt: "",
      __v: -1,
    },
    company: {
      _id: "",
      name: "",
      logo: "",
      description: "",
      address: "",
      website: "",
      country: "",
      companyTurnover: -1,
      companyId: "",
      typeAndSizeOfPartnersRequired: [],
      typesOfPartnershipConsidered: [],
      createdAt: "",
      developmentStage: "",
      iprStatus: [],
      __v: -1,
    },
  });

  let [successModal, setSuccessModal] = useState<boolean>(false);
  const [state, setState] = useState("LOADING");
  const [message, setMessage] = useState("");

  let { id } = useParams();
  let promotionId: string = id ? id : "";

  const handleSuccessModal = (
    isOpen: boolean,
    state: string,
    message: string
  ) => {
    setSuccessModal(isOpen);
    setState(state);
    setMessage(message);
  };

  const sidebar_carousal_settings = {
    dots: true,
    infinite: true,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    swipeToSlide: true,
    autoplay: true,
  };

  const loadPromotion = async (promotionId: string) => {
    const extoken: string =
      localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";
    let config = {
      method: RequestMethods.GET,
      url: `${process.env.REACT_APP_BASE_API}/promotions/${promotionId}`,
      headers: {
        Authorization: `Bearer ${extoken}`,
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setPromotion(response?.data?.promotion);

        dispatch(spinnerLoaderStop());
      })
      .catch(function (error) {
        dispatch(spinnerLoaderStop());
      });
  };

  const paymentHandler = async (
    amount: number,
    currency: string,
    order_id: string,
    promotionId: string
  ) => {
    let options = {
      key: process.env.REACT_APP_RAZORPAY_KEY_ID,
      amount,
      currency,
      name: Company.NAME,
      description: Company.DESCRIPTION,
      image: gbi_home_logo,
      order_id,
      callback_url: `${process.env.REACT_APP_BASE_API}/payments/validate`,
      prefill: {
        name: user?.user?.name,
        email: user?.user?.email,
      },
      notes: {
        promotionId,
      },
      theme: {
        color: "#3399cc",
      },
    };

    let rzp1 = new (window as any).Razorpay(options);

    rzp1.on("payment.failed", function (response: any) {
      alert("Payment Failed. Please Retry again.");
      navigate("/featured/failed");
    });

    rzp1.open();
  };

  const handleRetryPromotionPayment = async () => {
    try {
      handleSuccessModal(true, "LOADING", "");

      const { order_id, currency, amount } = await retryPromotionPayment({
        promotionId,
      });

      handleSuccessModal(false, "LOADING", "");

      paymentHandler(amount, currency, order_id, promotionId);
    } catch (err) {
      notify("Failed to create Featured!", "error");
    }
  };

  useEffect(() => {
    loadPromotion(promotionId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const DOC = new Date(promotion.createdAt);

  return (
    <React.Fragment>
      <div className="min-h-screen bg-gray-50">
        {spinner.SPINNER ? (
          <ScreenSpinner />
        ) : (
          <>
            {/* Hero Section */}
            <div className="relative bg-gradient-to-r from-GTI-BLUE-default via-blue-700 to-indigo-800 overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                <div className="flex items-center justify-center mb-6">
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                    <StarIcon className="h-12 w-12 text-white" />
                  </div>
                </div>
                <div className="text-center">
                  <h1 className="text-3xl md:text-5xl font-bold text-white mb-4 leading-tight">
                    {promotion.title}
                  </h1>
                  <div className="flex items-center justify-center space-x-2 text-blue-100 mb-6">
                    <CalendarIcon className="h-5 w-5" />
                    <span className="text-lg">
                      Posted on{" "}
                      {DOC.toLocaleString("default", {
                        month: "long",
                        day: "2-digit",
                        year: "numeric",
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Content Section */}
                <div className="lg:col-span-2 space-y-6">
                  {/* Content Description */}
                  <article className="bg-white rounded-3xl shadow-xl p-8 border border-gray-100">
                    <div className="prose prose-lg max-w-none">
                      <RenderHTML html={promotion.description} />
                    </div>
                  </article>

                  {/* Status Cards */}
                  <div className="space-y-4">
                    {/* Payment Status */}
                    {promotion.paymentStatus && (
                      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <CreditCardIcon className="h-6 w-6 text-GTI-BLUE-default" />
                            <span className="font-semibold text-gray-900">
                              Payment Status
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {promotion.paymentStatus ===
                            PaymentStatus.SUCCESS ? (
                              <CheckCircleIcon className="h-5 w-5 text-green-600" />
                            ) : (
                              <XCircleIcon className="h-5 w-5 text-red-600" />
                            )}
                            <span
                              className={`px-3 py-1 rounded-full text-sm font-medium ${
                                promotion.paymentStatus ===
                                PaymentStatus.SUCCESS
                                  ? "bg-green-100 text-green-800"
                                  : "bg-red-100 text-red-700"
                              }`}
                            >
                              {promotion.paymentStatus}
                            </span>
                          </div>
                        </div>

                        {/* Retry Payment Button */}
                        {(promotion.paymentStatus === PaymentStatus.PENDING ||
                          promotion.paymentStatus === PaymentStatus.FAILED) && (
                          <div className="mt-4">
                            <button
                              className="w-full bg-GTI-BLUE-default text-white py-3 px-4 rounded-xl hover:bg-blue-800 transition-all duration-200 font-medium flex items-center justify-center space-x-2"
                              onClick={() => handleRetryPromotionPayment()}
                            >
                              <CreditCardIcon className="h-5 w-5" />
                              <span>Retry Payment</span>
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                  {/* Approval Status */}
                  <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <ClockIcon className="h-6 w-6 text-GTI-BLUE-default" />
                        <span className="font-semibold text-gray-900">
                          Approval Status
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {promotion.verificationStatus ===
                        VerificationStatus.ACCEPTED ? (
                          <CheckCircleIcon className="h-5 w-5 text-green-600" />
                        ) : promotion.verificationStatus ===
                          VerificationStatus.PENDING ? (
                          <ClockIcon className="h-5 w-5 text-yellow-600" />
                        ) : (
                          <XCircleIcon className="h-5 w-5 text-red-600" />
                        )}
                        <span
                          className={`px-3 py-1 rounded-full text-sm font-medium ${
                            promotion.verificationStatus ===
                            VerificationStatus.PENDING
                              ? "bg-yellow-100 text-yellow-800"
                              : promotion.verificationStatus ===
                                VerificationStatus.ACCEPTED
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-700"
                          }`}
                        >
                          {promotion.verificationStatus}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Image Gallery */}
                  {promotion?.images?.length > 0 && (
                    <div className="bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100">
                      <div className="p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <StarIcon className="h-5 w-5 text-GTI-BLUE-default mr-2" />
                          Promotion Images
                        </h3>
                        <Slider
                          {...sidebar_carousal_settings}
                          className="rounded-2xl overflow-hidden"
                        >
                          {promotion.images.map((image, index) => (
                            <div key={index} className="relative aspect-video">
                              <img
                                className="w-full h-full object-cover rounded-2xl"
                                src={image}
                                alt={`${promotion.title} - Image ${index + 1}`}
                              />
                            </div>
                          ))}
                        </Slider>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      {successModal && (
        <SuccessModal
          state={state}
          message={message}
          show={successModal}
          toggle={handleSuccessModal}
        />
      )}
    </React.Fragment>
  );
};

export default Promotion;
