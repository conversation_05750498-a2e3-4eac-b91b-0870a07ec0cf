import React, { <PERSON><PERSON><PERSON>, useEffect, useState } from "react";
import { Helmet } from "react-helmet";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  StarIcon,
  SparklesIcon,
  CreditCardIcon,
} from "@heroicons/react/24/outline";

import featured_button from "../../assests/images/featured/featured-button.svg";
import featured from "../../assests/images/featured/featured.svg";
import personalised_promotion from "../../assests/images/personalised-promotion.svg";
import promote_profile from "../../assests/images/promote-profile.svg";
import { FEATURED, LIMIT, SKIP, metaData, title } from "../constants";
import globe from "../../assests/home/<USER>";
import SuccessModal from "./SuccessModal";
import { PROFILE_TYPES } from "../../shared/enum";
import { getPromotions } from "../../store/actioncreators/promotionsaction";

import SocialMediaDetails from "../utils/SocialMediaDetails";
import ProfilePromotionModal from "./ProfilePromotionModal";
import PromotionModal from "./PromotionModal";
import HeroSection from "./HeroSection";
import PromotionalPlatforms from "./PromotionalPlatforms";
// import RenderRazorpay from "../utils/Razorpay";

const Promotions = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  const navigate = useNavigate();
  const dispatch: Dispatch<any> = useDispatch();

  let [successModal, setSuccessModal] = useState<boolean>(false);
  const [state, setState] = useState("LOADING");
  const [message, setMessage] = useState("");

  const user: USER = useSelector((state: STATE) => state.USER.USER);

  const [socialMediaDetails, setSocialMediaDetails] = useState(false);
  const [socialMedia] = useState({
    logo: "",
    details: [
      {
        type: "",
        link: "",
        text: "",
      },
    ],
  });
  const [startPromotion, setStartPromomtion] = useState(false);
  const [promoteProfile, setPromoteProfile] = useState(false);
  const [personalisedPromote, setPersonalisedPromote] = useState(false);
  const promotions: PROMOTIONS_STATE = useSelector(
    (state: STATE) => state.PROMOTIONS
  );

  useEffect(() => {
    dispatch(getPromotions(SKIP, LIMIT));
  }, [dispatch]);

  const handleSuccessModal = (
    isOpen: boolean,
    state: string,
    message: string
  ) => {
    setSuccessModal(isOpen);
    setState(state);
    setMessage(message);
  };

  const changePromoteProfile = async () => {
    setPromoteProfile(!promoteProfile);
  };

  const changePersonalisedPromote = async () => {
    setPersonalisedPromote(!personalisedPromote);
  };

  const handleYourFeaturedView = () => {
    navigate(`/your-featured`);
  };

  return (
    <div className="flex flex-col relative w-full min-h-screen bg-gray-50">
      <Helmet>
        <title>{title.PROMOTIONS}</title>
        <meta
          name="description"
          key="description"
          content={metaData.PROMOTIONS}
        />
        <meta name="title" key="title" content={title?.PROMOTIONS} />
        <meta property="og:title" content={title.PROMOTIONS} />
        <meta property="og:description" content={metaData.PROMOTIONS} />
        <meta property="og:image" content={globe} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/technology`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content={title?.PROMOTIONS} />
        <meta name="twitter:description" content={metaData.PROMOTIONS} />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content={title?.PROMOTIONS} />
      </Helmet>

      <HeroSection />

      {/* Enhanced Content Section */}
      <div className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="flex flex-col lg:flex-row w-full items-start justify-center gap-16">
          {promoteProfile && (
            <PromotionModal
              changeModalState={changePromoteProfile}
              handleSuccessModal={handleSuccessModal}
            />
          )}
          {personalisedPromote && (
            <ProfilePromotionModal
              changeModalState={changePersonalisedPromote}
              handleSuccessModal={handleSuccessModal}
            />
          )}

          {/* Enhanced Main Content Card */}
          <div className="flex flex-col items-center lg:w-1/2">
            <div className="bg-white rounded-3xl shadow-2xl p-8 md:p-12 border border-gray-100 hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
              {/* Decorative Background */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-GTI-BLUE-default/5 to-purple-500/5 rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-500/5 to-GTI-BLUE-default/5 rounded-full blur-2xl"></div>

              <div className="relative z-10">
                <div className="text-center mb-10">
                  {/* Enhanced Icon */}
                  <div className="flex justify-center mb-8">
                    <div className="relative group">
                      <div className="absolute inset-0 bg-GTI-BLUE-default/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                      <div className="relative bg-gradient-to-br from-GTI-BLUE-default/10 to-purple-500/10 rounded-3xl p-6 border border-GTI-BLUE-default/20 group-hover:border-GTI-BLUE-default/40 transition-all duration-300">
                        <StarIcon className="h-16 w-16 text-GTI-BLUE-default group-hover:scale-110 transition-transform duration-300" />
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Typography */}
                  <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-GTI-BLUE-default to-gray-900 bg-clip-text text-transparent mb-6">
                    Get Featured
                  </h2>
                  <div className="w-16 h-1 bg-gradient-to-r from-GTI-BLUE-default to-purple-500 mx-auto mb-6 rounded-full"></div>
                  <p className="text-lg text-gray-600 leading-relaxed max-w-md mx-auto">
                    {FEATURED}
                  </p>
                </div>

                {/* Enhanced Feature Image for Mobile */}
                <div className="lg:hidden mb-10">
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-GTI-BLUE-default/20 to-purple-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <img
                      src={featured}
                      alt="Featured"
                      className="relative w-full rounded-3xl shadow-2xl group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                </div>

                {/* Enhanced Action Buttons */}
                {startPromotion ? (
                  <div className="space-y-4 w-full">
                    {user?.userType !== PROFILE_TYPES.GENERAL_SUBSCRIBER && (
                      <button
                        className="w-full bg-gradient-to-r from-GTI-BLUE-default to-blue-700 text-white py-4 px-6 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-3"
                        onClick={() => {
                          if (user?.admin !== -1) {
                            setStartPromomtion(false);
                            setPromoteProfile(true);
                            setPersonalisedPromote(false);
                          }
                        }}
                      >
                        <CreditCardIcon className="h-6 w-6" />
                        <span>Promote Profile</span>
                      </button>
                    )}
                    <button
                      className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white py-4 px-6 rounded-xl font-semibold text-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-3"
                      onClick={() => {
                        if (user?.admin !== -1) {
                          setStartPromomtion(false);
                          setPromoteProfile(false);
                          setPersonalisedPromote(true);
                        }
                      }}
                    >
                      <SparklesIcon className="h-6 w-6" />
                      <span>Personalized Promotion</span>
                    </button>
                  </div>
                ) : (
                  <div className="w-full">
                    {promotions?.promotionsCount ? (
                      <button
                        className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-4 px-6 rounded-xl font-semibold text-lg hover:from-green-700 hover:to-green-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-3"
                        onClick={() => {
                          if (user?.admin !== -1) {
                            handleYourFeaturedView();
                          } else {
                            handleLoginModal();
                          }
                        }}
                      >
                        <StarIcon className="h-6 w-6" />
                        <span>View Your Featured Content</span>
                      </button>
                    ) : null}
                    <button
                      className="w-full bg-gradient-to-r from-GTI-BLUE-default to-blue-700 text-white py-4 px-6 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-3 mt-4"
                      onClick={() => {
                        if (user?.admin !== -1) {
                          setStartPromomtion(true);
                          setPromoteProfile(false);
                          setPersonalisedPromote(false);
                        } else {
                          handleLoginModal();
                        }
                      }}
                    >
                      <SparklesIcon className="h-6 w-6" />
                      <span>Start Promotion</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Feature Image for Desktop */}
          <div className="lg:w-1/2 hidden lg:flex justify-center items-center">
            <img
              src={featured}
              alt="Featured"
              className="w-full max-w-lg rounded-3xl shadow-2xl"
            />
          </div>
        </div>

        <PromotionalPlatforms />
      </div>

      <div className="flex flex-col mb-10 justify-center items-center ml-5 mr-5 lg:ml-0 lg:mr-0">
        <h2 className="text-GTI-BLUE-default font-semibold text-3xl lg:text-4xl w-full">
          Let’s take your technology global
        </h2>
        {startPromotion ? (
          <div className="flex w-full justify-center items-center">
            {user?.userType !== PROFILE_TYPES.GENERAL_SUBSCRIBER && (
              <div
                className="bg-contain rounded-sm cursor-pointer w-full h-10 mt-5 bg-center"
                style={{
                  backgroundImage: `url(${promote_profile})`,
                  backgroundRepeat: "no-repeat",
                }}
                onClick={() => {
                  if (user?.admin !== -1) {
                    setStartPromomtion(false);
                    setPromoteProfile(true);
                    setPersonalisedPromote(false);
                  }
                }}
              ></div>
            )}
            <div
              className="bg-contain rounded-sm cursor-pointer w-full h-10 mt-5 bg-center"
              style={{
                backgroundImage: `url(${personalised_promotion})`,
                backgroundRepeat: "no-repeat",
              }}
              onClick={() => {
                if (user?.admin !== -1) {
                  setStartPromomtion(false);
                  setPromoteProfile(false);
                  setPersonalisedPromote(true);
                }
              }}
            ></div>
          </div>
        ) : (
          <div
            className="bg-contain rounded-sm cursor-pointer w-full h-10 mt-5 m-auto bg-center"
            style={{
              backgroundImage: `url(${featured_button})`,
              backgroundRepeat: "no-repeat",
            }}
            onClick={() => {
              if (user?.admin !== -1) {
                setStartPromomtion(true);
                setPromoteProfile(false);
                setPersonalisedPromote(false);
              } else {
                handleLoginModal();
              }
            }}
          ></div>
        )}
      </div>

      {successModal && (
        <SuccessModal
          state={state}
          message={message}
          show={successModal}
          toggle={handleSuccessModal}
        />
      )}

      {socialMediaDetails && (
        <SocialMediaDetails
          setSocialMediaDetails={setSocialMediaDetails}
          logo={socialMedia["logo"]}
          socialMediaDetails={socialMedia["details"]}
        />
      )}
    </div>
  );
};

export default Promotions;
