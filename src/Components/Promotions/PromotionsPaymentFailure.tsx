import React from "react";
import { useNavigate } from "react-router-dom";
import {
  XCircleIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon,
} from "@heroicons/react/24/outline";

import error from "../../assests/error.gif";

const PromotionsPaymentFailure = ({
  handleLoginModal,
}: {
  handleLoginModal: () => void;
}) => {
  let navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full">
        <div className="bg-white rounded-3xl shadow-2xl p-10 text-center border border-gray-100 relative overflow-hidden">
          {/* Decorative Background */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-500/10 to-orange-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-yellow-500/10 to-red-500/10 rounded-full blur-2xl"></div>

          <div className="relative z-10">
            {/* Enhanced Error Animation */}
            <div className="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-gradient-to-br from-red-100 to-red-200 mb-8 relative">
              <div className="absolute inset-0 bg-red-500/20 rounded-full animate-ping"></div>
              <div className="absolute inset-2 bg-red-500/10 rounded-full animate-pulse"></div>
              <XCircleIcon className="h-16 w-16 text-red-600 relative z-10" />
            </div>

            {/* Enhanced Error Message */}
            <div className="mb-10">
              <h2 className="text-4xl font-bold bg-gradient-to-r from-red-600 via-orange-600 to-yellow-600 bg-clip-text text-transparent mb-6">
                Payment Failed
              </h2>
              <div className="flex items-center justify-center mb-6">
                <div className="bg-red-500/10 rounded-2xl p-3 mr-3">
                  <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                </div>
                <span className="text-red-600 font-bold text-lg">
                  Transaction Unsuccessful
                </span>
              </div>
              <div className="w-16 h-1 bg-gradient-to-r from-red-500 to-orange-500 mx-auto mb-6 rounded-full"></div>
              <p className="text-gray-600 leading-relaxed text-lg mb-6">
                We're sorry, but your payment could not be processed. Please
                check your payment details and try again, or contact our support
                team if the issue persists.
              </p>

              {/* Help Options */}
              <div className="bg-blue-50 rounded-2xl p-6 border border-blue-200/50">
                <ChatBubbleLeftRightIcon className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                <p className="text-blue-700 font-medium">Need Help?</p>
                <p className="text-blue-600 text-sm">
                  Contact our support team for assistance
                </p>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="space-y-4">
              <button
                onClick={() => navigate("/featured")}
                type="button"
                className="w-full bg-gradient-to-r from-GTI-BLUE-default to-blue-700 text-white font-bold py-5 px-8 rounded-2xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center justify-center space-x-3 group"
              >
                <ArrowPathIcon className="h-6 w-6 group-hover:rotate-180 transition-transform duration-500" />
                <span>Try Again</span>
              </button>
              <div className="grid grid-cols-2 gap-4">
                <button
                  onClick={() => navigate("/")}
                  type="button"
                  className="bg-white text-gray-700 font-semibold py-3 px-4 rounded-2xl border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-300 transform hover:scale-105"
                >
                  Back to Home
                </button>
                <button
                  onClick={() =>
                    window.open(
                      "mailto:<EMAIL>",
                      "_blank"
                    )
                  }
                  type="button"
                  className="bg-blue-50 text-blue-700 font-semibold py-3 px-4 rounded-2xl border-2 border-blue-200 hover:border-blue-300 hover:bg-blue-100 transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
                >
                  <ChatBubbleLeftRightIcon className="h-4 w-4" />
                  <span>Support</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionsPaymentFailure;
