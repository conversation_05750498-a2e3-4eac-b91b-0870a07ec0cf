import React from "react";
import { useNavigate } from "react-router-dom";
import {
  CheckCircleIcon,
  SparklesIcon,
  StarIcon,
  ArrowRightIcon,
} from "@heroicons/react/24/outline";

import success from "../../assests/success.gif";

const PromotionsPaymentSuccess = ({
  handleLoginModal,
}: {
  handleLoginModal: () => void;
}) => {
  let navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full">
        <div className="bg-white rounded-3xl shadow-2xl p-10 text-center border border-gray-100 relative overflow-hidden">
          {/* Decorative Background */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-500/10 to-green-500/10 rounded-full blur-2xl"></div>

          <div className="relative z-10">
            {/* Enhanced Success Animation */}
            <div className="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-gradient-to-br from-green-100 to-green-200 mb-8 relative">
              <div className="absolute inset-0 bg-green-500/20 rounded-full animate-ping"></div>
              <div className="absolute inset-2 bg-green-500/10 rounded-full animate-pulse"></div>
              <CheckCircleIcon className="h-16 w-16 text-green-600 relative z-10" />
            </div>

            {/* Enhanced Success Message */}
            <div className="mb-10">
              <h2 className="text-4xl font-bold bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
                Payment Successful!
              </h2>
              <div className="flex items-center justify-center mb-6">
                <div className="bg-GTI-BLUE-default/10 rounded-2xl p-3 mr-3">
                  <SparklesIcon className="h-6 w-6 text-GTI-BLUE-default" />
                </div>
                <span className="text-GTI-BLUE-default font-bold text-lg">
                  Featured Content Created
                </span>
              </div>
              <div className="w-16 h-1 bg-gradient-to-r from-green-500 to-blue-500 mx-auto mb-6 rounded-full"></div>
              <p className="text-gray-600 leading-relaxed text-lg">
                Thank you for your payment! Your featured content has been
                created successfully and will be reviewed by our team. We'll
                notify you once it's approved and live on our platform.
              </p>

              {/* Success Features */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
                <div className="bg-green-50 rounded-2xl p-4 border border-green-200/50">
                  <StarIcon className="h-6 w-6 text-green-600 mx-auto mb-2" />
                  <p className="text-green-700 font-medium text-sm">
                    Premium Placement
                  </p>
                </div>
                <div className="bg-blue-50 rounded-2xl p-4 border border-blue-200/50">
                  <SparklesIcon className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                  <p className="text-blue-700 font-medium text-sm">
                    Global Exposure
                  </p>
                </div>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="space-y-4">
              <button
                onClick={() => navigate("/featured")}
                type="button"
                className="w-full bg-gradient-to-r from-GTI-BLUE-default to-blue-700 text-white font-bold py-5 px-8 rounded-2xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 flex items-center justify-center space-x-3 group"
              >
                <SparklesIcon className="h-6 w-6 group-hover:scale-110 transition-transform duration-300" />
                <span>View Featured Content</span>
                <ArrowRightIcon className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
              </button>
              <button
                onClick={() => navigate("/")}
                type="button"
                className="w-full bg-white text-GTI-BLUE-default font-semibold py-4 px-6 rounded-2xl border-2 border-gray-200 hover:border-GTI-BLUE-default hover:bg-blue-50 transition-all duration-300 transform hover:scale-105"
              >
                Back to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionsPaymentSuccess;
