import React, { Dispatch, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  StarIcon,
  CalendarIcon,
  EyeIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  SparklesIcon,
} from "@heroicons/react/24/outline";

import { getPromotions } from "../../store/actioncreators/promotionsaction";
import { ScreenSpinner } from "../utils/loader";
import { LIMIT, PROMOTIONS, SKIP, promotionsType } from "../constants";
import { spinnerLoaderStart } from "../../store/actioncreators/loaderactions";
import { VerificationStatus } from "../../shared/enum";
import RenderHTML from "../utils/RenderHTML";
import { getQueryParams } from "../../utils";

const Card = ({ item }: { item: promotionsType }) => {
  const dispatch: Dispatch<any> = useDispatch();
  const navigate = useNavigate();

  const handleView = () => {
    dispatch(spinnerLoaderStart());
    navigate(`/featured/${item._id}`, { state: { product: item } });
  };

  const DOP = new Date(item.createdAt);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case VerificationStatus.ACCEPTED:
        return <CheckCircleIcon className="h-4 w-4" />;
      case VerificationStatus.PENDING:
        return <ClockIcon className="h-4 w-4" />;
      default:
        return <XCircleIcon className="h-4 w-4" />;
    }
  };

  const getStatusStyles = (status: string) => {
    switch (status) {
      case VerificationStatus.ACCEPTED:
        return "bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300";
      case VerificationStatus.PENDING:
        return "bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 border border-yellow-300";
      default:
        return "bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300";
    }
  };

  return (
    <div
      className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 cursor-pointer border border-gray-100 overflow-hidden relative"
      onClick={() => {
        handleView();
      }}
    >
      {/* Decorative Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-GTI-BLUE-default/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      <div className="relative z-10">
        {/* Enhanced Image Section */}
        <div className="relative overflow-hidden rounded-t-3xl">
          <img
            src={item.images[0]}
            className="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500"
            alt={item.title}
            loading="lazy"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

          {/* Featured Badge */}
          <div className="absolute top-4 left-4">
            <div className="bg-GTI-BLUE-default/90 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center space-x-1">
              <StarIcon className="h-3 w-3" />
              <span>Featured</span>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="p-6">
          {/* Date and Status Row */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2 text-gray-500">
              <CalendarIcon className="h-4 w-4" />
              <span className="text-sm font-medium">
                {DOP.toLocaleString("default", {
                  month: "short",
                  day: "2-digit",
                  year: "numeric",
                })}
              </span>
            </div>
            <div
              className={`inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full ${getStatusStyles(
                item.verificationStatus
              )}`}
            >
              {getStatusIcon(item.verificationStatus)}
              <span className="ml-1">{item.verificationStatus}</span>
            </div>
          </div>

          {/* Title */}
          <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-GTI-BLUE-default transition-colors duration-300">
            {item.title.length > 30
              ? item.title.substring(0, 30) + "..."
              : item.title}
          </h3>

          {/* Description */}
          <div className="text-gray-600 text-sm leading-relaxed mb-4">
            {item.description.length > 120 ? (
              <RenderHTML html={item.description.substring(0, 120) + "..."} />
            ) : (
              <RenderHTML html={item.description.substring(0, 120)} />
            )}
          </div>

          {/* Action Button */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-GTI-BLUE-default">
              <EyeIcon className="h-4 w-4" />
              <span className="text-sm font-medium">View Details</span>
            </div>
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <SparklesIcon className="h-5 w-5 text-GTI-BLUE-default" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PromotionsList = ({
  handleLoginModal,
}: {
  handleLoginModal: () => void;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);
  const promotions: PROMOTIONS_STATE = useSelector(
    (state: STATE) => state.PROMOTIONS
  );

  const navigator = useNavigate();

  const [page, setPage] = useState({
    skip: getQueryParams("skip") ? getQueryParams("skip") : SKIP,
    limit: LIMIT,
  });
  const [maxSkip, setMaxSkip] = useState(0);

  const fetchData = (value: number) => {
    let final =
      parseInt(page.skip) + value < 0
        ? parseInt(page.skip)
        : parseInt(page.skip) + value;
    setPage({ skip: final.toString(), limit: page.limit });
    navigator(PROMOTIONS + `?skip=${final}`);
    window.scrollTo(0, 0);
  };

  useEffect(() => {
    setMaxSkip(Math.ceil(promotions.promotionsCount / parseInt(LIMIT)));
  }, [page, promotions]);

  useEffect(() => {
    dispatch(getPromotions(page.skip, page.limit));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page.skip, page.limit]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="bg-GTI-BLUE-default/10 rounded-2xl p-4">
              <StarIcon className="h-12 w-12 text-GTI-BLUE-default" />
            </div>
          </div>
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-GTI-BLUE-default via-purple-600 to-blue-600 bg-clip-text text-transparent mb-6">
            Your Featured Content
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-GTI-BLUE-default to-purple-500 mx-auto mb-6 rounded-full"></div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Manage and track your promoted content across our global platform
            network
          </p>
        </div>

        {/* Content Section */}
        {spinner.SPINNER ? (
          <div className="flex justify-center items-center py-20">
            <ScreenSpinner />
          </div>
        ) : promotions?.promotionsCount > 0 ? (
          <>
            {/* Stats Bar */}
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="bg-GTI-BLUE-default/10 rounded-xl p-3">
                    <SparklesIcon className="h-6 w-6 text-GTI-BLUE-default" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Total Featured Content
                    </h3>
                    <p className="text-gray-600">
                      Actively promoting your innovations
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-GTI-BLUE-default">
                    {promotions.promotionsCount}
                  </div>
                  <div className="text-sm text-gray-500">Items</div>
                </div>
              </div>
            </div>

            {/* Enhanced Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {promotions?.promotions.map(
                (item: promotionsType, id: number) => {
                  return <Card item={item} key={id} />;
                }
              )}
            </div>
          </>
        ) : (
          /* Enhanced Empty State */
          <div className="text-center py-20">
            <div className="bg-white rounded-3xl shadow-xl p-12 max-w-md mx-auto border border-gray-100">
              <div className="bg-gray-100 rounded-full p-6 w-24 h-24 mx-auto mb-6">
                <SparklesIcon className="h-12 w-12 text-gray-400 mx-auto" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                No Featured Content Yet
              </h3>
              <p className="text-gray-600 mb-8 leading-relaxed">
                Start promoting your innovations and reach a global audience
                with our featured content system.
              </p>
              <button
                onClick={() => navigator("/featured")}
                className="bg-gradient-to-r from-GTI-BLUE-default to-blue-700 text-white px-8 py-4 rounded-2xl font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                Create Featured Content
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PromotionsList;
