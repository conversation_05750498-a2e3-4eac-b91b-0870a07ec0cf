import React, { useEffect, useState } from "react";
import ReactD<PERSON> from "react-dom";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { Form, Formik } from "formik";
import { AiOutlineClose } from "react-icons/ai";
import { useSelector } from "react-redux";
import {
  SparklesIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

import {
  LIMIT,
  SKIP,
  promotionsCreate,
  promotionsProfileCreate,
} from "../constants";
import { notify } from "../../utils";
import { Company } from "../../shared/constants";
import gbi_home_logo from "../../assests/home/<USER>";
import { PROFILE_TYPES } from "../../shared/enum";
import { createPromotion } from "../../store/actioncreators/promotionsaction";

interface PromotionModalProps {
  changeModalState: () => void;
  handleSuccessModal: (isOpen: boolean, state: string, message: string) => void;
}

const PromotionModal: React.FC<PromotionModalProps> = ({
  changeModalState,
  handleSuccessModal,
}) => {
  const user: USER = useSelector((state: STATE) => state.USER.USER);
  const navigate = useNavigate();

  useEffect(() => {
    if (user.id && user.userType === PROFILE_TYPES.GENERAL_SUBSCRIBER) {
      notify("Unauthorized", "error");
      navigate("/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const [products, setProducts] = useState([]);
  const [opportunities, setOpportunities] = useState([]);

  const initialValues: promotionsProfileCreate = {
    id: "",
  };

  const getProducts = () => {
    const token = localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .get(
        `${process.env.REACT_APP_BASE_API}/products/user/${user?.id}?skip=${SKIP}&limit=${LIMIT}`,
        config
      )
      .then(function (response) {
        setProducts(response?.data?.products);
        initialValues.id = response?.data?.products[0]._id;
      })
      .catch(function (error) {
        setProducts([]);
      });
  };

  const getOpportunities = () => {
    const token = localStorage.getItem("GTI_data")?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .get(
        `${process.env.REACT_APP_BASE_API}/opportunities/user/${user?.id}?skip=${SKIP}&limit=${LIMIT}`,
        config
      )
      .then(function (response) {
        setOpportunities(response?.data?.opportunities);
        initialValues.id = response?.data?.opportunities[0]._id;
      })
      .catch(function (error) {
        setOpportunities([]);
      });
  };

  useEffect(() => {
    if (user?.userType === PROFILE_TYPES.DISPLAYER) {
      getProducts();
    } else if (user?.userType === PROFILE_TYPES.SCOUTER) {
      getOpportunities();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  const paymentHandler = async (
    amount: number,
    currency: string,
    order_id: string,
    promotionId: string
  ) => {
    let options = {
      key: process.env.REACT_APP_RAZORPAY_KEY_ID,
      amount,
      currency,
      name: Company.NAME,
      description: Company.DESCRIPTION,
      image: gbi_home_logo,
      order_id,
      callback_url: `${process.env.REACT_APP_BASE_API}/payments/validate`,
      prefill: {
        name: user?.user?.name,
        email: user?.user?.email,
      },
      notes: {
        promotionId,
      },
      theme: {
        color: "#3399cc",
      },
    };

    let rzp1 = new (window as any).Razorpay(options);

    rzp1.on("payment.failed", function (response: any) {
      alert("Payment Failed. Please Retry again.");
      navigate("/featured/failed");
    });

    rzp1.open();
  };

  const handleCreate = async (values: { id: string }) => {
    try {
      handleSuccessModal(true, "LOADING", "");

      const details: any[] =
        user?.userType === PROFILE_TYPES.DISPLAYER ? products : opportunities;

      const itemDetails = details.find((item) => item._id === values.id);

      const data: promotionsCreate = {
        title:
          user?.userType === PROFILE_TYPES.DISPLAYER
            ? itemDetails?.name
            : itemDetails?.company?.name,
        description: itemDetails.description,
        images: itemDetails?.images?.length
          ? itemDetails?.images
          : itemDetails?.image
          ? [itemDetails?.image]
          : [itemDetails?.company?.logo],
        productId:
          user?.userType === PROFILE_TYPES.DISPLAYER ? itemDetails?._id : null,
        opportunityId:
          user?.userType === PROFILE_TYPES.SCOUTER ? itemDetails?._id : null,
      };

      const { order_id, currency, amount, promotionId } = await createPromotion(
        data
      );

      handleSuccessModal(false, "", "");
      changeModalState();
      paymentHandler(amount, currency, order_id, promotionId);
    } catch (error) {
      handleSuccessModal(true, "ERROR", "Something went wrong");
    }
  };

  const content = (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md z-50 flex items-center justify-center p-4 animate-fade-in-scale">
      <div className="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-y-auto relative animate-slide-in-up">
        {/* Decorative Background Elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-GTI-BLUE-default/10 to-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-500/10 to-GTI-BLUE-default/10 rounded-full blur-2xl"></div>

        {/* Enhanced Modern Header */}
        <div className="relative z-10 flex items-center justify-between p-8 border-b border-gray-100">
          <div className="flex items-center space-x-4">
            <div className="relative group">
              <div className="absolute inset-0 bg-GTI-BLUE-default/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-gradient-to-br from-GTI-BLUE-default/10 to-purple-500/10 rounded-2xl p-4 border border-GTI-BLUE-default/20 group-hover:border-GTI-BLUE-default/40 transition-all duration-300">
                <SparklesIcon className="h-8 w-8 text-GTI-BLUE-default group-hover:scale-110 transition-transform duration-300" />
              </div>
            </div>
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-GTI-BLUE-default to-purple-600 bg-clip-text text-transparent">
                Promote Content
              </h2>
              <p className="text-gray-600 mt-1">
                Select from your existing technologies or opportunities to
                feature
              </p>
            </div>
          </div>
          <button
            onClick={changeModalState}
            className="group p-3 hover:bg-red-50 rounded-2xl transition-all duration-300 transform hover:scale-110 border border-gray-200 hover:border-red-200"
          >
            <AiOutlineClose className="w-6 h-6 text-gray-400 group-hover:text-red-500 transition-colors duration-300" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="p-8">
          <Formik
            initialValues={initialValues}
            onSubmit={(values) => {
              console.log({ values });
              handleCreate(values);
            }}
          >
            {() => {
              return (
                <Form className="space-y-8">
                  {/* Content Selection Section */}
                  <div className="space-y-4">
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        Choose Content to Promote
                      </h3>
                      <p className="text-gray-600">
                        Select from your existing{" "}
                        {user?.userType === PROFILE_TYPES?.DISPLAYER
                          ? "technologies"
                          : "opportunities"}{" "}
                        to create featured content
                      </p>
                    </div>

                    {/* Enhanced Action Button */}
                    {products?.length || opportunities?.length ? (
                      <div className="pt-8 border-t border-gray-100">
                        <button
                          type="submit"
                          className="relative w-full bg-gradient-to-r from-GTI-BLUE-default via-blue-600 to-purple-600 text-white py-4 px-8 rounded-2xl font-bold text-lg hover:from-blue-700 hover:via-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl overflow-hidden group"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                          <span className="relative z-10">
                            Create Featured Content
                          </span>
                        </button>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-6">
                          <ExclamationTriangleIcon className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                          <p className="text-yellow-800 font-semibold">
                            Please create{" "}
                            {user?.userType === PROFILE_TYPES?.DISPLAYER
                              ? "technologies"
                              : "opportunities"}{" "}
                            first to promote them.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </Form>
              );
            }}
          </Formik>
        </div>
      </div>
    </div>
  );

  return ReactDOM.createPortal(content, document.body);
};

export default PromotionModal;
