/* Enhanced Featured Content Display Animations */

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-scale {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-slide-in-up {
  animation: slide-in-up 0.8s ease-out;
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.6s ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Enhanced Prose Styling for Content */
.prose-blue {
  --tw-prose-links: #2563eb;
  --tw-prose-headings: #1e293b;
  --tw-prose-bold: #1e293b;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
}

.prose-blue h1,
.prose-blue h2,
.prose-blue h3,
.prose-blue h4,
.prose-blue h5,
.prose-blue h6 {
  color: var(--tw-prose-headings);
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose-blue p {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
  line-height: 1.75;
}

.prose-blue a {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
  transition: color 0.2s ease;
}

.prose-blue a:hover {
  color: #1d4ed8;
}

.prose-blue strong {
  color: var(--tw-prose-bold);
  font-weight: 600;
}

.prose-blue ul,
.prose-blue ol {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
  padding-left: 1.625rem;
}

.prose-blue li {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.prose-blue blockquote {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-left-width: 0.25rem;
  border-left-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6rem;
  margin-bottom: 1.6rem;
  padding-left: 1rem;
}

.prose-blue code {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875rem;
  background-color: #f3f4f6;
  padding: 0.25rem 0.375rem;
  border-radius: 0.375rem;
}

.prose-blue pre {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding: 0.8571429em 1.1428571em;
}

/* Custom Shadow Classes */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .animate-slide-in-up {
    animation-duration: 0.6s;
  }
  
  .animate-fade-in-scale {
    animation-duration: 0.4s;
  }
}

/* Print Styles */
@media print {
  .animate-float,
  .animate-glow,
  .animate-slide-in-up,
  .animate-fade-in-scale,
  .animate-shimmer {
    animation: none;
  }
}
