/* Modern Footer Styles */

/* Main Footer Container */
.modern-footer {
  @apply relative w-full overflow-hidden;
  background: linear-gradient(
    135deg,
    #0f172a 0%,
    #1e293b 25%,
    #334155 50%,
    #475569 75%,
    #64748b 100%
  );
  position: relative;
}

.modern-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(147, 197, 253, 0.05) 50%,
    transparent 100%
  );
  z-index: 1;
}

.modern-footer::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.5) 50%,
    transparent 100%
  );
  z-index: 2;
}

/* Footer Content Container */
.footer-content {
  @apply relative z-10 max-w-7xl mx-auto px-6 py-16;
}

/* Footer Top Section */
.footer-top {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16;
}

/* Enhanced Brand Section */
.footer-brand {
  @apply lg:col-span-1 space-y-6 relative;
}

.footer-brand::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(147, 197, 253, 0.02) 100%
  );
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.footer-brand:hover::before {
  opacity: 1;
}

.footer-logo-container {
  @apply flex items-center space-x-3 mb-6 relative;
}

.footer-logo {
  @apply h-16 w-auto transition-all duration-500 hover:scale-110 hover:brightness-110;
  filter: brightness(1.2) contrast(1.1)
    drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
  animation: logoGlow 4s ease-in-out infinite;
}

.footer-logo:hover {
  filter: brightness(1.4) contrast(1.2)
    drop-shadow(0 8px 16px rgba(59, 130, 246, 0.4));
  transform: scale(1.1) rotate(2deg);
}

@keyframes logoGlow {
  0%,
  100% {
    filter: brightness(1.2) contrast(1.1)
      drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
  }
  50% {
    filter: brightness(1.3) contrast(1.15)
      drop-shadow(0 6px 12px rgba(59, 130, 246, 0.3));
  }
}

.footer-brand-text {
  @apply text-white/90 text-base leading-relaxed font-roboto mb-6 relative;
  line-height: 1.7;
  transition: all 0.3s ease;
}

.footer-brand-text:hover {
  @apply text-white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.footer-tagline {
  @apply text-2xl font-bold text-white mb-4 font-roboto relative;
  background: linear-gradient(135deg, #ffffff 0%, #93c5fd 50%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Enhanced Social Media Section */
.footer-social {
  @apply flex space-x-4 relative;
}

.footer-social::before {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(
    45deg,
    rgba(59, 130, 246, 0.1),
    rgba(147, 197, 253, 0.05),
    rgba(99, 102, 241, 0.1)
  );
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.footer-social:hover::before {
  opacity: 1;
}

.social-icon {
  @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-500 cursor-pointer relative overflow-hidden;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.social-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.social-icon:hover::before {
  left: 100%;
}

.social-icon:hover {
  @apply transform scale-110 -translate-y-2;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.4) 0%,
    rgba(147, 197, 253, 0.3) 50%,
    rgba(99, 102, 241, 0.2) 100%
  );
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4),
    0 0 20px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.6);
  animation: socialPulse 0.6s ease-out;
}

@keyframes socialPulse {
  0% {
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.2);
  }
  50% {
    box-shadow: 0 15px 45px rgba(59, 130, 246, 0.6),
      0 0 30px rgba(59, 130, 246, 0.4);
  }
  100% {
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.2);
  }
}

.social-icon svg {
  @apply w-6 h-6 text-white/80 transition-all duration-300 relative z-10;
}

.social-icon:hover svg {
  @apply text-white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transform: scale(1.1);
}

/* Enhanced Navigation Sections */
.footer-nav-section {
  @apply space-y-6 relative;
  transition: all 0.3s ease;
}

.footer-nav-section::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.03) 0%,
    rgba(147, 197, 253, 0.01) 100%
  );
  border-radius: 15px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.footer-nav-section:hover::before {
  opacity: 1;
}

.footer-nav-title {
  @apply text-xl font-bold text-white mb-6 font-roboto relative;
  background: linear-gradient(135deg, #ffffff 0%, #93c5fd 50%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: titleGradient 4s ease-in-out infinite;
}

@keyframes titleGradient {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.footer-nav-title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #8b5cf6);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.footer-nav-section:hover .footer-nav-title::after {
  width: 60px;
}

.footer-nav-list {
  @apply space-y-4;
}

.footer-nav-item {
  @apply block text-white/70 hover:text-white transition-all duration-300 font-roboto text-base relative;
  position: relative;
  padding-left: 0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.footer-nav-item::before {
  content: "";
  position: absolute;
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #8b5cf6);
  transition: width 0.3s ease;
  border-radius: 1px;
}

.footer-nav-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.footer-nav-item:hover::before {
  width: 12px;
}

.footer-nav-item:hover::after {
  left: 100%;
}

.footer-nav-item:hover {
  @apply transform translate-x-3;
  color: #93c5fd;
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* Enhanced Footer Bottom Section */
.footer-bottom {
  @apply border-t pt-8 mt-16 relative;
  border-image: linear-gradient(
      90deg,
      transparent,
      rgba(59, 130, 246, 0.3),
      transparent
    )
    1;
  border-top: 1px solid;
}

.footer-bottom::before {
  content: "";
  position: absolute;
  top: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.5) 50%,
    transparent 100%
  );
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

.footer-bottom-content {
  @apply flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0 relative;
}

.footer-copyright {
  @apply text-white/60 text-sm font-roboto text-center lg:text-left transition-all duration-300;
}

.footer-copyright:hover {
  @apply text-white/80;
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.footer-legal-links {
  @apply flex flex-wrap items-center justify-center lg:justify-end space-x-6 relative;
}

.footer-legal-links::before {
  content: "";
  position: absolute;
  top: -5px;
  left: -10px;
  right: -10px;
  bottom: -5px;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(147, 197, 253, 0.02) 100%
  );
  border-radius: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.footer-legal-links:hover::before {
  opacity: 1;
}

.footer-legal-link {
  @apply text-white/60 hover:text-white text-sm font-roboto transition-all duration-300 relative;
  position: relative;
}

.footer-legal-link::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  transition: width 0.3s ease;
}

.footer-legal-link:hover::after {
  width: 100%;
}

.footer-legal-link:hover {
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.footer-legal-separator {
  @apply text-white/40 transition-colors duration-300;
}

.footer-legal-links:hover .footer-legal-separator {
  @apply text-white/60;
}

/* Enhanced Newsletter Section */
.footer-newsletter {
  @apply lg:col-span-1 space-y-6 relative;
}

.footer-newsletter::before {
  content: "";
  position: absolute;
  top: -15px;
  left: -15px;
  right: -15px;
  bottom: -15px;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.08) 0%,
    rgba(147, 197, 253, 0.04) 100%
  );
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.footer-newsletter:hover::before {
  opacity: 1;
}

.newsletter-title {
  @apply text-xl font-bold text-white mb-4 font-roboto relative;
  background: linear-gradient(135deg, #ffffff 0%, #93c5fd 50%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: titleGradient 4s ease-in-out infinite;
}

.newsletter-title::after {
  content: "✨";
  position: absolute;
  right: -30px;
  top: 0;
  font-size: 1.2rem;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.newsletter-description {
  @apply text-white/70 text-base font-roboto mb-6 leading-relaxed;
  transition: color 0.3s ease;
}

.footer-newsletter:hover .newsletter-description {
  @apply text-white/90;
}

.newsletter-form {
  @apply flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 relative;
}

.newsletter-input {
  @apply flex-1 px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent backdrop-blur-sm transition-all duration-300 relative;
  position: relative;
  overflow: hidden;
}

.newsletter-input::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.newsletter-input:focus::before {
  left: 100%;
}

.newsletter-input:focus {
  @apply bg-white/15 border-blue-400/50;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  transform: scale(1.02);
}

.newsletter-button {
  @apply px-6 py-3 rounded-xl font-bold text-white transition-all duration-300 font-roboto relative overflow-hidden;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  position: relative;
}

.newsletter-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.newsletter-button:hover::before {
  left: 100%;
}

.newsletter-button:hover {
  @apply transform scale-105 shadow-xl;
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  animation: buttonPulse 0.6s ease-out;
}

@keyframes buttonPulse {
  0% {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.6);
  }
  100% {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  }
}

/* Enhanced Responsive Design */

/* Large Tablets and Small Desktops */
@media (max-width: 1024px) {
  .footer-content {
    @apply px-6 py-14;
  }

  .footer-top {
    @apply grid-cols-2 gap-10 mb-14;
  }

  .footer-brand {
    @apply col-span-2;
  }

  .footer-newsletter {
    @apply col-span-2;
  }
}

/* Tablets */
@media (max-width: 768px) {
  .footer-content {
    @apply px-4 py-12;
  }

  .footer-top {
    @apply grid-cols-1 gap-8 mb-12;
  }

  .footer-brand {
    @apply text-center;
  }

  .footer-brand::before {
    @apply rounded-3xl;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
  }

  .footer-social {
    @apply justify-center space-x-6;
  }

  .social-icon {
    @apply w-14 h-14;
    /* Larger touch targets for mobile */
  }

  .social-icon svg {
    @apply w-7 h-7;
  }

  .footer-nav-title {
    @apply text-center;
  }

  .footer-nav-title::after {
    @apply left-1/2 transform -translate-x-1/2;
  }

  .footer-nav-list {
    @apply text-center space-y-5;
  }

  .footer-nav-item {
    @apply text-lg py-2;
    /* Larger touch targets */
  }

  .footer-nav-item::before {
    display: none;
  }

  .footer-nav-item:hover {
    @apply transform-none;
  }

  .footer-nav-item:hover::after {
    left: 0;
    width: 100%;
  }

  .newsletter-form {
    @apply flex-col space-y-4 space-x-0;
  }

  .newsletter-input {
    @apply py-4 text-lg;
    /* Better mobile input experience */
  }

  .newsletter-button {
    @apply py-4 text-lg;
    /* Consistent button sizing */
  }

  .footer-bottom-content {
    @apply flex-col space-y-4;
  }

  .footer-legal-links {
    @apply flex-col space-y-3 space-x-0;
  }

  .footer-legal-separator {
    display: none;
  }
}

/* Mobile Phones */
@media (max-width: 640px) {
  .footer-content {
    @apply px-3 py-10;
  }

  .footer-top {
    @apply gap-6 mb-10;
  }

  .footer-logo {
    @apply h-12;
  }

  .footer-tagline {
    @apply text-xl;
  }

  .footer-brand-text {
    @apply text-sm leading-relaxed;
  }

  .footer-nav-title {
    @apply text-lg;
  }

  .footer-nav-item {
    @apply text-base;
  }

  .social-icon {
    @apply w-12 h-12;
  }

  .social-icon svg {
    @apply w-6 h-6;
  }

  .newsletter-title {
    @apply text-lg;
  }

  .newsletter-description {
    @apply text-sm;
  }

  .newsletter-input {
    @apply py-3 text-base;
  }

  .newsletter-button {
    @apply py-3 px-4;
  }

  .footer-copyright {
    @apply text-xs;
  }

  .footer-legal-link {
    @apply text-xs py-2;
  }
}

/* Small Mobile Phones */
@media (max-width: 480px) {
  .footer-content {
    @apply px-2 py-8;
  }

  .footer-top {
    @apply gap-5 mb-8;
  }

  .footer-logo {
    @apply h-10;
  }

  .footer-tagline {
    @apply text-lg;
  }

  .footer-brand-text {
    @apply text-xs;
  }

  .footer-nav-title {
    @apply text-base;
  }

  .footer-nav-item {
    @apply text-sm;
  }

  .social-icon {
    @apply w-10 h-10 space-x-4;
  }

  .social-icon svg {
    @apply w-5 h-5;
  }

  .newsletter-title {
    @apply text-base;
  }

  .newsletter-description {
    @apply text-xs;
  }

  .newsletter-input {
    @apply py-2.5 px-3 text-sm;
  }

  .newsletter-button {
    @apply py-2.5 px-3;
  }

  .newsletter-button svg {
    @apply w-4 h-4;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Disable complex hover effects on touch devices */
  .footer-brand::before,
  .footer-nav-section::before,
  .footer-newsletter::before,
  .footer-legal-links::before {
    display: none;
  }

  .social-icon::before,
  .footer-nav-item::after,
  .newsletter-input::before,
  .newsletter-button::before {
    display: none;
  }

  /* Simplify animations for better performance */
  .footer-particles,
  .footer-content::before,
  .footer-content::after {
    display: none;
  }

  /* Enhanced touch targets */
  .social-icon {
    @apply w-12 h-12 min-w-[48px] min-h-[48px];
  }

  .footer-nav-item {
    @apply py-3 min-h-[44px] flex items-center justify-center;
  }

  .newsletter-input {
    @apply min-h-[44px];
  }

  .newsletter-button {
    @apply min-h-[44px] min-w-[44px];
  }

  .footer-legal-link {
    @apply py-2 min-h-[40px] flex items-center justify-center;
  }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  .footer-animate,
  .footer-particles,
  .footer-content::before,
  .footer-content::after {
    animation: none;
  }

  .footer-logo,
  .footer-tagline,
  .newsletter-title,
  .footer-nav-title {
    animation: none;
  }

  .social-icon:hover,
  .newsletter-button:hover {
    animation: none;
  }

  .footer-bottom::before {
    animation: none;
  }

  .newsletter-title::after {
    animation: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .modern-footer {
    background: #000000;
    border-top: 2px solid #ffffff;
  }

  .footer-nav-item,
  .footer-legal-link,
  .footer-copyright {
    color: #ffffff;
  }

  .social-icon {
    border: 2px solid #ffffff;
    background: transparent;
  }

  .newsletter-input {
    border: 2px solid #ffffff;
    background: transparent;
    color: #ffffff;
  }

  .newsletter-button {
    border: 2px solid #ffffff;
    background: #ffffff;
    color: #000000;
  }
}

/* Animation for footer reveal */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.footer-animate {
  animation: fadeInUp 0.8s ease-out;
}

/* Advanced Floating particles effect */
.footer-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.footer-particles::before,
.footer-particles::after {
  content: "";
  position: absolute;
  width: 6px;
  height: 6px;
  background: linear-gradient(
    45deg,
    rgba(59, 130, 246, 0.4),
    rgba(147, 197, 253, 0.6)
  );
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  animation: advancedFloat 8s ease-in-out infinite;
}

.footer-particles::before {
  top: 15%;
  left: 15%;
  animation-delay: 0s;
}

.footer-particles::after {
  top: 70%;
  right: 25%;
  animation-delay: 4s;
}

/* Additional floating elements */
.footer-content::before {
  content: "";
  position: absolute;
  top: 40%;
  left: 60%;
  width: 3px;
  height: 3px;
  background: rgba(147, 197, 253, 0.5);
  border-radius: 50%;
  animation: advancedFloat 10s ease-in-out infinite;
  animation-delay: 2s;
}

.footer-content::after {
  content: "";
  position: absolute;
  top: 25%;
  right: 15%;
  width: 5px;
  height: 5px;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.3),
    rgba(99, 102, 241, 0.4)
  );
  border-radius: 50%;
  animation: advancedFloat 12s ease-in-out infinite;
  animation-delay: 6s;
}

@keyframes advancedFloat {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-15px) translateX(10px) rotate(90deg) scale(1.2);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-25px) translateX(-5px) rotate(180deg) scale(0.8);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-10px) translateX(-15px) rotate(270deg) scale(1.1);
    opacity: 0.5;
  }
}
