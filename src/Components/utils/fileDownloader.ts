import fileDownload from "js-file-download";
import axios from "axios";
export const downloadFile = async (url: string) => {
  const filename = url.substring(url.lastIndexOf("-") + 1);
  await axios
    .get(url, {
      responseType: "blob",
    })
    .then((res) => {
      // console.log(url);
      fileDownload(res.data, filename);
    });
};

export const showPdf = (url: string) => {
  window.open(url, "_blank");
  // document.getElementById("main-iframe")?.setAttribute("src", url);
};
