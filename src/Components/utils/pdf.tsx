import { useState } from 'react';
import { Document, Page } from 'react-pdf/dist/esm/entry.webpack';

const Pdf = ({ pdf }: { pdf: string }) => {
    const [numPages, setNumPages] = useState(0);
    const [pageNumber, setPageNumber] = useState(1);

    const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
        setNumPages(numPages);
    };

    const goToPrevPage = () =>
        setPageNumber(pageNumber - 1 <= 1 ? 1 : pageNumber - 1);

    const goToNextPage = () =>
        setPageNumber(
            pageNumber + 1 >= numPages ? numPages : pageNumber + 1,
        );

    return (
        <div>
            <nav>
                <button onClick={goToPrevPage}>Prev</button>
                <button onClick={goToNextPage}>Next</button>
                <p>
                    Page {pageNumber} of {numPages}
                </p>
            </nav>

            <Document
                file={pdf}
                className="PdfDiv"
                onLoadSuccess={onDocumentLoadSuccess}

            >
                <Page height={document.getElementsByClassName('PdfDiv')[0]?.clientHeight * 5 ?? 150} pageNumber={pageNumber} />
            </Document>
        </div>
    );
};


export default Pdf;