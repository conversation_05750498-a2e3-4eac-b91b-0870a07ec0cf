/* Modern News Styles */

/* Enhanced <PERSON><PERSON> Styles */
.button {
  @apply font-medium rounded-lg text-sm px-5 py-2.5 mx-1 mb-2 focus:outline-none transition-all duration-200 transform hover:scale-105;
}

.active {
  @apply bg-GTI-BLUE-default text-white hover:bg-blue-800 shadow-lg hover:shadow-xl;
}

.not-active {
  @apply bg-white text-GTI-BLUE-default border-2 border-slate-200 hover:border-GTI-BLUE-default hover:shadow-md;
}

/* Modern Card Animations */
.news-card {
  @apply transition-all duration-300 ease-in-out;
}

.news-card:hover {
  @apply transform -translate-y-2 shadow-2xl;
}

/* Enhanced Tag Styles */
.news-tag {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-GTI-BLUE-default/10 text-GTI-BLUE-default hover:bg-GTI-BLUE-default hover:text-white transition-all duration-200;
}

.partner-tag {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-200;
}

/* Modern Content Styles */
.news-content {
  @apply max-w-none text-gray-700 leading-relaxed;
  line-height: 1.7;
}

.news-content h1,
.news-content h2,
.news-content h3 {
  @apply text-gray-900 font-bold mb-4 mt-6;
}

.news-content h1 {
  @apply text-2xl;
}

.news-content h2 {
  @apply text-xl;
}

.news-content h3 {
  @apply text-lg;
}

.news-content p {
  @apply text-gray-600 leading-relaxed mb-4;
}

.news-content a {
  @apply text-GTI-BLUE-default hover:text-blue-800 transition-colors duration-200 underline;
}

.news-content ul,
.news-content ol {
  @apply mb-4 pl-6;
}

.news-content li {
  @apply mb-2;
}

/* Enhanced Image Styles */
.news-image {
  @apply rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300;
}

/* Loading States */
.loading-shimmer {
  @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%];
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design Enhancements */
@media (max-width: 640px) {
  .news-content {
    @apply text-sm;
  }

  .news-tag,
  .partner-tag {
    @apply text-xs px-2 py-1;
  }
}
