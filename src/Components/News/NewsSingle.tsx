import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import axios from "axios";
import { Helmet } from "react-helmet";

import { newsFetched } from "../constants";
import articlebanner from "../../assests/banners/articlebanner.png";

const NewsSingle = ({ handleLoginModal }: { handleLoginModal: () => void }) => {
  let [news, setNews] = useState<newsFetched>({
    _id: "",
    title: "",
    link: "",
    shortDescription: "",
    description: "",
    imageUrl: "",
    author: "",
    metaDescription: "",
    enclosure: "",
    media: "",
    mediaCaptionUrl: "",
    mediaCaptionText: "",
    tags: [],
    partnersite: [],
    displayOnHomePage: true,
    isDeleted: false,
    createdAt: "",
    updatedAt: "",
  });

  const { id } = useParams();

  const loadNews = (id: string) => {
    var config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/news/${id}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        setNews(response.data);
      })
      .catch(function (error) {});
  };

  useEffect(() => {
    loadNews(id ?? "");
  }, [id]);
  const DOC = new Date(news.createdAt);

  return (
    <React.Fragment>
      <div
        className="flex flex-col w-full items-center bg-slate-100"
        style={{ msOverflowStyle: "none", scrollbarWidth: "none" }}
      >
        <Helmet>
          <title>{news.title}</title>
          <meta
            name="description"
            key="description"
            content={news.shortDescription}
          />
          <meta name="title" key="title" content={news.title} />
          <meta property="og:title" content={news.title} />
          <meta property="og:description" content={`${news.description}`} />
          <meta property="og:image" content={news.imageUrl} />
          <meta
            property="og:url"
            content={`${process.env.REACT_APP_BASE_URL}/news/${news._id}`}
          />
          <meta property="og:type" content="website" />
          <meta name="twitter:title" content={news.title} />
          <meta name="twitter:description" content={news.description} />
          <meta name="twitter:image" content={news.imageUrl} />
          <meta name="twitter:card" content={news.title} />
        </Helmet>
        <article className="max-w-4xl mx-auto bg-white rounded-3xl shadow-xl overflow-hidden mt-8 mb-12">
          {/* Hero Image */}
          {news.imageUrl && (
            <div className="relative h-64 md:h-96 overflow-hidden">
              <img
                src={news.imageUrl}
                className="w-full h-full object-cover"
                alt={news.title}
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
              {news.mediaCaptionText && (
                <div className="absolute bottom-4 left-4 right-4">
                  <p className="text-white text-sm bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2">
                    {news.mediaCaptionText}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Content */}
          <div className="p-8 md:p-12">
            {/* Header */}
            <header className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <div className="bg-GTI-BLUE-default/10 rounded-lg px-3 py-1">
                    <span className="text-GTI-BLUE-default text-sm font-medium">
                      News
                    </span>
                  </div>
                </div>
                <time className="text-gray-500 text-sm">
                  {DOC.toLocaleString("default", {
                    month: "long",
                    day: "2-digit",
                    year: "numeric",
                  })}
                </time>
              </div>

              <h1 className="text-3xl md:text-5xl font-bold text-gray-900 leading-tight mb-4">
                {news.title}
              </h1>

              {/* Short Description */}
              {news.shortDescription && (
                <p className="text-xl text-gray-600 leading-relaxed mb-6">
                  {news.shortDescription}
                </p>
              )}

              {/* Tags and Partner Sites */}
              <div className="flex flex-wrap gap-3 mb-6">
                {news?.partnersite?.map((partner, index) => (
                  <span key={index} className="partner-tag">
                    {partner}
                  </span>
                ))}
                {news?.tags?.map((tag, index) => (
                  <span key={index} className="news-tag">
                    {tag}
                  </span>
                ))}
              </div>
            </header>

            {/* Content Body */}
            <div
              className="news-content"
              dangerouslySetInnerHTML={{ __html: news.description }}
            ></div>
          </div>
        </article>
      </div>
    </React.Fragment>
  );
};

export default NewsSingle;
