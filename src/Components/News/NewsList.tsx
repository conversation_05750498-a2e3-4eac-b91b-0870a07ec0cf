import React, { Dispatch, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { NewspaperIcon } from "@heroicons/react/24/outline";
import { newsFetched } from "../constants";
import { useNavigate } from "react-router-dom";
import { ScreenSpinner } from "../utils/loader";
import { getQueryParams } from "../../utils";
import { getNews } from "../../store/actioncreators/newsAction";

const Card = ({ item }: { item: newsFetched }) => {
  const DOC = new Date(item.createdAt);
  const navigate = useNavigate();
  const handleView = () => {
    navigate(`/news/${item._id}`, { state: { id: item._id } });
  };

  return (
    <article
      className="group relative bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-xl hover:shadow-GTI-BLUE-default/10 transition-all duration-300 cursor-pointer transform hover:-translate-y-1 h-full"
      onClick={handleView}
    >
      {/* Image Container */}
      <div className="relative aspect-[16/10] overflow-hidden bg-gray-100">
        {item.imageUrl ? (
          <>
            <img
              src={item?.imageUrl ?? ""}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              alt={item.title}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </>
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-GTI-BLUE-default/10 to-blue-100 flex items-center justify-center">
            <NewspaperIcon className="h-16 w-16 text-GTI-BLUE-default/40" />
          </div>
        )}

        {/* Date Badge */}
        <div className="absolute top-4 left-4">
          <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1 shadow-sm">
            <time className="text-xs font-medium text-GTI-BLUE-default">
              {DOC.toLocaleString("default", {
                month: "short",
                day: "2-digit",
                year: "numeric",
              })}
            </time>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-GTI-BLUE-default transition-colors duration-200">
          {item.title}
        </h3>
        <div
          className="text-gray-600 text-sm line-clamp-3 leading-relaxed"
          dangerouslySetInnerHTML={{
            __html: item.shortDescription?.split("<br>")?.join(""),
          }}
        ></div>

        {/* Read More Indicator */}
        <div className="mt-4 flex items-center text-GTI-BLUE-default text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <span>Read Article</span>
          <svg
            className="ml-1 w-4 h-4 transition-transform group-hover:translate-x-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </div>
      </div>
    </article>
  );
};

const NewsList = () => {
  const dispatch: Dispatch<any> = useDispatch();
  const { news, newsCount }: any = useSelector((state: STATE) => state.NEWS);
  const spinner: LOADER = useSelector((state: STATE) => state.LOADER.LOADER);

  const navigate = useNavigate();
  const skip = getQueryParams("skip") ?? "0";

  const [page, setPage] = useState({
    skip: skip ? skip : "0",
    limit: "9",
  });
  const [maxSkip, setMaxSkip] = useState(0);

  useEffect(() => {
    setMaxSkip(Math.ceil(newsCount / 9));
  }, [page, newsCount]);

  useEffect(() => {
    dispatch(getNews(page.skip, page.limit));
  }, [page]);

  const fetchData = (val: number) => {
    let newSkip = parseInt(page.skip) + val;
    if (newSkip >= 0) {
      setPage({
        skip: newSkip + "",
        limit: page.limit,
      });
      navigate(`/news?skip=${newSkip}`);
      window.scrollTo(0, 0);
    }
  };

  return (
    <div className="flex flex-col w-full">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <div>
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            Latest News
          </h2>
          <p className="text-gray-600">
            Stay informed with the latest technology news and industry updates
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-GTI-BLUE-default/10 text-GTI-BLUE-default">
            {newsCount} Articles
          </span>
        </div>
      </div>

      {spinner.SPINNER ? (
        <ScreenSpinner />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {news.map((item: newsFetched, id: number) => {
            return <Card item={item} key={id} />;
          })}
        </div>
      )}
      {/* Modern Pagination */}
      {newsCount > parseInt(page.limit) && (
        <div className="flex justify-center mt-12">
          <nav className="flex items-center space-x-2">
            <button
              disabled={page.skip === "0"}
              onClick={() => {
                fetchData(-1);
              }}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-GTI-BLUE-default disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-700 transition-colors duration-200"
            >
              <svg
                className="mr-2 w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              Previous
            </button>
            {/* Page Numbers */}
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, maxSkip) }, (_, i) => {
                const pageNum = parseInt(page.skip) + i + 1;
                const isCurrentPage = i === 0;

                if (pageNum <= maxSkip) {
                  return (
                    <button
                      key={pageNum}
                      onClick={() => {
                        if (!isCurrentPage) fetchData(i);
                      }}
                      className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                        isCurrentPage
                          ? "bg-GTI-BLUE-default text-white"
                          : "text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-GTI-BLUE-default"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                }
                return null;
              })}

              {maxSkip > parseInt(page.skip) + 5 && (
                <>
                  <span className="px-2 text-gray-500">...</span>
                  <button
                    onClick={() => {
                      fetchData(maxSkip - parseInt(page.skip) - 1);
                    }}
                    className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-GTI-BLUE-default transition-colors duration-200"
                  >
                    {maxSkip}
                  </button>
                </>
              )}
            </div>

            <button
              disabled={
                parseInt(page.limit) * (parseInt(page.skip) + 1) >= newsCount
              }
              onClick={() => {
                fetchData(1);
              }}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-GTI-BLUE-default disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white disabled:hover:text-gray-700 transition-colors duration-200"
            >
              Next
              <svg
                className="ml-2 w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default NewsList;
