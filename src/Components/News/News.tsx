import { Helmet } from "react-helmet";
import { NewspaperIcon } from "@heroicons/react/24/outline";

import article from "../../assests/images/articles.png";
import NewsList from "./NewsList";
import { title, metaData } from "../constants";
import "./style.css";

const News = () => {
  return (
    <div className="flex flex-col relative w-full min-h-screen bg-gray-50">
      <Helmet>
        <title>{title.NEWS}</title>
        <meta name="description" key="description" content={metaData.NEWS} />
        <meta name="title" key="title" content="News" />
        <meta property="og:title" content="News" />
        <meta property="og:description" content="News" />
        <meta property="og:image" content={article} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/news`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="News" />
        <meta name="twitter:description" content={metaData.NEWS} />
        <meta name="twitter:image" content={article} />
        <meta name="twitter:card" content="News" />
      </Helmet>

      {/* Modern Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-GTI-BLUE-default via-blue-700 to-indigo-800">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
          <div className="text-center">
            <div className="flex justify-center items-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-white/20 rounded-full blur-xl"></div>
                <div className="relative bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                  <NewspaperIcon className="h-12 w-12 md:h-16 md:w-16 text-white" />
                </div>
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight">
              News
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              Stay updated with the latest technology news, industry insights,
              and breakthrough innovations
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl px-6 py-3 border border-white/20">
                <span className="text-white/90 text-sm font-medium">
                  Latest Technology News
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
        </div>
      </div>

      {/* Content Section */}
      <div className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <NewsList />
      </div>
    </div>
  );
};

export default News;
