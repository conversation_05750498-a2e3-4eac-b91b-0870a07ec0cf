import React, { useState } from "react";
import { IoPersonCircleOutline } from "react-icons/io5";
import axios from "axios";
import { TbUserPlus } from "react-icons/tb";
import "./style.css";
import { useSelector } from "react-redux";

const Card = ({
  Img,
  title,
  org,
  email,
}: {
  Img: string;
  title: string;
  org: string;
  email: string;
}) => {
  const currentUser: USER = useSelector((state: STATE) => state.USER.USER);
  const profile = false;
  let handleReject = async () => {
    var config = {
      method: "patch",
      url: `${process.env.REACT_APP_BASE_API}/users/rejectuserprofiles?email=${email}`,
      headers: {
        Authorization: `Bearer ${currentUser.token}`,
        "Content-Type": "application/json",
      },
    };

    await axios(config)
      .then(function (response) {})
      .catch(function (error) {});
  };
  let handleApprove = async () => {
    var config = {
      method: "patch",
      url: `${process.env.REACT_APP_BASE_API}/users/acceptuserprofiles?email=${email}`,
      headers: {
        Authorization: `Bearer ${currentUser.token}`,
        "Content-Type": "application/json",
      },
    };

    await axios(config)
      .then(function (response) {})
      .catch(function (error) {});
  };
  return (
    <div className="flex duration-200 flex-col w-52 h-60 mb-5 ml-5  rounded-lg shadow-lg border-slate-200 border-2 hover:shadow-GTI-BLUE-default overflow-clip">
      <img
        src={Img}
        alt={title}
        className="h-2/5 object-contain border-b-2 border-b-slate-400"
      />
      <div className="h-full relative ">
        <div className="flex rounded-full bg-red-200 mx-5 -translate-y-10">
          {!profile ? (
            <IoPersonCircleOutline className="absolute bg-white rounded-full object-contain duration-150 w-16 h-16 text-GTI-BLUE-default " />
          ) : (
            <img src={Img} alt={title} />
          )}
        </div>
        <div className="flex flex-col py-5 px-4">
          <label className="font-roboto font-semibold ">{title}</label>
          <label className="font-roboto">{org}</label>
        </div>
        <div className="flex flex-row w-full justify-between px-2">
          <button
            type="button"
            onClick={() => {
              handleApprove();
            }}
            className="text-white font-sm font-roboto bg-GTI-BLUE-default flex items-center hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-1"
          >
            <TbUserPlus className="mr-1" />
            Initiate
          </button>
          <button
            type="button"
            onClick={() => {
              handleReject();
            }}
            className="py-2.5 px-3  font-sm font-roboto text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-red-500 focus:z-10 focus:ring-4 focus:ring-gray-200"
          >
            Reject
          </button>
        </div>
      </div>
    </div>
  );
};

export default Card;
