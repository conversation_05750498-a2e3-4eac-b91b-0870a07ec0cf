/* Modern Notification Styles */

/* Notification Dropdown Container */
.notification-dropdown-container {
  @apply absolute right-0 top-full mt-3 w-96 max-w-sm z-50 max-h-[500px] flex flex-col;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05), 0 0 20px rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideDown 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Notification Dropdown Header */
.notification-dropdown-header {
  @apply flex items-center justify-between p-6 border-b border-gray-100/50;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.08) 0%,
    rgba(147, 197, 253, 0.05) 100%
  );
  border-radius: 24px 24px 0 0;
}

.notification-dropdown-title {
  @apply text-xl font-bold text-GTI-BLUE-default font-roboto;
}

.notification-count-badge {
  @apply text-white text-xs font-bold rounded-full min-w-[24px] h-6 flex items-center justify-center px-2 ml-2;
  background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
  font-size: 11px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.notification-close-button {
  @apply p-2 rounded-xl hover:bg-white/50 transition-all duration-200 text-gray-500 hover:text-gray-700 hover:scale-110;
  backdrop-filter: blur(10px);
}

/* Notification Dropdown Content */
.notification-dropdown-content {
  @apply flex-1 overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb transparent;
}

.notification-dropdown-content::-webkit-scrollbar {
  width: 4px;
}

.notification-dropdown-content::-webkit-scrollbar-track {
  background: transparent;
}

.notification-dropdown-content::-webkit-scrollbar-thumb {
  background-color: #e5e7eb;
  border-radius: 2px;
}

.notification-dropdown-content::-webkit-scrollbar-thumb:hover {
  background-color: #d1d5db;
}

/* Notification List */
.notification-list {
  @apply divide-y divide-gray-50;
}

/* Individual Notification Card in Dropdown */
.notification-card-dropdown {
  @apply relative transition-all duration-300 cursor-pointer border-l-4 border-transparent hover:border-GTI-BLUE-default;
  background: linear-gradient(
    145deg,
    transparent 0%,
    rgba(255, 255, 255, 0.5) 100%
  );
}

.notification-card-dropdown:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.08) 0%,
    rgba(147, 197, 253, 0.05) 50%,
    rgba(255, 255, 255, 0.8) 100%
  );
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.notification-avatar {
  @apply relative flex-shrink-0;
}

.notification-title {
  @apply text-sm font-medium text-gray-900 font-roboto line-clamp-2 mb-1;
}

.notification-description {
  @apply text-xs text-gray-600 font-roboto line-clamp-2 mb-1;
}

.notification-time {
  @apply text-xs text-gray-400 font-roboto;
}

/* More Indicator */
.notification-more-indicator {
  @apply text-center py-3 text-sm text-GTI-BLUE-default font-medium bg-GTI-BLUE-default/5 border-t border-gray-50;
}

/* Empty State */
.notification-empty-state {
  @apply flex flex-col items-center justify-center py-8 px-4 text-center;
}

.notification-empty-icon {
  @apply mb-3 p-3 bg-gray-50 rounded-full;
}

.notification-empty-text {
  @apply text-sm font-medium text-gray-900 mb-1 font-roboto;
}

.notification-empty-subtext {
  @apply text-xs text-gray-500 font-roboto;
}

/* Notification Dropdown Footer */
.notification-dropdown-footer {
  @apply p-6 border-t border-gray-100/50;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(147, 197, 253, 0.03) 100%
  );
  border-radius: 0 0 24px 24px;
}

.notification-view-all-button {
  @apply w-full py-4 px-6 text-white text-sm font-bold rounded-xl transition-all duration-300 font-roboto;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.notification-view-all-button:hover {
  @apply shadow-xl transform scale-105;
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

/* Responsive Design */
@media (max-width: 640px) {
  .notification-dropdown-container {
    @apply w-72 right-4;
  }
}

/* Main Notifications Page Styles */

/* Page Container */
.notifications-page-container {
  @apply min-h-screen py-8 px-4 w-full;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  position: relative;
  overflow-x: hidden;
}

.notifications-page-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300px;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(147, 197, 253, 0.1) 100%
  );
  z-index: 0;
}

.notifications-page-content {
  @apply max-w-6xl mx-auto w-full relative z-10;
}

/* Page Header */
.notifications-page-header {
  @apply text-center mb-16 relative;
}

.notifications-page-header::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

.notifications-page-title {
  @apply text-4xl md:text-6xl font-bold mb-6 font-roboto relative;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #6366f1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(59, 130, 246, 0.1);
  position: relative;
}

.notifications-page-title::before {
  content: "🔔";
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2rem;
  opacity: 0.8;
}

.notifications-page-subtitle {
  @apply text-xl text-gray-600 font-roboto max-w-3xl mx-auto leading-relaxed;
  font-weight: 400;
}

/* Notification Cards for Main Page */
.notification-card-main {
  @apply bg-white rounded-3xl shadow-lg hover:shadow-2xl border border-gray-100 transition-all duration-500 mb-8 overflow-hidden relative;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.notification-card-main::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-card-main:hover::before {
  opacity: 1;
}

.notification-card-main:hover {
  @apply transform -translate-y-2 scale-[1.02];
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(59, 130, 246, 0.1), 0 0 20px rgba(59, 130, 246, 0.1);
}

.notification-card-content {
  @apply p-8 flex items-start space-x-6 relative;
}

.notification-card-avatar {
  @apply relative flex-shrink-0;
}

.notification-card-body {
  @apply flex-1 min-w-0;
}

.notification-card-title {
  @apply text-xl font-bold text-gray-900 mb-3 font-roboto group-hover:text-GTI-BLUE-default transition-colors duration-300;
  line-height: 1.3;
}

.notification-card-description {
  @apply text-gray-600 mb-4 font-roboto leading-relaxed text-base;
  line-height: 1.6;
}

.notification-card-meta {
  @apply flex items-center justify-between text-sm text-gray-500 pt-2 border-t border-gray-100;
}

.notification-card-date {
  @apply flex items-center space-x-2 font-roboto font-medium;
}

.notification-card-status {
  @apply px-4 py-2 rounded-full text-xs font-bold uppercase tracking-wide transition-all duration-200;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notification-status-unread {
  @apply bg-gradient-to-r from-red-500 to-pink-500 text-white;
  animation: pulse 2s infinite;
}

.notification-status-read {
  @apply bg-gradient-to-r from-gray-400 to-gray-500 text-white;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Link Styling */
.notification-card-link {
  @apply block transition-all duration-200;
}

.notification-card-link:hover .notification-card-main {
  @apply border-GTI-BLUE-default/30;
}

/* Empty State for Main Page */
.notifications-empty-main {
  @apply text-center py-20 px-8;
}

.notifications-empty-icon-main {
  @apply mx-auto mb-8 p-8 rounded-full w-32 h-32 flex items-center justify-center relative;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.notifications-empty-icon-main::before {
  content: "";
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: inherit;
  z-index: -1;
  opacity: 0.1;
}

.notifications-empty-title-main {
  @apply text-3xl font-bold text-gray-900 mb-4 font-roboto;
  background: linear-gradient(135deg, #374151 0%, #6b7280 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.notifications-empty-text-main {
  @apply text-lg text-gray-600 font-roboto max-w-lg mx-auto leading-relaxed;
}

/* Pagination Styles */
.notifications-pagination {
  @apply flex items-center justify-center space-x-4 mt-16 mb-8;
}

.pagination-button {
  @apply inline-flex items-center px-6 py-3 text-sm font-bold rounded-xl border-2 transition-all duration-300 font-roboto shadow-lg;
  min-width: 120px;
  justify-content: center;
}

.pagination-button-primary {
  @apply text-GTI-BLUE-default bg-white border-GTI-BLUE-default hover:bg-GTI-BLUE-default hover:text-white hover:shadow-xl hover:scale-105 disabled:text-gray-400 disabled:bg-gray-100 disabled:border-gray-300 disabled:hover:bg-gray-100 disabled:hover:text-gray-400 disabled:hover:border-gray-300 disabled:hover:scale-100;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

.pagination-button-current {
  @apply text-white border-GTI-BLUE-default shadow-xl;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.pagination-button:disabled {
  @apply cursor-not-allowed opacity-50;
  transform: none !important;
}

.pagination-button:not(:disabled):hover {
  transform: translateY(-2px);
}

/* Loading State */
.notifications-loading {
  @apply flex items-center justify-center py-16;
}

.notifications-loading-spinner {
  @apply animate-spin rounded-full h-12 w-12 border-b-2 border-GTI-BLUE-default;
}

/* Responsive Design for Main Page */
@media (max-width: 768px) {
  .notifications-page-title {
    @apply text-3xl;
  }

  .notification-card-content {
    @apply p-4 flex-col space-x-0 space-y-3;
  }

  .notification-card-meta {
    @apply flex-col items-start space-y-2;
  }

  .notifications-pagination {
    @apply flex-wrap gap-2;
  }
}
