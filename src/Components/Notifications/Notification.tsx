import React, { Dispatch, useEffect } from "react";
import "./styles.css";
import { BsBell, BsX } from "react-icons/bs";
import { LIMIT, notificationItem, NOTIFICATIONS, SKIP } from "../constants";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { getAllNotification } from "../../store/actioncreators/notificationactions";
import { useNavigate } from "react-router-dom";

const Card = ({ item }: { item: notificationItem }) => {
  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const notificationDate = new Date(dateString);
    const diffInMinutes = Math.floor(
      (now.getTime() - notificationDate.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    if (diffInMinutes < 10080)
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    return notificationDate.toLocaleDateString();
  };

  return (
    <div className="notification-card-dropdown group">
      <div className="flex items-start space-x-3 p-4">
        <div className="notification-avatar">
          {item.image ? (
            <img
              src={item.image}
              className="w-10 h-10 rounded-full object-cover border-2 border-white shadow-sm"
              alt="Notification"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-GTI-BLUE-default to-blue-600 flex items-center justify-center">
              <BsBell className="w-5 h-5 text-white" />
            </div>
          )}
          {!item.isViewed && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white"></div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <p className="notification-title">
            {item.title || "New notification"}
          </p>
          {item.description && (
            <p className="notification-description">
              {item.description.length > 60
                ? `${item.description.substring(0, 60)}...`
                : item.description}
            </p>
          )}
          <p className="notification-time">{formatTimeAgo(item.createdAt)}</p>
        </div>
      </div>
    </div>
  );
};

const Notification = ({
  handleNotificationModal,
}: {
  handleNotificationModal: () => void;
}) => {
  const dispatch: Dispatch<any> = useDispatch();
  const notifications: NOTIFICATION = useSelector(
    (state: STATE) => state.NOTIFICATION.NOTIFICATION_LIST
  );
  const navigate = useNavigate();
  const handleView = () => {
    navigate(NOTIFICATIONS);
    handleNotificationModal();
  };

  useEffect(() => {
    dispatch(getAllNotification(SKIP, LIMIT));
  }, []);

  const hasNotifications = notifications.NOTIFICATION_NOT_VIEWED.length > 0;

  return (
    <div className="notification-dropdown-container">
      {/* Header */}
      <div className="notification-dropdown-header">
        <div className="flex items-center space-x-2">
          <BsBell className="w-5 h-5 text-GTI-BLUE-default" />
          <h4 className="notification-dropdown-title">Notifications</h4>
          {hasNotifications && (
            <span className="notification-count-badge">
              {notifications.NOTIFICATION_NOT_VIEWED.length}
            </span>
          )}
        </div>
        <button
          onClick={handleNotificationModal}
          className="notification-close-button"
          aria-label="Close notifications"
        >
          <BsX className="w-5 h-5" />
        </button>
      </div>

      {/* Content */}
      <div className="notification-dropdown-content">
        {hasNotifications ? (
          <div className="notification-list">
            {notifications.NOTIFICATION_NOT_VIEWED.slice(0, 5).map(
              (item: notificationItem, id: number) => (
                <Card item={item} key={item._id || id} />
              )
            )}
            {notifications.NOTIFICATION_NOT_VIEWED.length > 5 && (
              <div className="notification-more-indicator">
                +{notifications.NOTIFICATION_NOT_VIEWED.length - 5} more
                notifications
              </div>
            )}
          </div>
        ) : (
          <div className="notification-empty-state">
            <div className="notification-empty-icon">
              <BsBell className="w-8 h-8 text-gray-300" />
            </div>
            <p className="notification-empty-text">No new notifications</p>
            <p className="notification-empty-subtext">
              We'll notify you when something arrives!
            </p>
          </div>
        )}
      </div>

      {/* Footer */}
      {hasNotifications && (
        <div className="notification-dropdown-footer">
          <button onClick={handleView} className="notification-view-all-button">
            View All Notifications
          </button>
        </div>
      )}
    </div>
  );
};

export default Notification;
