import React, { Dispatch, useEffect, useState } from "react";
import { SlCalender } from "react-icons/sl";
import { useDispatch, useSelector } from "react-redux";
import { BsBell, BsChevronLeft, BsChevronRight } from "react-icons/bs";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet";

import { notificationItem, title, metaData } from "../constants";
import { getAllNotification } from "../../store/actioncreators/notificationactions";
import { markNotification } from "../../api/user";
import { getQueryParams } from "../../utils";
import globe from "../../assests/home/<USER>";
import "./styles.css";

const Card = ({ item }: { item: notificationItem }) => {
  const DOC = new Date(item.createdAt);

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const notificationDate = new Date(dateString);
    const diffInMinutes = Math.floor(
      (now.getTime() - notificationDate.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    if (diffInMinutes < 10080)
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    return notificationDate.toLocaleDateString();
  };

  return (
    <a
      href={item.link}
      className="notification-card-link"
      target={item.link?.startsWith("http") ? "_blank" : "_self"}
      rel={item.link?.startsWith("http") ? "noopener noreferrer" : undefined}
    >
      <div className="notification-card-main group">
        <div className="notification-card-content">
          <div className="notification-card-avatar">
            {item.image ? (
              <img
                src={item.image}
                className="w-16 h-16 rounded-full object-cover border-4 border-white shadow-xl ring-2 ring-GTI-BLUE-default/20"
                alt="Notification"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-GTI-BLUE-default via-blue-600 to-indigo-600 flex items-center justify-center shadow-xl ring-2 ring-GTI-BLUE-default/20">
                <BsBell className="w-8 h-8 text-white" />
              </div>
            )}
            {!item.isViewed && (
              <div className="absolute -top-2 -right-2 w-5 h-5 bg-gradient-to-r from-red-500 to-pink-500 rounded-full border-3 border-white shadow-lg animate-pulse">
                <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-ping opacity-75"></div>
              </div>
            )}
          </div>

          <div className="notification-card-body">
            <h3 className="notification-card-title">
              {item.title || "New notification"}
            </h3>

            {item.description && (
              <p className="notification-card-description">
                {item.description}
              </p>
            )}

            <div className="notification-card-meta">
              <div className="notification-card-date">
                <SlCalender className="w-4 h-4" />
                <span>
                  {DOC.toLocaleDateString("default", {
                    month: "short",
                    day: "numeric",
                    year: "numeric",
                  })}
                </span>
                <span>•</span>
                <span>{formatTimeAgo(item.createdAt)}</span>
              </div>

              <span
                className={`notification-card-status ${
                  item.isViewed
                    ? "notification-status-read"
                    : "notification-status-unread"
                }`}
              >
                {item.isViewed ? "Read" : "New"}
              </span>
            </div>
          </div>
        </div>
      </div>
    </a>
  );
};

const NotificationView = () => {
  const dispatch: Dispatch<any> = useDispatch();
  const notifications: NOTIFICATION = useSelector(
    (state: STATE) => state.NOTIFICATION.NOTIFICATION_LIST
  );

  const navigate = useNavigate();
  const skip = getQueryParams("skip") ?? "0";
  const [page, setPage] = useState({
    skip: "0",
    limit: "8",
  });

  const fetchData = (val: number) => {
    let newSkip = parseInt(page.skip) + val;
    if (newSkip >= 0) {
      navigate(`/notifications?skip=${newSkip}`);
      setPage({
        skip: newSkip.toString(),
        limit: page.limit,
      });
      dispatch(getAllNotification(skip ? skip : "0", "12"));
    }
  };

  useEffect(() => {
    dispatch(getAllNotification(skip ? skip : "0", "12"));
    markNotification();
    window.scrollTo(0, 0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [skip]);

  const hasNotifications = notifications.NOTIFICATION.length > 0;

  return (
    <div className="notifications-page-container">
      <Helmet>
        <title>{title.NOTIFICATIONS}</title>
        <meta
          name="description"
          key="description"
          content={metaData.NOTIFICATIONS}
        />
        <meta name="title" key="title" content="Notifications" />
        <meta property="og:title" content="Notifications" />
        <meta property="og:description" content="Notifications" />
        <meta property="og:image" content={globe} />
        <meta
          property="og:url"
          content={`${process.env.REACT_APP_BASE_URL}/notifications`}
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:title" content="Notifications" />
        <meta name="twitter:description" content={metaData.NOTIFICATIONS} />
        <meta name="twitter:image" content={globe} />
        <meta name="twitter:card" content="Notifications" />
      </Helmet>

      <div className="notifications-page-content">
        {/* Header */}
        <div className="notifications-page-header">
          <h1 className="notifications-page-title">Notifications</h1>
          <p className="notifications-page-subtitle">
            Stay updated with your latest activities and important updates
          </p>
        </div>

        {/* Content */}
        {hasNotifications ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between mb-8 p-4 bg-white/50 backdrop-blur-sm rounded-2xl border border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700">
                  {notifications.NOTIFICATION.length} notification
                  {notifications.NOTIFICATION.length !== 1 ? "s" : ""} found
                </span>
              </div>
              <div className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                Updated just now
              </div>
            </div>
            {notifications.NOTIFICATION.map(
              (item: notificationItem, id: number) => (
                <Card item={item} key={item._id || id} />
              )
            )}
          </div>
        ) : (
          <div className="notifications-empty-main">
            <div className="notifications-empty-icon-main">
              <BsBell className="w-12 h-12 text-gray-400" />
            </div>
            <h2 className="notifications-empty-title-main">
              No notifications yet
            </h2>
            <p className="notifications-empty-text-main">
              When you have new notifications, they'll appear here. We'll keep
              you updated on important activities and updates.
            </p>
          </div>
        )}

        {/* Pagination */}
        {hasNotifications && (
          <div className="notifications-pagination">
            <button
              disabled={page.skip === "0"}
              onClick={() => fetchData(-1)}
              className="pagination-button pagination-button-primary"
            >
              <BsChevronLeft className="w-4 h-4 mr-1" />
              Previous
            </button>

            <button
              disabled
              className="pagination-button pagination-button-current"
            >
              Page {parseInt(page.skip) + 1}
            </button>

            <button
              disabled={
                (parseInt(page.skip) + 1) * parseInt(page.limit) >=
                notifications?.COUNT
              }
              onClick={() => fetchData(1)}
              className="pagination-button pagination-button-primary"
            >
              Next
              <BsChevronRight className="w-4 h-4 ml-1" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationView;
