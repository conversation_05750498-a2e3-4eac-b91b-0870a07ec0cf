export interface empItem {
  id: number;
  title: string;
  email: string;
}
export interface empData extends Array<empItem> {}

export interface connectionItem {
  _id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  countryCode: string;
  referenceCode: string;
  isEmailVerified: Boolean;
  isUserVerified: Boolean;
  isRejected: Boolean;
  password: string;
  userRole: number;
  companyId: string;
  follower: [];
  following: [];
  connections: [
    {
      connectionStatus: string;
      userId: number;
    }
  ];
  createdAt: string;
  __v: number;
  profileImage: string;
  bannerImage: string;
}
