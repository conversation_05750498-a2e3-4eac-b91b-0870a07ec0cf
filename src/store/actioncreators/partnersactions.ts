import axios from "axios";
import { store } from "../../store";
import * as PARTNER from "../../Components/constants";
import { PARTNERS_GET } from "../actiontypes/actionTypes";

export const getPartners =
  (skip: string, limit: string) => async (dispatch: GET_PARTNERS_DISPATCH) => {
    var config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/technology-partners?skip=${skip}&limit=${limit}`,
      headers: {
        Authorization: `Bearer ${store.getState().USER.USER.token}`,
        "Content-Type": "application/json",
      },
    };

    await axios(config)
      .then(function (response) {
        const list: PARTNERS = {
          PARTNERS_LIST: response.data,
        };
        const action: PARTNERS_ACTION = {
          type: PARTNERS_GET,
          PARTNERS: list,
        };
        // console.log("Partners fetched ");
        return dispatch(action);
      })
      .catch(function (error) {
        // console.log("Partners not fetched ", error);
      });
  };
