import axios from "axios";
import { store } from "../../store";
import { BLOG_GET, BLOG_GET_ID } from "../actiontypes/actionTypes";

export const getBlogs =
  (secId: string, skip: string, limit: string) =>
  (dispatch: GET_BLOG_DISPATCH) => {
    var config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_API}/blog?skip=${skip}&limit=${limit}`,
      headers: {
        // Authorization: `Bearer ${store.getState().USER.USER.token}`,
        "Content-Type": "application/json",
      },
    };

    axios(config)
      .then(function (response) {
        const list: BLOG = {
          BLOG_LIST: response.data,
        };
        const action: BLOG_ACTION = {
          type: BLOG_GET,
          BLOG: list,
        };
        return dispatch(action);
      })
      .catch(function (error) {});
  };

export const getBlogId = (id: string) => (dispatch: GET_BLOG_ID_DISPATCH) => {
  var config = {
    method: "get",
    url: `${process.env.REACT_APP_BASE_API}/blog/${id}`,
    headers: {
      Authorization: `Bearer ${store.getState().USER.USER.token}`,
      "Content-Type": "application/json",
    },
  };

  axios(config)
    .then(function (response) {
      const list: BLOG = {
        BLOG_LIST: response.data,
      };
      const action: BLOG_ACTION = {
        type: BLOG_GET_ID,
        BLOG: list,
      };
      return dispatch(action);
    })
    .catch(function (error) {});
};
