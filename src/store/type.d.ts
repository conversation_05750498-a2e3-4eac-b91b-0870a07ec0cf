interface USER {
  id: string;
  admin: number;
  token: string;
  loader: <PERSON><PERSON><PERSON>;
  profileImage: string;
  userType: string;
  productCount: number;
  opportunityCount: number;
  subscribeEmail: boolean;
  user: {
    name: string;
    email: string;
    phone: number;
    country: string;
    ref: string;
    pwd: string;
    emailVerified: boolean;
    userVerified: boolean;
    rejected: boolean;
    profileImg: string;
    coverImg: string;
  };
  company: Array<{
    name: string;
    id: string;
    description: string;
    address: string;
    logo: string;
    website: string;
    country: string;
    companyTurnover: string;
    typeAndSizeOfPartnersRequired: string[];
    typesOfPartnershipConsidered: string[];
    companyAndSizeOfClient: string;
    transnationalCoOperation: boolean;
  }>;
}

type USER_STATE = {
  USER: USER;
};

type USER_ACTION = {
  type: string;
  USER: USER;
};
type UserDelAction = {
  type: string;
};

type DispatchType = (args: USER_ACTION) => USER_ACTION;
type DispatchType2 = (args: UserDelAction) => UserDelAction;

interface ARTICLE {
  articles: articleItemFetched[];
  articlesCount: number;
}

type ARTICLE_STATE = {
  ARTICLE: ARTICLE;
};

type ARTICLE_ACTION = {
  type: string;
  ARTICLE: ARTICLE;
};

// blogs
interface BLOG {
  BLOG_LIST: {
    blogs: any[];
    blogsCount: number;
  };
}

type BLOG_STATE = {
  BLOG: BLOG;
};

type BLOG_ACTION = {
  type: string;
  BLOG: BLOG;
};

type GET_BLOG_DISPATCH = (args: BLOG_ACTION) => BLOG_STATE;
type UPDATE_BLOG_DISPATCH = (args: BLOG_ACTION) => BLOG_STATE;
type CREATE_BLOG_DISPATCH = (args: BLOG_ACTION) => BLOG_STATE;
type GET_BLOG_ID_DISPATCH = (args: BLOG_ACTION) => BLOG_STATE;
type DELETE_BLOG_DISPATCH = (args: BLOG_ACTION) => BLOG_STATE;

// events
interface EVENT {
  EVENT_LIST: {
    events: eventItemsFetched[];
    eventsCount: number;
  };
  USER_LIST: [];
}

type EVENT_STATE = {
  EVENT: EVENT;
};

type EVENT_ACTION = {
  type: string;
  EVENT: EVENTS;
};

type GET_EVENT_DISPATCH = (args: EVENT_ACTION) => EVENT_STATE;
type GET_EVENT_USERS_DISPATCH = (args: EVENT_ACTION) => EVENT_STATE;
type UPDATE_EVENT_DISPATCH = (args: EVENT_ACTION) => EVENT_STATE;
type CREATE_EVENT_DISPATCH = (args: EVENT_ACTION) => EVENT_STATE;
type GET_EVENT_ID_DISPATCH = (args: EVENT_ACTION) => EVENT_STATE;
type REGISTER_EVENT_DISPATCH = (args: EVENT_ACTION) => EVENT_STATE;
type DELETE_EVENT_DISPATCH = (args: EVENT_ACTION) => EVENT_STATE;

// sector
interface SECTOR {
  SECTOR_LIST: sectorItem[];
}

type SECTOR_STATE = {
  SECTOR: SECTOR;
};

type SECTOR_ACTION = {
  type: string;
  SECTOR: SECTOR;
};

type GET_SECTOR_DISPATCH = (args: SECTOR_ACTION) => SECTOR_STATE;
type GET_SECTOR_BY_ID_DISPATCH = (args: SECTOR_ACTION) => SECTOR_STATE;
type CREATE_SECTOR_DISPATCH = (args: SECTOR_ACTION) => SECTOR_STATE;
// sub-sector
interface SUB_SECTOR {
  SUB_SECTOR_LIST: subsectorItem[];
}

interface PUBLICATION {
  publications: any[];
  publications: number;
}

type PUBLICATIONS_ACTION = {
  type: string;
  PUBLICATION: {
    publications: any[];
    publicationsCount: number;
  };
};

interface NEWS {
  news: any[];
  newsCount: number;
}

type NEWS_ACTION = {
  type: string;
  NEWS: {
    news: any[];
    newsCount: number;
  };
};

type SUB_SECTOR_STATE = {
  SUB_SECTOR: SUB_SECTOR;
};

type SUB_SECTOR_ACTION = {
  type: string;
  SUB_SECTOR: SUB_SECTOR;
};

type GET_SUB_SECTOR_DISPATCH = (args: SUB_SECTOR_ACTION) => SUB_SECTOR_STATE;
type GET_SUB_SECTOR_BY_ID_DISPATCH = (
  args: SUB_SECTOR_ACTION
) => SUB_SECTOR_STATE;
type CREATE_SUB_SECTOR_DISPATCH = (args: SUB_SECTOR_ACTION) => SUB_SECTOR_STATE;

type GET_ARTICLE_DISPATCH = (args: ARTICLE_ACTION) => ARTICLE_STATE;
type UPDATE_ARTICLE_DISPATCH = (args: ARTICLE_ACTION) => ARTICLE_STATE;
type CREATE_ARTICLE_DISPATCH = (args: ARTICLE_ACTION) => ARTICLE_STATE;
type GET_ARTICLE_ID_DISPATCH = (args: ARTICLE_ACTION) => ARTICLE_STATE;
type DELETE_ARTICLE_DISPATCH = (args: ARTICLE_ACTION) => ARTICLE_STATE;

interface TOAST {
  MESSAGE: string;
  STATUS: Boolean;
  TYPE: Number;
}

type TOAST_STATE = {
  TOAST: TOAST;
};

type TOAST_ACTION = {
  type: string;
};

type TOAST_DISPATCH = (args: TOAST_ACTION) => TOAST_STATE;

// product state
interface PRODUCTS {
  PRODUCTS_LIST: {
    productsCount: number;
    products: any[];
  };
  YOUR_PRODUCT_LIST: {
    products: productItemPartialFetched[];
    productsCount: number;
  };
  COMPANY_PRODUCT_LIST: [];
  COMPANY_PENDING_PRODUCT_LIST: [];
  COMPANY_APPROVED_PRODUCT_LIST: [];
  COMPANY_UNAPPROVED_PRODUCT_LIST: [];
  COMPANY_REJECTED_PRODUCT_LIST: [];
  TOTAL: number;
  TOTAL_PENDING: number;
  TOTAL_APPROVED: number;
  TOTAL_UNAPPROVED: number;
  TOTAL_REJECTED: number;
}

type PRODUCT_STATE = {
  PRODUCTS: PRODUCTS;
};

type GET_PRODUCT_ACTION = {
  type: string;
  PRODUCTS: PRODUCTS;
};

type GET_PRODUCTS_ACTION = {
  type: string;
  PRODUCTS: PRODUCTS;
};

type PRODUCT_DISPATCH = (args: GET_PRODUCTS_ACTION) => PRODUCT_STATE;
type COMPANYREJECT_PRODUCT_DISPATCH = (
  args: GET_COMPANY_REJECTEDPRODUCTS_ACTION
) => PRODUCT_STATE;

//opportunity
interface OPP {
  OPP_LIST: {
    opportunities: any[];
    opportunitiesCount: number;
  };
  YOUR_OPP_LIST: oppotunityItemPartialFetched[];
  COMPANY_OPP_LIST: [];
  COMPANY_PENDING_OPP_LIST: [];
  COMPANY_APPROVED_OPP_LIST: [];
  COMPANY_UNAPPROVED_OPP_LIST: [];
  COMPANY_REJECTED_OPP_LIST: [];
  TOTAL: number;
  TOTAL_PENDING: number;
  TOTAL_APPROVED: number;
  TOTAL_UNAPPROVED: number;
  TOTAL_REJECTED: number;
}

type OPP_STATE = {
  OPP: OPP;
};

type GET_OPP_ACTION = {
  type: string;
  OPP: OPP;
};

type OPP_DISPATCH = (args: GET_OPP_ACTION) => OPP_STATE;
type COMPANYREJECT_OPP_DISPATCH = (
  args: GET_COMPANY_REJECTEDOPP_ACTION
) => OPP_STATE;

// innovation
interface INNOVATION {
  INNOVATION_LIST: [];
  USER_LIST: [];
  INNOVATION_COUNT: 0;
}

type INNOVATION_STATE = {
  INNOVATION: INNOVATION;
};

type INNOVATION_ACTION = {
  type: string;
  INNOVATION: INNOVATION;
};

type GET_INNOVATION_DISPATCH = (args: INNOVATION_ACTION) => INNOVATION_STATE;
type GET_INNOVATION_USERS_DISPATCH = (
  args: INNOVATION_ACTION
) => INNOVATION_STATE;
type UPDATE_INNOVATION_DISPATCH = (args: INNOVATION_ACTION) => INNOVATION_STATE;
type CREATE_INNOVATION_DISPATCH = (args: INNOVATION_ACTION) => INNOVATION_STATE;
type GET_INNOVATION_ID_DISPATCH = (args: INNOVATION_ACTION) => INNOVATION_STATE;
type DELETE_INNOVATION_DISPATCH = (args: INNOVATION_ACTION) => INNOVATION_STATE;
type REGISTER_INNOVATION_DISPATCH = (
  args: INNOVATION_ACTION
) => INNOVATION_STATE;

//follow

type FOLLOW_STATE = {};

type FOLLOW_ACTION = {
  type: string;
};

type FOLLOW_DISPATCH = (args: FOLLOW_ACTION) => FOLLOW_STATE;

//connection
type CONNECTION = {
  CONNECTION_LIST: {
    connections: connectionItem[];
    connectionsCount: number;
  };
};

type CONNECTION_STATE = {
  CONNECTION: CONNECTION;
};

type CONNECTION_ACTION = {
  type: string;
  CONNECTION: CONNECTION_STATE;
};

type CONNECTION_DISPATCH = (args: CONNECTION_ACTION) => CONNECTION_STATE;

// contact
interface CONTACT {
  CONTACT_LIST: [];
}

type CONTACT_STATE = {
  CONTACT: CONTACT;
};

type CONTACT_ACTION = {
  type: string;
  CONTACT: CONTACT;
};

type CREATE_CONTACT_DISPATCH = (args: CONTACT_ACTION) => CONTACT_STATE;

//notification

type NOTIFICATION = {
  NOTIFICATION: notificationItem[];
  NOTIFICATION_NOT_VIEWED: notificationItem[];
  COUNT: number;
};

type NOTIFICATION_STATE = {
  NOTIFICATION_LIST: NOTIFICATION;
};

type NOTIFICATION_ACTION = {
  type: string;
  NOTIFICATION: NOTIFICATION_STATE;
};

type NOTIFICATION_DISPATCH = (args: NOTIFICATION_ACTION) => NOTIFICATION_STATE;

//chat
type CHAT = {
  CHAT: getchatItem[];
};

type CHAT_STATE = {
  CHAT_LIST: CHAT;
  CHAT_TOTAL: number;
};

type CHAT_ACTION = {
  type: string;
  CHAT: CHAT_STATE;
};

type CHAT_DISPATCH = (args: CHAT_ACTION) => CHAT_STATE;

// technology partners
interface PARTNERS {
  PARTNERS_LIST: techPartner[];
}

type PARTNERS_STATE = {
  PARTNERS: PARTNERS;
};

type PARTNERS_ACTION = {
  type: string;
  PARTNERS: PARTNERS;
};

type GET_PARTNERS_DISPATCH = (args: PARTNERS_ACTION) => PARTNERS_STATE;
// loader
interface LOADER {
  BACKDROP: Boolean;
  SPINNER: Boolean;
}

type LOADER_STATE = {
  LOADER: LOADER;
};

type LOADER_ACTION = {
  type: string;
  LOADER: LOADER;
};

type LOADER_DISPATCH = (args: LOADER_ACTION) => LOADER_STATE;

type PUBLICATION_STATE = {
  publications: any[];
  publicationsCount: number;
};

type NEWS_STATE = {
  news: any[];
  newsCount: number;
};

interface TECHNOLOGY_PARTNER {
  _id: string;
  name: string;
  image: string;
}

type TECHNOLOGY_PARTNER_STATE = {
  technologyPartner: TECHNOLOGY_PARTNER[];
};

type TECHNOLOGY_PARTNER_ACTION = {
  type: string;
  technologyPartner: TECHNOLOGY_PARTNER[];
};

type PROMOTIONS_STATE = {
  promotions: any[];
  homepagePromotions: any[];
  promotionsCount: number;
};

type PROMOTIONS_ACTION = {
  type: string;
  PROMOTIONS: {
    promotions: any[];
    homepagePromotions: any[];
    promotionsCount: number;
  };
};

// main state
type STATE = {
  USER: USER_STATE;
  ARTICLE: ARTICLE_STATE;
  PRODUCTS: PRODUCT_STATE;
  BLOG: BLOG_STATE;
  PUBLICATION: PUBLICATION_STATE;
  NEWS: NEWS_STATE;
  EVENT: EVENT_STATE;
  SECTOR: SECTOR_STATE;
  SUB_SECTOR: SUB_SECTOR_STATE;
  TOAST: TOAST;
  OPP: OPP_STATE;
  INNOVATION: INNOVATION_STATE;
  FOLLOW: FOLLOW_STATE;
  CONNECTION: CONNECTION_STATE;
  CHAT: CHAT_STATE;
  NOTIFICATION: NOTIFICATION_STATE;
  PARTNER: PARTNERS_STATE;
  LOADER: LOADER_STATE;
  TECHNOLOGY_PARTNER: TECHNOLOGY_PARTNER_STATE;
  PROMOTIONS: PROMOTIONS_STATE;
};
