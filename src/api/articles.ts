import { AxiosInstance } from ".";
import { STORAGE_KEY } from "../Components/constants";

export const getRelatedArticles = async (id: string) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.get(`/article?sectorId=${id}`, config);
    return response;
  } catch (err: any) {
    // console.log("GET ERR", err);
    return err?.response;
  }
};
export const getRelatedBlogs = async (id: string) => {
  try {
    const userData: string =
      localStorage.getItem(STORAGE_KEY)?.split(" ")[0] ?? "";
    const config = {
      headers: {
        Authorization: `Bearer ${userData}`,
      },
    };
    const response = await AxiosInstance.get(`/article?sectorId=${id}`, config);
    return response;
  } catch (err: any) {
    // console.log("GET ERR", err);
    return err?.response;
  }
};
