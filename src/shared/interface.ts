export interface ErrorModalInterface {
  show: Boolean;
  message: string;
}

export interface Document {
  document: File | null;
  documentType: string;
}

export interface PersonalFormValues {
  name: string;
  email: string;
  password: string;
  country: string;
  phone: string;
  ref: string;
}

export interface FormValues {
  name: string;
  description: string;
  address: string;
  website: string;
  country: string;
  turnover: string;
  logo: File | null;
  documents: Document[];
  s_documents: Document;
}

export interface FormValues2 {
  comp_id: string;
}
