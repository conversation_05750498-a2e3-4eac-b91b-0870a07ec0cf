import React, { useEffect } from "react";
import ReactDOM from "react-dom";
import "../styles/animations.css";

interface ICustomModal {
  open: boolean;
  onClose: () => void;
  children?: React.ReactElement;
}

const CustomModal: React.FC<any> = (props) => {
  const { open, onClose, children, ...restProps } = props as any;
  useEffect(() => {
    if (open) document.body.style.overflow = "hidden";
    else document.body.style.overflow = "auto";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [open]);

  const content = open ? (
    <div className="fixed inset-0 z-50 bg-black/60 backdrop-blur-md flex justify-center items-center p-4 animate-fade-in-up">
      <div className="relative w-full max-w-4xl max-h-[95vh] animate-scale-in">
        <div className="relative bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
          {/* Decorative Background Elements */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-GTI-BLUE-default/10 to-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-500/10 to-GTI-BLUE-default/10 rounded-full blur-2xl"></div>

          {/* Enhanced Close Button */}
          <button
            type="button"
            className="absolute top-4 right-4 z-10 text-gray-400 bg-white/80 backdrop-blur-sm hover:bg-red-50 hover:text-red-500 rounded-2xl text-sm p-3 transition-all duration-300 transform hover:scale-110 border border-gray-200 hover:border-red-200 group"
            data-modal-hide="popup-modal"
            onClick={onClose}
          >
            <svg
              aria-hidden="true"
              className="w-6 h-6 transition-transform duration-300 group-hover:rotate-90"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close modal</span>
          </button>

          {/* Enhanced Content Area */}
          <div
            className="relative z-10 p-8 overflow-y-auto max-h-[85vh]"
            {...restProps}
          >
            {children}
          </div>
        </div>
      </div>
      {/* </div> */}
    </div>
  ) : null;
  return ReactDOM.createPortal(content, document.body);
};

export default CustomModal;
